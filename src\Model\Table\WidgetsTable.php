<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Datasource\EntityInterface;
use App\Model\Behavior\AuditTrailBehavior;
use Cake\Http\ServerRequestFactory;

/**
 * Widgets Model
 *
 * @property \App\Model\Table\WidgetCategoryMappingsTable&\Cake\ORM\Association\HasMany $WidgetCategoryMappings
 *
 * @method \App\Model\Entity\Widget newEmptyEntity()
 * @method \App\Model\Entity\Widget newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Widget> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Widget get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Widget findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Widget patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Widget> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Widget|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Widget saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Widget>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Widget>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Widget>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Widget> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Widget>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Widget>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Widget>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Widget> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class WidgetsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    protected $language;
    protected $country;
    protected $country_id;

    public function initialize(array $config): void
    {
        parent::initialize($config);
        $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;

        $this->addBehavior('App.AuditTrail');

        $this->setTable('widgets');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->hasMany('WidgetCategoryMappings', [
            'foreignKey' => 'widget_id',
        ]);

        $this->belongsToMany('categories', [
            'joinTable' => 'widget_category_mappings',
            'foreignKey' => 'widget_id',
            'targetForeignKey' => 'category_id',
            'through' => 'WidgetCategoryMappings',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('url_key')
            ->maxLength('url_key', 255)
            ->allowEmptyString('url_key');

        $validator
            ->scalar('title')
            ->maxLength('title', 255)
            ->requirePresence('title', 'create')
            ->notEmptyString('title');

        $validator
            ->scalar('title_ar')
            ->maxLength('title_ar', 255)
            ->requirePresence('title_ar', 'create')
            ->notEmptyString('title_ar');

        $validator
            ->scalar('summary')
            ->allowEmptyString('summary');

        $validator
            ->scalar('summary_ar')
            ->allowEmptyString('summary_ar');

        $validator
            ->allowEmptyString('no_of_product');

        $validator
            ->scalar('product_preference')
            ->allowEmptyString('product_preference');

        $validator
            ->scalar('widget_type')
            ->requirePresence('widget_type', 'create')
            ->notEmptyString('widget_type');

        $validator
            ->boolean('display_in_web')
            ->allowEmptyString('display_in_web');

        $validator
            ->boolean('display_in_mobile')
            ->allowEmptyString('display_in_mobile');

        $validator
            ->scalar('web_image')
            ->maxLength('web_image', 255)
            ->allowEmptyFile('web_image');

        $validator
            ->scalar('mobile_image')
            ->maxLength('mobile_image', 255)
            ->allowEmptyFile('mobile_image');

        $validator
            ->date('start_date')
            ->allowEmptyDate('start_date');

        $validator
            ->date('end_date')
            ->allowEmptyDate('end_date');

        $validator
            ->allowEmptyString('display_order');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    public function delete(EntityInterface $widget, array $options = []): bool{
        $widget->status = 'D';
        if ($this->save($widget)) {
            return true;
        }
        return false;
    }

    // public function homeWidgets($type) {

    //     $order = ['Widgets.display_order' => 'ASC'];
    //     if($type == 'mobile'){
    //         $select_col = ['id', 'title', 'summary', 'product_preference', 'url_key', 'no_of_product'];
    //         $condition  = ['Widgets.status' => 'A', 'Widgets.start_date <=' => date('Y-m-d'),'Widgets.end_date >=' => date('Y-m-d'), 'Widgets.display_in_mobile' => 1];
    //     } else {
    //         $select_col = ['id', 'title', 'summary', 'product_preference', 'url_key', 'no_of_product'];
    //         $condition = ['Widgets.status' => 'A', 'Widgets.start_date <=' => date('Y-m-d'),'Widgets.end_date >=' => date('Y-m-d'), 'Widgets.display_in_web' => 1];
    //     }

    //     $widgets = $this->find()
    //         ->select($select_col)
    //         ->where($condition)
    //         ->order($order)
    //         ->disableHydration()
    //         ->toArray();

    //     return $widgets;
    // }

    public function homeWidgets($language = 'English')
    {
        if ($language == 'Arabic') {
            $select_col = [
                'id' => 'Widgets.id',
                'title' => 'Widgets.title_ar',
                'summary' => 'Widgets.summary_ar',
                'product_preference' => 'Widgets.product_preference',
                'widget_type' => 'Widgets.widget_type',
                'url_key' => 'Widgets.url_key',
                'no_of_product' => 'Widgets.no_of_product'
            ];
        } else {
            $select_col = [
                'id' => 'Widgets.id',
                'title' => 'Widgets.title',
                'summary' => 'Widgets.summary',
                'product_preference' => 'Widgets.product_preference',
                'widget_type' => 'Widgets.widget_type',
                'url_key' => 'Widgets.url_key',
                'no_of_product' => 'Widgets.no_of_product'
            ];
        }

        $order = ['Widgets.display_order' => 'ASC'];
        $condition = ['Widgets.status' => 'A'];

        $widgets = $this->find()
            ->select($select_col)
            ->where($condition)
            ->order($order)
            ->disableHydration()
            ->toArray();

        return $widgets;
    }

    public function getWidgetAllocatedCategoryIds($widgetId)
    {
        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');
        $select_col = [
            'category_id',
            'category_name' => $isArabic ? 'Categories.name_ar' : 'Categories.name',
            'url_key' => 'Categories.url_key'
        ];
        $categoryIds = $this->WidgetCategoryMappings->find()
            ->select($select_col)
            ->where([
                'WidgetCategoryMappings.widget_id' => $widgetId,
                'WidgetCategoryMappings.status' => 'A',
            ])
            ->contain([
                'Categories' => function ($q) {
                    return $q->where(['Categories.country_id' => $this->country_id]);
                }
            ])
            ->enableHydration(false)
            ->toArray();

        return $categoryIds;
    }

}
