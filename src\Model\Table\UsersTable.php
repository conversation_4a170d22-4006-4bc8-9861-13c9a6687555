<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\Query;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Datasource\EntityInterface;



/**
 * Users Model
 *
 * @method \App\Model\Entity\User newEmptyEntity()
 * @method \App\Model\Entity\User newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\User> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\User get($primaryKey, $options = [])
 * @method \App\Model\Entity\User findOrCreate($search, callable $callback = null, $options = [])
 * @method \App\Model\Entity\User patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\User[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\User|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\User saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\User[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\User[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\User[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\User[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 */
class UsersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('users');
        $this->setDisplayField('email'); // Set email as the display field
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Roles', [
            'foreignKey' => 'role_id',
            'joinType' => 'INNER'
        ]);

        // $this->hasMany('ShowroomsManager', [
        //     'className' => 'Showrooms',
        //     'foreignKey' => 'showroom_manager',
        // ]);

        $this->hasOne('Showrooms', [
            'className' => 'Showrooms',
            'foreignKey' => 'showroom_manager',
        ]);


        $this->hasMany('ShowroomSupervisor', [
            'className' => 'Showrooms',
            'foreignKey' => 'showroom_supervisor',
        ]);

        $this->hasMany('SalesPersons', [
            'className' => 'Orders',
            'foreignKey' => 'sales_person_id',
        ]);

        $this->hasOne('Customers', [
            'foreignKey' => 'user_id',
            'joinType' => 'INNER',
        ]);

        $this->hasOne('Drivers', [
            'foreignKey' => 'user_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param Validator $validator Validator instance.
     * @return Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('first_name')
            ->maxLength('first_name', 255)
            // ->requirePresence('first_name', 'create')
            // ->notEmptyString('first_name', 'First name is required');
            ->allowEmptyString('first_name');

        $validator
            ->scalar('last_name')
            ->maxLength('last_name', 255)
            // ->requirePresence('last_name', 'create')
            // ->notEmptyString('last_name', 'Last name is required');
            ->allowEmptyString('last_name');

        $validator
            ->email('email')
            ->allowEmptyString('email', null)
            ->add('email', 'unique', [
                'rule' => function ($value, $context) {
                    if (!empty($value)) {
                        // Access the table instance directly using the 'table' key in the context
                        $table = $context['providers']['table'];

                        // Check for existing records with 'A' status and the same email
                        $query = $table->find()
                            ->where([
                                'email' => $value,
                                'status' => 'A',
                            ]);

                        // Exclude the current record (if editing)
                        if (!empty($context['data']['id'])) {
                            $query->where(['id !=' => $context['data']['id']]);
                        }

                        // Use count() to check if any matching records exist
                        return $query->count() === 0; // Returns true if no matching records exist
                    }
                    return true; // Skip validation if the email is empty
                },
                'provider' => 'table', // Specify the table as the provider
                'message' => 'Email already exists for active records',
                'on' => function ($context) {
                    return !empty($context['data']['email']);
                }
            ]);


        $validator
            ->scalar('password')
            ->maxLength('password', 255);
        // ->requirePresence('password', 'create')
        // ->notEmptyString('password', 'Password is required');

        $validator
            ->integer('role_id');
        // ->requirePresence('role_id', 'create')
        // ->notEmptyString('role_id', 'Role is required');

        $validator
            ->scalar('mobile_no')
            ->maxLength('mobile_no', 20)
            ->allowEmptyString('mobile_no');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->scalar('department')
            ->maxLength('department', 150)
            ->allowEmptyString('department');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param RulesChecker $rules The rules object to be modified.
     * @return RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        //Siddharth commented this as edit user does not work.
        
        /*$rules->add(function ($entity, $options) {
            if (!empty($entity->email)) {
                return $this->isUnique(['email']);
            }
            return true;
        }, 'isUnique', [
            'errorField' => 'email',
            'message' => 'This email address is already taken'
        ]);*/

        /*$rules->add(function ($entity, $options) {
            // Check if the email field is not empty or null
            if (empty($entity->email)) {
                return true; // Skip validation
            }

            // Check for uniqueness where status is 'A'
            $exists = $this->find()
                ->where(['email' => $entity->email, 'status' => 'A'])
                ->count();

            return $exists === 0;
        }, ['errorField' => 'email', 'message' => 'This email address is already taken.']);*/

        return $rules;
    }

    //M
    public function findAuth(Query $query, array $options)
    {
        return $query->select(['Users.id', 'Users.user_type', 'Users.first_name', 'Users.last_name', 'Users.country_code', 'Users.mobile_no', 'Users.last_login', 'Users.email', 'Users.token', 'Users.role_id', 'Customers.id', 'Customers.profile_photo', 'Drivers.id', 'Drivers.driver_photo', 'Roles.name'])
            ->leftJoinWith('Customers')
            ->leftJoinWith('Drivers')
            ->leftJoinWith('Roles')
            ->where(['Users.status' => 'A']);
    }

    //S add user
    public function addUser($data)
    {
        $save_user = $this->newEmptyEntity();
        $save_user->first_name = trim($data['first_name'])??'';
        $save_user->last_name =trim($data['last_name'])??'';
        $save_user->password = trim($data['password']);
        $save_user->user_type = trim($data['user_type']);
        $save_user->mobile_no = $data['mobile_no'];
        $save_user->country_code = isset($data['country_code']) && $data['country_code'] !== ''
            ? $data['country_code']
            : '';
        if ($this->save($save_user)) {
            return $save_user->id;
        } else {
            $errors = $save_user->getErrors();
            // echo "<pre>"; print_r($errors); die;
            return false;
        }
    }

    public function editUser($data)
    {
        $update = $this->find()
            ->where(['mobile_no' => $data['mobile_no'], 'status' => 'A'])
            ->order(['id' => 'DESC'])
            ->first();
        if ($update) {
            $update->email = $data['email'];
            $update->email_verify_token = $data['email_verify_token'];
            $update->is_email_verified = $data['is_email_verified'];
            if ($this->save($update)) {
                return $update->id;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    //S add user
    public function add_user($data)
    {
        $save_user = $this->newEmptyEntity();
        $save_user->first_name = trim($data['first_name']);
        $save_user->last_name = trim($data['last_name']);
        $save_user->email = trim($data['email']);
        $save_user->password = trim($data['password']);
        $save_user->country_code = $data['m_country_code'];
        $save_user->mobile_no = $data['mobile_no'];
        $save_user->user_type = $data['user_type'];
        $save_user->created_by = $data['created_by'];
        if ($this->save($save_user)) {
            return $save_user->id;
        } else {
            return false;
        }
    }

    //S check email exist
    public function checkEmailExist($email, $id = '')
    {
        if ($id) {
            $query = $this->find()
                ->where(['Users.email' => $email, 'Users.status' => 'A'])
                ->andWhere(['Users.id <>' => $id])
                ->order(['Users.id' => 'DESC']) // Specify the column to order by
                ->first();
        } else {
            $query = $this->find()
                ->where(['Users.email' => $email, 'Users.status' => 'A'])
                ->order(['Users.id' => 'DESC']) // Specify the column to order by
                ->first();
        }

        if ($query) {
            return $query['id'];
        } else {
            return false;
        }
    }

    public function checkMobileExist($mobile, $country_code, $id = '')
    {
        if ($id) {
            $query = $this->find()->where(['Users.mobile_no' => $mobile, 'Users.country_code' => $country_code, 'Users.status' => 'A'])->andWhere(['Users.id <>' => $id])->first();
        } else {
            $query = $this->find()->where(['Users.mobile_no' => $mobile, 'Users.country_code' => $country_code, 'Users.status' => 'A'])->first();
        }

        if ($query) {
            return $query['id'];
        } else {
            return false;
        }
    }

    //Z get all user data
    public function fetchShowroomManager()
    {

        $fetchShowroomManager = $this->find()
            ->select(['id', 'first_name', 'last_name']) // Select only required fields
            ->where(['Users.status' => 'A'])
            ->matching('Roles', function ($q) {
                return $q->where(['Roles.name' => 'Showroom Manager']);
            })
                ->notMatching('Showrooms', function ($q) {
                return $q->where(['Showrooms.showroom_manager IS NOT' => null]);
            });

        // $subquery = $this->Showrooms->find()
        // ->select(['showroom_manager']);

        // $fetchShowroomManager = $this->find()
        //     ->select(['Users.id', 'Users.first_name', 'Users.last_name'])
        //     ->where(['Users.status' => 'A'])
        //     ->where(['Users.id NOT IN' => $subquery])
        //     ->matching('Roles', function ($q) {
        //         return $q->where(['Roles.name' => 'Showroom Manager']);
        //     });

        return $fetchShowroomManager;
    }

    public function fetchSupervisor()
    {
        $query = $this->find()
            ->select(['id', 'first_name', 'last_name']) // Select only required fields
            ->where(['Users.status' => 'A'])
            ->matching('Roles', function ($q) {
                return $q->where(['Roles.name' => 'Showroom Supervisor']);
            });

        return $query->toArray();
    }

    //S
    public function fetchShowroomUser()
    {

        $fetchShowroomUser = $this->find()
            ->select(['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']) // Select only required fields
            ->where(['Users.status' => 'A'])
            ->matching('Roles', function ($q) {
                return $q->where(['Roles.name' => 'Sales Person']);
            });

        return $fetchShowroomUser;
    }

    //M
    public function add_new_user($attributes)
    {

        $new = $this->newEmptyEntity();
        foreach ($attributes as $key => $value) {
            $new->$key = $value;
        }

        if ($this->save($new)) {
            return $new->id;
        } else {
            return false;
        }
    }

    public function get_active_user_by_email($email)
    {
        return $this->find()->where(['email' => $email, 'status' => 'A'])->contain(['Customers'])->first();
    }

    public function update_user_by_id($id, $attributes)
    {
        $user = $this->get($id);
        $user = $this->patchEntity($user, $attributes);
        if ($this->save($user)) {
            return $user;
        } else {
            return $user;
        }
    }

    public function changeUserPassword($userId, $oldPassword, $newPassword)
    {
        $user = $this->get($userId);
        $hasher = new DefaultPasswordHasher(); // Adjust the namespace if needed

        if (!$hasher->check($oldPassword, $user->password)) {
            return ['status' => 'error'];
        }

        $user->password = $newPassword;

        if ($this->save($user)) {
            return ['status' => 'success'];
        } else {
            return ['status' => 'error'];
        }
    }

    public function delete(EntityInterface $customer, array $options = []): bool
    {
        $customer->status = 'D';
        if ($this->save($customer)) {
            return true;
        }
        return false;
    }

    //S
    public function listSupervisor()
    {
        $query = $this->find()
            ->select([ 'id' => 'Users.id', // Customer ID
                'name' => 'CONCAT(Users.first_name, " ", Users.last_name)' // Full name
                ]) // Select only required fields
            ->where(['Users.status' => 'A'])
            ->matching('Roles', function ($q) {
                return $q->where(['Roles.name' => 'Showroom Supervisor']);
            });

        return $query->toArray();
    }
    public function getUserIdentityInfo($identity)
    {
        // Check if identity is provided
        if (!$identity || !isset($identity->id)) {
            return null; // Return null or handle the case where identity is not provided
        }

        // Fetch user information with customer and driver details
        $user = $this->Users->find()
        ->contain(['Customers' => function ($q) {
            return $q->select(['id']);
        }])
        ->select(['Users.id'])
        ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
        ->first();
        return $user;
    }
}
