<?php
declare(strict_types=1);

namespace App\Controller;

use Cake\I18n\FrozenTime;

/**
 * ContactEnquiries Controller
 *
 * @property \App\Model\Table\ContactsTable $Contacts
 */
class ContactEnquiriesController extends AppController
{
    protected $Contacts;
    /**
     * Initialize method
     *
     * @return void
     */
    public function initialize(): void
    {
        parent::initialize();
        $this->Contacts = $this->fetchTable('Contacts');
        $this->viewBuilder()->setLayout('admin');
        
        // Skip permission check for this controller
        //this->Authentication->addUnauthenticatedActions(['index', 'view', 'updateStatus', 'delete']);
    }

    /**
     * Index method - List all contact enquiries
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $this->paginate = [
            'limit' => $this->paginationCount ?? 20,
            'order' => ['Contacts.created' => 'DESC']
        ];
        
        $conditions = ['deleted' => 0];
        
        // Filter by status
        if ($this->request->getQuery('status')) {
            $conditions['status'] = $this->request->getQuery('status');
        }
        
        // Filter by inquiry type
        if ($this->request->getQuery('inquiry_type')) {
            $conditions['inquiry_type'] = $this->request->getQuery('inquiry_type');
        }
        
        // Filter by search term
        if ($this->request->getQuery('search')) {
            $search = $this->request->getQuery('search');
            $conditions['OR'] = [
                'name LIKE' => '%' . $search . '%',
                'email LIKE' => '%' . $search . '%',
                'phone LIKE' => '%' . $search . '%',
                'message LIKE' => '%' . $search . '%'
            ];
        }
        
        $query = $this->Contacts->find()
            ->where($conditions);
            
        $contacts = $this->paginate($query);
        
        // Get unique inquiry types for filter dropdown
        $inquiryTypes = $this->Contacts->find('list', [
            'keyField' => 'inquiry_type',
            'valueField' => 'inquiry_type'
        ])->distinct('inquiry_type')->toArray();
        
        $statusOptions = ['Pending' => 'Pending', 'In-Progress' => 'In-Progress', 'Complete' => 'Complete'];
        
        $this->set(compact('contacts', 'inquiryTypes', 'statusOptions'));
    }

    /**
     * View method - Display a single contact enquiry
     *
     * @param string|null $id Contact id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $contact = $this->Contacts->get($id);
        $this->set(compact('contact'));
    }

    /**
     * Update status method
     *
     * @param string|null $id Contact id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function updateStatus($id = null)
    {
        $this->request->allowMethod(['post', 'put']);
        $contact = $this->Contacts->get($id);
        
        if ($this->request->is(['post', 'put'])) {
            $contact = $this->Contacts->patchEntity($contact, $this->request->getData());
            if ($this->Contacts->save($contact)) {
                $this->Flash->success(__('The contact enquiry status has been updated.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The contact enquiry status could not be updated. Please try again.'));
        }
        
        return $this->redirect(['action' => 'index']);
    }

    /**
     * Delete method - Soft delete a contact enquiry
     *
     * @param string|null $id Contact id.
     * @return \Cake\Http\Response|null|void Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $contact = $this->Contacts->get($id);
        
        // Perform soft delete
        $contact->deleted = true;
        $contact->deleted_date = FrozenTime::now();
        
        if ($this->Contacts->save($contact)) {
            $this->Flash->success(__('The contact enquiry has been deleted.'));
        } else {
            $this->Flash->error(__('The contact enquiry could not be deleted. Please try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }
}