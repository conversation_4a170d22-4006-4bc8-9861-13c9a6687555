<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

/**
 * Migration to add country_id fields to tables for country filtering
 * 
 * This migration adds country_id fields to various tables to enable
 * country-based filtering in the admin panel.
 * 
 * Run this migration with: bin/cake migrations migrate
 */
class AddCountryIdToTables extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        // Add country_id to orders table (if it exists)
        if ($this->hasTable('orders')) {
            $table = $this->table('orders');
            if (!$table->hasColumn('country_id')) {
                $table->addColumn('country_id', 'integer', [
                    'default' => null,
                    'null' => true,
                    'after' => 'id', // Add after id column
                    'comment' => 'Foreign key to countries table for filtering'
                ]);
                
                // Add foreign key constraint
                $table->addForeignKey('country_id', 'countries', 'id', [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE'
                ]);
                
                $table->update();
            }
        }

        // Add country_id to customers table (if it exists)
        if ($this->hasTable('customers')) {
            $table = $this->table('customers');
            if (!$table->hasColumn('country_id')) {
                $table->addColumn('country_id', 'integer', [
                    'default' => null,
                    'null' => true,
                    'after' => 'id',
                    'comment' => 'Foreign key to countries table for filtering'
                ]);
                
                $table->addForeignKey('country_id', 'countries', 'id', [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE'
                ]);
                
                $table->update();
            }
        }

        // Add country_id to products table (if it exists and doesn't have it)
        if ($this->hasTable('products')) {
            $table = $this->table('products');
            if (!$table->hasColumn('country_id')) {
                $table->addColumn('country_id', 'integer', [
                    'default' => null,
                    'null' => true,
                    'after' => 'id',
                    'comment' => 'Foreign key to countries table for filtering'
                ]);
                
                $table->addForeignKey('country_id', 'countries', 'id', [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE'
                ]);
                
                $table->update();
            }
        }

        // Add country_id to showrooms table (if it exists and doesn't have it)
        if ($this->hasTable('showrooms')) {
            $table = $this->table('showrooms');
            if (!$table->hasColumn('country_id')) {
                $table->addColumn('country_id', 'integer', [
                    'default' => null,
                    'null' => true,
                    'after' => 'id',
                    'comment' => 'Foreign key to countries table for filtering'
                ]);
                
                $table->addForeignKey('country_id', 'countries', 'id', [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE'
                ]);
                
                $table->update();
            }
        }

        // Add country_id to warehouses table (if it exists and doesn't have it)
        if ($this->hasTable('warehouses')) {
            $table = $this->table('warehouses');
            if (!$table->hasColumn('country_id')) {
                $table->addColumn('country_id', 'integer', [
                    'default' => null,
                    'null' => true,
                    'after' => 'id',
                    'comment' => 'Foreign key to countries table for filtering'
                ]);
                
                $table->addForeignKey('country_id', 'countries', 'id', [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE'
                ]);
                
                $table->update();
            }
        }

        // Add country_id to suppliers table (if it exists and doesn't have it)
        if ($this->hasTable('suppliers')) {
            $table = $this->table('suppliers');
            if (!$table->hasColumn('country_id')) {
                $table->addColumn('country_id', 'integer', [
                    'default' => null,
                    'null' => true,
                    'after' => 'id',
                    'comment' => 'Foreign key to countries table for filtering'
                ]);
                
                $table->addForeignKey('country_id', 'countries', 'id', [
                    'delete' => 'SET_NULL',
                    'update' => 'CASCADE'
                ]);
                
                $table->update();
            }
        }
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        // Remove country_id from orders table
        if ($this->hasTable('orders')) {
            $table = $this->table('orders');
            if ($table->hasColumn('country_id')) {
                $table->dropForeignKey('country_id');
                $table->removeColumn('country_id');
                $table->update();
            }
        }

        // Remove country_id from customers table
        if ($this->hasTable('customers')) {
            $table = $this->table('customers');
            if ($table->hasColumn('country_id')) {
                $table->dropForeignKey('country_id');
                $table->removeColumn('country_id');
                $table->update();
            }
        }

        // Remove country_id from products table
        if ($this->hasTable('products')) {
            $table = $this->table('products');
            if ($table->hasColumn('country_id')) {
                $table->dropForeignKey('country_id');
                $table->removeColumn('country_id');
                $table->update();
            }
        }

        // Remove country_id from showrooms table
        if ($this->hasTable('showrooms')) {
            $table = $this->table('showrooms');
            if ($table->hasColumn('country_id')) {
                $table->dropForeignKey('country_id');
                $table->removeColumn('country_id');
                $table->update();
            }
        }

        // Remove country_id from warehouses table
        if ($this->hasTable('warehouses')) {
            $table = $this->table('warehouses');
            if ($table->hasColumn('country_id')) {
                $table->dropForeignKey('country_id');
                $table->removeColumn('country_id');
                $table->update();
            }
        }

        // Remove country_id from suppliers table
        if ($this->hasTable('suppliers')) {
            $table = $this->table('suppliers');
            if ($table->hasColumn('country_id')) {
                $table->dropForeignKey('country_id');
                $table->removeColumn('country_id');
                $table->update();
            }
        }
    }
}
