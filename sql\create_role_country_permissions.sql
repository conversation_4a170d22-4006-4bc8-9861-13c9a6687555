-- Create role_country_permissions table manually
-- Run this SQL in your database if migrations are not working

-- First, add country_access_type to roles table if it doesn't exist
ALTER TABLE `roles` 
ADD COLUMN `country_access_type` ENUM('all', 'specific', 'user_country') DEFAULT 'all' NOT NULL 
COMMENT 'all=access all countries, specific=access specific countries only, user_country=access only user assigned country';

-- Create role_country_permissions table
CREATE TABLE `role_country_permissions` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `role_id` INT(11) NOT NULL COMMENT 'Foreign key to roles table',
    `country_id` INT(11) NULL COMMENT 'Foreign key to countries table. NULL means access to all countries',
    `can_access` TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Whether this role can access this country',
    `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON>EY (`id`),
    INDEX `idx_role_id` (`role_id`),
    INDEX `idx_country_id` (`country_id`),
    UNIQUE INDEX `unique_role_country` (`role_id`, `country_id`),
    CONSTRAINT `fk_role_country_permissions_role_id` 
        FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_role_country_permissions_country_id` 
        FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) 
        ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Insert default permissions for existing roles (all roles get access to all countries)
INSERT INTO `role_country_permissions` (`role_id`, `country_id`, `can_access`)
SELECT `id`, NULL, 1 FROM `roles` WHERE `status` != 'D';

-- Verify the table was created
SELECT 'Table created successfully' as status;
SELECT COUNT(*) as role_count FROM `role_country_permissions`;
