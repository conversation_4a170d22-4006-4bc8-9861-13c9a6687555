<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Country Entity
 *
 * @property int $id
 * @property string $name
 * @property string|null $name_ar
 * @property string|null $iso_code_2
 * @property string|null $iso_code_3
 * @property string|null $phone_code
 * @property string|null $address_format
 *
 * @property \App\Model\Entity\Currency $currency
 * @property \App\Model\Entity\CustomerAddress[] $customer_addresses
 * @property \App\Model\Entity\Merchant[] $merchants
 * @property \App\Model\Entity\Showroom[] $showrooms
 */
class Country extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'name' => true,
        'name_ar' => true,
        'currency' => true,
        'iso_code_2' => true,
        'iso_code_3' => true,
        'phone_code' => true,
        'address_format' => true,
        'customer_addresses' => true,
        'merchants' => true,
        'showrooms' => true,
    ];
}
