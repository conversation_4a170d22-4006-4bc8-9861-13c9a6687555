# Country Filtering System for Admin Panel

This document explains how to implement and use the country-based filtering system in the admin panel.

## Overview

The country filtering system allows administrators to filter data by country across the entire admin panel. The selected country is stored in the session and persists across page loads.

## Features

- **Header Dropdown**: Country selection dropdown in the admin header
- **Session Management**: Selected country stored in session
- **Automatic Filtering**: Easy-to-use methods for applying country filters
- **AJAX Support**: Dynamic country switching without page reload
- **Persistent State**: Country selection persists across admin pages

## Implementation

### 1. Database Requirements

Ensure your tables have a `country_id` field that references the `countries` table:

```sql
-- Example: Add country_id to orders table
ALTER TABLE orders ADD COLUMN country_id INT(11) NULL;
ALTER TABLE orders ADD FOREIGN KEY (country_id) REFERENCES countries(id);

-- Example: Add country_id to customers table  
ALTER TABLE customers ADD COLUMN country_id INT(11) NULL;
ALTER TABLE customers ADD FOREIGN KEY (country_id) REFERENCES countries(id);
```

### 2. Controller Usage

In your controllers, use the provided helper methods:

```php
<?php
// In your controller action
public function index()
{
    $query = $this->YourModel->find()
        ->contain(['Countries'])
        ->order(['created' => 'DESC']);

    // Apply country filter automatically
    $query = $this->applyCountryFilter($query, 'YourModel.country_id');

    $data = $this->paginate($query);
    $this->set(compact('data'));
}
```

### 3. Available Methods

#### `getCurrentCountryFilter()`
Returns the currently selected country ID from session.

```php
$selectedCountryId = $this->getCurrentCountryFilter();
if ($selectedCountryId) {
    // Country is selected
    $country = $this->Countries->getCountryById($selectedCountryId);
}
```

#### `applyCountryFilter($query, $countryField)`
Applies country filter to a query if a country is selected.

```php
// Basic usage
$query = $this->applyCountryFilter($query, 'Products.country_id');

// With joins
$query = $this->applyCountryFilter($query, 'Customers.country_id');
```

### 4. Frontend Integration

The country dropdown is automatically available in the admin header. No additional frontend code is required.

#### Custom AJAX Handling

If you need custom AJAX functionality:

```javascript
// Get current country filter
$.ajax({
    url: '/app/set-country-filter',
    type: 'POST',
    data: { country_id: countryId },
    success: function(response) {
        // Handle success
        window.location.reload(); // Reload to apply filter
    }
});
```

## Examples

### Example 1: Simple Product Filtering

```php
public function index()
{
    $query = $this->Products->find()
        ->contain(['Brands', 'Categories']);

    // Apply country filter if products have country_id
    $query = $this->applyCountryFilter($query, 'Products.country_id');

    $products = $this->paginate($query);
    $this->set(compact('products'));
}
```

### Example 2: Dashboard Statistics

```php
public function dashboard()
{
    $ordersQuery = $this->Orders->find();
    $ordersQuery = $this->applyCountryFilter($ordersQuery, 'Orders.country_id');
    
    $stats = [
        'total_orders' => $ordersQuery->count(),
        'revenue' => $ordersQuery->select(['total' => 'SUM(amount)'])->first()->total
    ];
    
    $this->set(compact('stats'));
}
```

### Example 3: Custom Logic Based on Country

```php
public function customLogic()
{
    $selectedCountryId = $this->getCurrentCountryFilter();
    
    if ($selectedCountryId) {
        $country = $this->Countries->getCountryById($selectedCountryId);
        
        // Apply country-specific business logic
        switch ($country->iso_code_2) {
            case 'QA': // Qatar
                $this->handleQatarLogic();
                break;
            case 'AE': // UAE
                $this->handleUAELogic();
                break;
        }
    }
}
```

## Session Structure

The country filter is stored in the session as:

```php
$_SESSION['Admin']['selectedCountryId'] = 123;
```

## Styling

The country dropdown includes custom CSS styling for a modern look. The styles are automatically included in the admin layout.

## Troubleshooting

### Country Filter Not Working

1. Ensure your table has a `country_id` field
2. Check that the field name matches what you're passing to `applyCountryFilter()`
3. Verify the Countries table has data

### Dropdown Not Showing

1. Check that `$countries` variable is being set in AppController
2. Ensure the header element is being included in your layout
3. Verify JavaScript is loading correctly

### Session Not Persisting

1. Check session configuration in CakePHP
2. Ensure cookies are enabled in browser
3. Verify CSRF token is being passed correctly

## Security Considerations

- Country IDs are validated against the database
- CSRF protection is implemented for AJAX requests
- Session data is properly sanitized

## Performance Notes

- Country dropdown data is cached in the controller
- Queries are optimized to only apply filters when necessary
- Session reads are minimized through caching
