<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

/**
 * Migration to create role-country permissions system
 * 
 * This migration creates a table to manage which countries each role can access
 */
class CreateRoleCountryPermissions extends AbstractMigration
{
    /**
     * Up Method.
     *
     * @return void
     */
    public function up(): void
    {
        // Create role_country_permissions table
        $table = $this->table('role_country_permissions');
        $table->addColumn('role_id', 'integer', [
            'null' => false,
            'comment' => 'Foreign key to roles table'
        ])
        ->addColumn('country_id', 'integer', [
            'null' => true,
            'comment' => 'Foreign key to countries table. NULL means access to all countries'
        ])
        ->addColumn('can_access', 'boolean', [
            'default' => true,
            'null' => false,
            'comment' => 'Whether this role can access this country'
        ])
        ->addColumn('created', 'datetime', [
            'default' => 'CURRENT_TIMESTAMP',
            'null' => false
        ])
        ->addColumn('modified', 'datetime', [
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'null' => false
        ])
        ->addIndex(['role_id'])
        ->addIndex(['country_id'])
        ->addIndex(['role_id', 'country_id'], ['unique' => true])
        ->create();

        // Add foreign key constraints
        $table->addForeignKey('role_id', 'roles', 'id', [
            'delete' => 'CASCADE',
            'update' => 'CASCADE'
        ]);

        $table->addForeignKey('country_id', 'countries', 'id', [
            'delete' => 'CASCADE',
            'update' => 'CASCADE'
        ]);

        $table->update();

        // Add country_access_type to roles table
        $rolesTable = $this->table('roles');
        if (!$rolesTable->hasColumn('country_access_type')) {
            $rolesTable->addColumn('country_access_type', 'enum', [
                'values' => ['all', 'specific', 'user_country'],
                'default' => 'all',
                'null' => false,
                'comment' => 'all=access all countries, specific=access specific countries only, user_country=access only user assigned country'
            ]);
            $rolesTable->update();
        }
    }

    /**
     * Down Method.
     *
     * @return void
     */
    public function down(): void
    {
        // Remove country_access_type from roles table
        $rolesTable = $this->table('roles');
        if ($rolesTable->hasColumn('country_access_type')) {
            $rolesTable->removeColumn('country_access_type');
            $rolesTable->update();
        }

        // Drop role_country_permissions table
        $this->table('role_country_permissions')->drop()->save();
    }
}
