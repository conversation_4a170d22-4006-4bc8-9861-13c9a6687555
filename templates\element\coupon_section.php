<?php
/**
 * Common Coupon Section Element
 * Can be used in cart, address, and checkout pages
 * 
 * Required variables:
 * - $orderSummary (array): Order summary data
 * - $availableCoupons (array): Available coupons
 * - $appliedCoupon (array|null): Currently applied coupon
 */

$appliedCoupon = $appliedCoupon ?? null;
$availableCoupons = $availableCoupons ?? [];
$showAvailableCoupons = $showAvailableCoupons ?? true;
?>

<!-- Coupon Section -->
<div class="coupon-section">
    <?php if ($appliedCoupon): ?>
        <!-- Applied Coupon Display -->
        <div class="applied-coupon-card mb-3 p-3 border border-success rounded bg-success-light" id="applied-coupon-card">
            <div class="d-flex justify-content-between align-items-center">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-1">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong class="text-success"><?= h($appliedCoupon['code']) ?></strong>
                        <?php if ($appliedCoupon['coupon_type'] === 'shipping'): ?>
                            <span class="badge bg-info text-white ms-2">Free Shipping</span>
                        <?php elseif ($appliedCoupon['coupon_type'] === 'percentage'): ?>
                            <span class="badge bg-success text-white ms-2"><?= $appliedCoupon['discount_value'] ?>% OFF</span>
                        <?php else: ?>
                            <span class="badge bg-primary text-white ms-2"><?= $this->Price->setPriceFormat($appliedCoupon['discount_value']) ?> OFF</span>
                        <?php endif; ?>
                    </div>
                    <small class="text-muted">
                        <?php if ($appliedCoupon['coupon_type'] === 'shipping'): ?>
                            Free shipping applied
                        <?php else: ?>
                            You saved <?= $this->Price->setPriceFormat($appliedCoupon['discount_amount']) ?>
                        <?php endif; ?>
                    </small>
                </div>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeCoupon()">
                    <i class="fas fa-times"></i> Remove
                </button>
            </div>
        </div>
    <?php else: ?>
        <!-- Coupon Input Section -->
        <div class="coupon-input-section mb-3" id="coupon-input-section">
            <div class="d-flex gap-2">
                <div class="flex-grow-1">
                    <input type="text" 
                           class="form-control" 
                           id="coupon-code-input" 
                           placeholder="Enter coupon code"
                           maxlength="20">
                    <div class="invalid-feedback" id="coupon-error-message"></div>
                </div>
                <button type="button" 
                        class="btn btn-outline-success" 
                        id="apply-coupon-btn" 
                        onclick="applyCoupon()">
                    <span class="btn-text">Apply</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                </button>
            </div>
            
            <?php if ($showAvailableCoupons && !empty($availableCoupons)): ?>
               <!--
                <div class="mt-2">
                    <button type="button" 
                            class="btn btn-link btn-sm p-0 text-decoration-none" 
                            onclick="toggleAvailableCoupons()">
                        <i class="fas fa-tag me-1"></i>
                        <span id="available-coupons-toggle-text">View available coupons</span>
                        <i class="fas fa-chevron-down ms-1" id="available-coupons-toggle-icon"></i>
                    </button>
                </div>  -->
            <?php endif; ?>
        </div>
    <?php endif; ?>

      <div class="mt-2">
                    <button type="button" 
                            class="btn btn-link btn-sm p-0 text-decoration-none" 
                            onclick="toggleAvailableCoupons()">
                        <i class="fas fa-tag me-1"></i>
                        <span id="available-coupons-toggle-text">View available coupons</span>
                        <i class="fas fa-chevron-down ms-1" id="available-coupons-toggle-icon"></i>
                    </button>
                </div>
    <?php if ($showAvailableCoupons && !empty($availableCoupons)): ?>
        <!-- Available Coupons List -->
        <div class="available-coupons mt-2" id="available-coupons" style="display: none;">
            <small class="text-muted d-block mb-2">Available Coupons:</small>
            <?php foreach ($availableCoupons as $coupon): ?>
                <div class="coupon-item p-3 border rounded mb-2 <?= $coupon['is_applicable'] ? 'border-success' : 'border-warning' ?>" 
                     data-coupon-id="<?= $coupon['id'] ?>" data-coupon-code="<?= h($coupon['code']) ?>">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <strong class="<?= $coupon['is_applicable'] ? 'text-success' : 'text-warning' ?> me-2">
                                    <?= h($coupon['code']) ?>
                                </strong>
                                <?php if ($coupon['type'] === 'shipping'): ?>
                                    <span class="badge bg-info text-white">Free Shipping</span>
                                <?php elseif ($coupon['type'] === 'percentage'): ?>
                                    <span class="badge bg-success text-white"><?= $coupon['value'] ?>% OFF</span>
                                <?php else: ?>
                                    <span class="badge bg-primary text-white"><?= $this->Price->setPriceFormat($coupon['value']) ?> OFF</span>
                                <?php endif; ?>
                            </div>
                            
                            <small class="d-block text-muted mb-1">
                                <?= h($coupon['description']) ?>
                            </small>

                            <?php if (!empty($coupon['applicable_to'])): ?>
                                <small class="d-block mb-1" style="color:green">
                                    <i class="fas fa-tag me-1"></i>
                                    Applicable <?= h($coupon['applicable_to']) ?>
                                </small>
                            <?php endif; ?>

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    Min: <?= $this->Price->setPriceFormat($coupon['min_amount']) ?>
                                    <?php if (!empty($coupon['max_discount']) && $coupon['type'] === 'percentage'): ?>
                                        | Max: <?= $this->Price->setPriceFormat($coupon['max_discount']) ?>
                                    <?php endif; ?>
                                </small>
                                <small class="text-muted">
                                    Valid until: <?= h($coupon['valid_until']) ?>
                                </small>
                            </div>
                            
                            <?php if (!$coupon['is_applicable']): ?>
                                <small class="text-warning d-block mt-1">
                                    <i class="fas fa-info-circle"></i>
                                    Add <?= $this->Price->setPriceFormat($coupon['amount_needed']) ?> more to use this coupon
                                </small>
                            <?php endif; ?>
                        </div>
                        
                        <div class="ms-2">
                            <?php if ($coupon['is_applicable']): ?>
                                <button type="button" class="btn btn-sm btn-success" 
                                        onclick="applyCouponCode('<?= h($coupon['code']) ?>')">
                                    <i class="fas fa-check"></i> Apply
                                </button>
                            <?php else: ?>
                                <button type="button" class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-lock"></i> Locked
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($coupon['terms_conditions'])): ?>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                <a href="#" class="text-decoration-none" onclick="showCouponTerms('<?= h($coupon['code']) ?>', '<?= h(addslashes($coupon['terms_conditions'])) ?>')">
                                    Terms & Conditions
                                </a>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php elseif ($showAvailableCoupons): ?>
        <!-- No Applicable Coupons Message -->
        <div class="available-coupons mt-2" id="available-coupons" style="display: none;">
            <div class="text-center py-4">
                <i class="fas fa-ticket-alt text-muted mb-2" style="font-size: 2rem;"></i>
                <p class="text-muted mb-1">No coupons available for your current cart products</p>
                <small class="text-muted">Add specific products that have available coupons to see offers</small>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Coupon Terms & Conditions Modal -->
<div class="modal fade" id="couponTermsModal" tabindex="-1" aria-labelledby="couponTermsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title fw-bold" id="couponTermsModalLabel">
                    <i class="fas fa-ticket-alt text-primary me-2"></i>
                    Coupon Terms & Conditions
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body py-4">
                <div class="mb-3">
                    <h6 class="text-primary mb-2" id="couponCodeTitle">Coupon Code</h6>
                </div>
                <div class="terms-content" id="couponTermsContent">
                    <!-- Terms content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
