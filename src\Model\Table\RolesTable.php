<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use Cake\Validation\Validator;

/**
 * Roles Model
 *
 * @property \App\Model\Table\PermissionsTable&\Cake\ORM\Association\HasMany $Permissions
 * @property \App\Model\Table\UsersTable&\Cake\ORM\Association\HasMany $Users
 *
 * @method \App\Model\Entity\Role newEmptyEntity()
 * @method \App\Model\Entity\Role newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Role> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Role get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Role findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Role patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Role> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Role|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Role saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Role>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Role>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Role>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Role> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Role>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Role>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Role>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Role> deleteManyOrFail(iterable $entities, array $options = [])
 */
class RolesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('roles');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->hasMany('Permissions', [
            'foreignKey' => 'role_id',
        ]);
        $this->hasMany('Users', [
            'foreignKey' => 'role_id',
        ]);
        $this->hasMany('RoleCountryPermissions', [
            'foreignKey' => 'role_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('status')
            ->notEmptyString('status');
            
        return $validator;
    }

    /**
     * Get countries accessible by a role
     *
     * @param int $roleId
     * @return array|null Array of country IDs, or null if all countries
     */
    public function getAccessibleCountries($roleId)
    {
        $role = $this->get($roleId);

        if ($role->country_access_type === 'all') {
            return null; // All countries
        }

        if ($role->country_access_type === 'user_country') {
            return 'user_country'; // Special flag for user-based country access
        }

        // For 'specific' type, get from RoleCountryPermissions
        $roleCountryPermissions = TableRegistry::getTableLocator()->get('RoleCountryPermissions');
        return $roleCountryPermissions->getRoleCountryIds($roleId);
    }

    /**
     * Check if a role can access a specific country
     *
     * @param int $roleId
     * @param int|null $countryId
     * @param int|null $userCountryId For user_country access type
     * @return bool
     */
    public function canAccessCountry($roleId, $countryId = null, $userCountryId = null)
    {
        $role = $this->get($roleId);

        switch ($role->country_access_type) {
            case 'all':
                return true;

            case 'user_country':
                if ($userCountryId === null) {
                    return false;
                }
                return $countryId === null || $countryId === $userCountryId;

            case 'specific':
                $roleCountryPermissions = TableRegistry::getTableLocator()->get('RoleCountryPermissions');
                return $roleCountryPermissions->canAccessCountry($roleId, $countryId);

            default:
                return false;
        }
    }

    /**
     * Set country access for a role
     *
     * @param int $roleId
     * @param string $accessType 'all', 'specific', or 'user_country'
     * @param array $countryIds Array of country IDs for 'specific' type
     * @return bool
     */
    public function setCountryAccess($roleId, $accessType, $countryIds = [])
    {
        // Update role's country_access_type
        $role = $this->get($roleId);
        $role->country_access_type = $accessType;

        if (!$this->save($role)) {
            return false;
        }

        // Set country permissions
        $roleCountryPermissions = TableRegistry::getTableLocator()->get('RoleCountryPermissions');
        return $roleCountryPermissions->setRoleCountryPermissions($roleId, $countryIds, $accessType);
    }
}
