<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Contact $contact
 */
?>
<div class="card">
    <div class="card-header">
        <h4><?= __('Contact Enquiry Details') ?></h4>
        <div class="card-header-action">
            <?= $this->Html->link('<i class="fas fa-arrow-left"></i> ' . __('Back'), ['action' => 'index'], ['class' => 'btn btn-primary text-white', 'escape' => false]) ?>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-striped">
                    <tr>
                        <th><?= __('Name') ?></th>
                        <td><?= h($contact->name) ?></td>
                    </tr>
                    <tr>
                        <th><?= __('Email') ?></th>
                        <td><?= h($contact->email) ?></td>
                    </tr>
                    <tr>
                        <th><?= __('Phone') ?></th>
                        <td><?= h($contact->phone) ?></td>
                    </tr>
                    <tr>
                        <th><?= __('Inquiry Type') ?></th>
                        <td><?= h($contact->inquiry_type) ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-striped">
                    <tr>
                        <th><?= __('Status') ?></th>
                        <td>
                            <span class="badge <?= ($contact->status == 'Pending' || empty($contact->status)) ? 'badge-warning' : ($contact->status == 'In-Progress' ? 'badge-info' : 'badge-success') ?>">
                                <?= empty($contact->status) ? 'Pending' : h($contact->status) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th><?= __('Created') ?></th>
                        <td><?= h($contact->created) ?></td>
                    </tr>
                    <tr>
                        <th><?= __('Modified') ?></th>
                        <td><?= h($contact->modified) ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Message') ?></h4>
                    </div>
                    <div class="card-body">
                        <div class="p-3 bg-light rounded">
                            <?= nl2br(h($contact->message)) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Update Status') ?></h4>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <?= $this->Form->create(null, ['url' => ['action' => 'updateStatus', $contact->id], 'class' => 'd-inline-block mr-2']) ?>
                            <?= $this->Form->hidden('status', ['value' => 'Pending']) ?>
                            <button type="submit" class="btn btn-warning">Pending</button>
                            <?= $this->Form->end() ?>
                            
                            <?= $this->Form->create(null, ['url' => ['action' => 'updateStatus', $contact->id], 'class' => 'd-inline-block mr-2']) ?>
                            <?= $this->Form->hidden('status', ['value' => 'In-Progress']) ?>
                            <button type="submit" class="btn btn-info">In-Progress</button>
                            <?= $this->Form->end() ?>
                            
                            <?= $this->Form->create(null, ['url' => ['action' => 'updateStatus', $contact->id], 'class' => 'd-inline-block mr-2']) ?>
                            <?= $this->Form->hidden('status', ['value' => 'Complete']) ?>
                            <button type="submit" class="btn btn-success">Complete</button>
                            <?= $this->Form->end() ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12 text-right">
                <?= $this->Form->postLink(__('Delete Enquiry'), ['action' => 'delete', $contact->id], ['confirm' => __('Are you sure you want to delete # {0}?', $contact->id), 'class' => 'btn btn-danger']) ?>
            </div>
        </div>
    </div>
</div>