<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Widget Entity
 *
 * @property int $id
 * @property string|null $url_key
 * @property string $title
 * @property string|null $title_ar
 * @property string|null $summary
 * @property string|null $summary_ar
 * @property int|null $no_of_product
 * @property string|null $product_preference
 * @property string $widget_type
 * @property bool|null $display_in_web
 * @property bool|null $display_in_mobile
 * @property string|null $web_image
 * @property string|null $mobile_image
 * @property \Cake\I18n\Date|null $start_date
 * @property \Cake\I18n\Date|null $end_date
 * @property int|null $display_order
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\WidgetCategoryMapping[] $widget_category_mappings
 * @property \App\Model\Entity\Category[] $categories
 */
class Widget extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        '*' => true,
        'url_key' => true,
        'country_id' => true,
        'title' => true,
        'title_ar' => true,
        'summary' => true,
        'summary_ar' => true,
        'no_of_product' => true,
        'product_preference' => true,
        'widget_type' => true,
        'display_in_web' => true,
        'display_in_mobile' => true,
        'web_image' => true,
        'mobile_image' => true,
        'start_date' => true,
        'end_date' => true,
        'display_order' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'widget_category_mappings' => true,
        'categories' => true,
    ];
}
