/**
 * Cart Update Functions
 * Handles real-time cart count updates without page reload
 */

// Global cart update functions
window.CartUpdater = {

    /**
     * Update cart count badge
     * @param {number} count - New cart count
     */
    updateCartCount: function(count) {
        //console.log('CartUpdater.updateCartCount called with count:', count);

        // Update both desktop and mobile cart badges
        const desktopBadge = document.getElementById('cart-count-badge-desktop');
        const desktopNumberSpan = document.getElementById('cart-count-number-desktop');
        const mobileBadge = document.getElementById('cart-count-badge-mobile');
        const mobileNumberSpan = document.getElementById('cart-count-number-mobile');

        // Update desktop badge
        if (desktopBadge && desktopNumberSpan) {
            if (count > 0) {
                desktopBadge.style.display = '';
                desktopNumberSpan.textContent = count;

                // Add animation effect
                desktopBadge.classList.add('cart-count-updated');
                setTimeout(() => {
                    desktopBadge.classList.remove('cart-count-updated');
                }, 300);
            } else {
                desktopBadge.style.display = 'none';
            }
        }

        // Update mobile badge
        if (mobileBadge && mobileNumberSpan) {
            if (count > 0) {
                mobileBadge.style.display = '';
                mobileNumberSpan.textContent = count;

                // Add animation effect
                mobileBadge.classList.add('cart-count-updated');
                setTimeout(() => {
                    mobileBadge.classList.remove('cart-count-updated');
                }, 300);
            } else {
                mobileBadge.style.display = 'none';
            }
        }
    },

    /**
     * Fetch current cart count from server
     */
    refreshCartCount: function() {
        fetch('/cart/getCartCount', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text(); // Get as text first
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    this.updateCartCount(data.count);
                } else {
                    console.error('Cart count fetch failed:', data.message);
                }
            } catch (e) {
                console.error('Invalid JSON response for cart count:', text);
                // Try to extract error from HTML if it's an error page
                if (text.includes('<div class')) {
                    console.error('Received HTML error page instead of JSON');
                }
            }
        })
        .catch(error => {
            console.error('Error fetching cart count:', error);
        });
    },

    /**
     * Enhanced add to cart function with cart count update
     * @param {number} productId - Product ID to add
     * @param {number} quantity - Quantity to add (default: 1)
     * @param {function} successCallback - Optional success callback
     * @param {function} errorCallback - Optional error callback
     */
    addToCart: function(productId, quantity = 1, successCallback = null, errorCallback = null) {
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('meta[name="csrfToken"]')?.getAttribute('content');

        if (!csrfToken) {
            console.error('CSRF token not found');
            if (errorCallback) errorCallback('Security token not found');
            return;
        }

        fetch(`/cart/addToCart/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                quantity: quantity
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text(); // Get as text first
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.status === 'success') {
                    // Update cart count if provided
                    if (data.cartCount !== undefined) {
                        this.updateCartCount(data.cartCount);
                    }

                    // Show success message
                    if (window.showToastMessage) {
                        window.showToastMessage(data.message, 'success');
                    }

                    // Call success callback if provided
                    if (successCallback) {
                        successCallback(data);
                    }
                } else {
                    // Show error message
                    if (window.showToastMessage) {
                        window.showToastMessage(data.message, 'error');
                    }

                    // Call error callback if provided
                    if (errorCallback) {
                        errorCallback(data.message);
                    }
                }
            } catch (e) {
                console.error('Invalid JSON response for addToCart:', text);
                if (text.includes('<div class')) {
                    throw new Error('Server returned HTML error page instead of JSON');
                }
                throw new Error('Invalid JSON response from server');
            }
        })
        .catch(error => {
            console.error('Error adding to cart:', error);

            // Show error message
            if (window.showToastMessage) {
                window.showToastMessage('Error adding item to cart', 'error');
            }

            // Call error callback if provided
            if (errorCallback) {
                errorCallback('Network error');
            }
        });
    },

    /**
     * Initialize cart updater
     */
    init: function() {
       // console.log('CartUpdater.init() called');

        // Check if cart elements exist
        const desktopBadge = document.getElementById('cart-count-badge-desktop');
        const mobileBadge = document.getElementById('cart-count-badge-mobile');
       // console.log('Initial cart elements check - Desktop Badge:', desktopBadge, 'Mobile Badge:', mobileBadge);

        // Add CSS for cart count animation
        const style = document.createElement('style');
        style.textContent = `
            .cart-count-updated {
                animation: cartCountPulse 0.3s ease-in-out;
            }

            @keyframes cartCountPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);

        // Listen for custom cart update events
        document.addEventListener('cartUpdated', (event) => {
            if (event.detail && event.detail.count !== undefined) {
                this.updateCartCount(event.detail.count);
            } else {
                this.refreshCartCount();
            }
        });

       // console.log('Cart updater initialized successfully');
    }
};

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    CartUpdater.init();
});

// Expose global function for backward compatibility
window.updateCartCount = function(count) {
    CartUpdater.updateCartCount(count);
};

window.refreshCartCount = function() {
    CartUpdater.refreshCartCount();
};

window.addToCartWithUpdate = function(productId, quantity = 1) {
    CartUpdater.addToCart(productId, quantity);
};

// Test function for debugging
// window.testCartUpdate = function(count = 5) {
//     console.log('Testing cart update with count:', count);
//     CartUpdater.updateCartCount(count);
// };
