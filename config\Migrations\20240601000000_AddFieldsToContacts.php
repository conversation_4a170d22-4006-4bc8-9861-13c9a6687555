<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddFieldsToContacts extends AbstractMigration
{
    /**
     * Change Method.
     *
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('contacts');
        $table->addColumn('status', 'string', [
            'default' => 'Pending',
            'limit' => 20,
            'null' => false,
        ]);
        $table->addColumn('deleted', 'boolean', [
            'default' => false,
            'null' => false,
        ]);
        $table->addColumn('deleted_date', 'datetime', [
            'default' => null,
            'null' => true,
        ]);
        $table->update();
    }
}