<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\SiteSetting $siteSetting
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/bootstrap-colorpicker/dist/css/bootstrap-colorpicker.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Site Settings") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'SiteSettings', 'action' => 'index']) ?>"><?= __("Global Settings") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Edit") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #206331"><?= __("Global Setting") ?></h6>
            <?= $this->Form->create($siteSetting) ?>
            <div class="form-group row">
                <label for="site_title" class="col-sm-2 col-form-label fw-bold"><?= __("Site Title") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('site_title', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'site_title',
                        'placeholder' => __('Site Title'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="address_line1" class="col-sm-2 col-form-label fw-bold"><?= __("Address Line 1") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('address_line1', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'address_line1',
                        'placeholder' => __('Address Line 1'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="address_line2" class="col-sm-2 col-form-label fw-bold"><?= __("Address Line 2") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('address_line2', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'address_line2',
                        'placeholder' => __('Address Line 2'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="country" class="col-sm-2 col-form-label fw-bold"><?= __("Country") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('country', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'country',
                        'placeholder' => __('Country'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="state" class="col-sm-2 col-form-label fw-bold"><?= __("State") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('state', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'state',
                        'placeholder' => __('State'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="city" class="col-sm-2 col-form-label fw-bold"><?= __("City") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('city', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'city',
                        'placeholder' => __('City'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="zipcode" class="col-sm-2 col-form-label fw-bold"><?= __("Zip Code") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('zipcode', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'zipcode',
                        'placeholder' => __('Zip Code'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="customer_support_no"
                    class="col-sm-2 col-form-label fw-bold"><?= __("Customer Support No") ?>
                    <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('customer_support_no', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'customer_support_no',
                        'placeholder' => __('Customer Support No'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="contact_no" class="col-sm-2 col-form-label fw-bold"><?= __("Contact No") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('contact_no', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'contact_no',
                        'placeholder' => __('Contact No'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="support_email" class="col-sm-2 col-form-label fw-bold"><?= __("Support Email") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('support_email', [
                        'type' => 'email',
                        'class' => 'form-control',
                        'id' => 'support_email',
                        'placeholder' => __('Support Email'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="admin_email" class="col-sm-2 col-form-label fw-bold"><?= __("Admin Email") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('admin_email', [
                        'type' => 'email',
                        'class' => 'form-control',
                        'id' => 'admin_email',
                        'placeholder' => __('Admin Email'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="business_open_time" class="col-sm-2 col-form-label fw-bold"><?= __("Business Open Time") ?>
                    <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('business_open_time', [
                        'type' => 'time',
                        'class' => 'form-control',
                        'id' => 'business_open_time',
                        'placeholder' => __('Business Open Time'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="business_close_time"
                    class="col-sm-2 col-form-label fw-bold"><?= __("Business Close Time") ?>
                    <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('business_close_time', [
                        'type' => 'time',
                        'class' => 'form-control',
                        'id' => 'business_close_time',
                        'placeholder' => __('Business Close Time'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="company_logo" class="col-sm-2 col-form-label fw-bold"><?= __("Company Logo") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('company_logo', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'company_logo',
                        'placeholder' => __('Company Logo'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="fav_icon" class="col-sm-2 col-form-label fw-bold"><?= __("Fav Icon") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('fav_icon', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'fav_icon',
                        'placeholder' => __('Fav Icon'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="facebook_url" class="col-sm-2 col-form-label fw-bold"><?= __("Facebook URL") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('facebook_url', [
                        'type' => 'url',
                        'class' => 'form-control',
                        'id' => 'facebook_url',
                        'placeholder' => __('Facebook URL'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="twitter_url" class="col-sm-2 col-form-label fw-bold"><?= __("Twitter URL") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('twitter_url', [
                        'type' => 'url',
                        'class' => 'form-control',
                        'id' => 'twitter_url',
                        'placeholder' => __('Twitter URL'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="pinterest_url" class="col-sm-2 col-form-label fw-bold"><?= __("Pinterest URL") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('pinterest_url', [
                        'type' => 'url',
                        'class' => 'form-control',
                        'id' => 'pinterest_url',
                        'placeholder' => __('Pinterest URL'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="youtube_url" class="col-sm-2 col-form-label fw-bold"><?= __("YouTube URL") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('youtube_url', [
                        'type' => 'url',
                        'class' => 'form-control',
                        'id' => 'youtube_url',
                        'placeholder' => __('YouTube URL'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="instagram_url" class="col-sm-2 col-form-label fw-bold"><?= __("Instagram URL") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('instagram_url', [
                        'type' => 'url',
                        'class' => 'form-control',
                        'id' => 'instagram_url',
                        'placeholder' => __('Instagram URL'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="linkedin_url" class="col-sm-2 col-form-label fw-bold"><?= __("LinkedIn URL") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('linkedin_url', [
                        'type' => 'url',
                        'class' => 'form-control',
                        'id' => 'linkedin_url',
                        'placeholder' => __('LinkedIn URL'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="linkedin_url" class="col-sm-2 col-form-label fw-bold"><?= __("Pagination Counts") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('pagination_count', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'options' => $paginationCounts,
                        'label' => false,
                        'empty' => __('Select a count'),
                    ]); ?>
                </div>
            </div>
             <div class="form-group row">
                <label for="deliver_charge" class="col-sm-2 col-form-label fw-bold"><?= __("Delivery Charge") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('delivery_charge', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'delivery_charge',
                        'placeholder' => __('Delivery Charge'),
                        'label' => false,
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="product_return_in_days" class="col-sm-2 col-form-label fw-bold"><?= __("Product Return In Days") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('product_return_in_days', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'product_return_in_days',
                        'placeholder' => __('Product Return In Days'),
                        'label' => false,
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="product_cancel_in_days" class="col-sm-2 col-form-label fw-bold"><?= __("Product Cancel In Days") ?></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('product_cancel_in_days', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'product_cancel_in_days',
                        'placeholder' => __('Product Cancel In Days'),
                        'label' => false,
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="credit-min-limit" class="col-sm-2 col-form-label fw-bold"><?= __("Credit Minimum Limit") ?></label>
                <div class="col-sm-9 main-field">
                    <div class="input-group currency-container">
                        <?php echo $this->Form->control('credit_min_limit', [
                            'type' => 'number',
                            'class' => 'form-control',
                            'id' => 'credit-min-limit',
                            'placeholder' => __('Credit Minimum Limit'),
                            'label' => false,
                        ]); ?>
                        <div class="currency-block" id="discount-suffix">
                            <?= __(h($currencySymbol)) ?>
                        </div>

                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label for="express-delivery-order-cutoff-time" class="col-sm-2 col-form-label fw-bold"><?= __("Express Delivery Order Cutoff-time") ?><sup
                class="text-danger font-11">*</sup></label>
                <div class="col-sm-9 main-field">
                    <?php echo $this->Form->control('express_delivery_order_cutoff_time', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'express-delivery-order-cutoff-time',
                        'placeholder' => __('Express Delivery Order Cutoff-time'),
                        'label' => false,
                    ]); ?>
                </div>
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary"><?= __("Save") ?></button>
            </div>
        </div>
    </div>
</div>
</div>