<!-- jQ<PERSON><PERSON> and jQuery Validation Plugin -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        #toast-message-container .alert {
            transition: none !important;
            opacity: 1 !important;
        }
    </style>
<!-- Toast Message Container -->
<div id="toast-message-container" style="position: fixed; top: 80px; right: 20px; z-index: 9999; max-width: 350px;"></div>

<div class="row login-admin-form">
    <div class="col-md-6"></div>
    <div class="col-md-6">
        
        <a class="navbar-brand login-form" href="#">
            <img class="m-auto img-fluid" src="<?= $this->Url->webroot('img/ozone/Ozonex_Logo_4.png') ?>" />
        </a>

        <div class="store-card">
            <h4 class="frg-pass">Forgot Password?</h4>
            <p>
                Please enter the email address you signed up
                with and we will send you a password reset link.
            </p>
            <?= $this->Form->create(null, ['url' => ['controller' => 'Customer','action' => 'forgotPassword'],'type' => 'post']) ?>
            <div class="input-border">
                <input type="email" name="email" class="text" required="" />
                <label>Email</label>
                <div class="text-center my-3 email_error" style="color:orange;display:none;font-size:16px;"></div>
                <div class="border"></div>
                <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'home']) ?>" class="forgot_pwd"><span style="width: 12%;">Login</span></a>
            </div>
            <input type="submit" id="forgot_password" class="btn" value="Submit" />
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('[type="email"]').on('keyup', function () {
            var email = $(this).val().trim();
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (email === '') {
                $('.email_error').text('Email is required.').show();
                $('#forgot_password').prop('disabled',true);
            } else if (!emailRegex.test(email)) {
                $('.email_error').text('Please enter a valid email address.').show();
                $('#forgot_password').prop('disabled',true);
            } else {
                $('.email_error').text('').show();
                $('#forgot_password').prop('disabled',false);
            }
        });
    });
</script>
<?php $session = $this->request->getSession(); ?>
<script>
    $(document).ready(function() {
        // Check for toast message from session
        <?php
        $toastMessage = $session->read('toast_message');
        if ($toastMessage):
            // Clear the message from session after reading
            $session->delete('toast_message');
        ?>
        showToastMessage('<?= h($toastMessage['message']) ?>', '<?= h($toastMessage['type']) ?>');
        <?php endif; ?>
    });

    function showToastMessage(message, type) {
        console.log('Toast triggered:', message, type);
        // Create message container with enhanced styling
        const messageContainer = $(`
            <div class="alert alert-dismissible fade show" style="
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                border: none;
                animation: slideInRight 0.3s ease-out;
            "></div>
        `);

        // Set message type and styling
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        messageContainer.addClass(alertClass);

        // Add message content with icon
        const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        messageContainer.html(`
            <div class="alert-body d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <span><b>${message}</b></span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.alert').fadeOut(300, function(){ $(this).remove(); })"></button>
            </div>
        `);

        // Add to container and show with animation
        $('#toast-message-container').append(messageContainer);

        // Auto hide after 4 seconds
        setTimeout(() => {
            messageContainer.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }
</script>