<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Http\ServerRequestFactory;

/**
 * Brands Model
 *
 * @property \App\Model\Table\BannerAdsTable&\Cake\ORM\Association\HasMany $BannerAds
 * @property \App\Model\Table\BrandCategoryMappingsTable&\Cake\ORM\Association\HasMany $BrandCategoryMappings
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\HasMany $Products
 *
 * @method \App\Model\Entity\Brand newEmptyEntity()
 * @method \App\Model\Entity\Brand newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Brand> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Brand get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Brand findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Brand patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Brand> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Brand|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Brand saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Brand>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Brand>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Brand>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Brand> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Brand>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Brand>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Brand>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Brand> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class BrandsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    protected $language;
    protected $country;
    protected $country_id;

    public function initialize(array $config): void
    {
        parent::initialize($config);
        $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;

        $this->setTable('brands');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->hasMany('BannerAds', [
            'foreignKey' => 'brand_id',
        ]);
        $this->hasMany('BrandCategoryMappings', [
            'foreignKey' => 'brand_id',
        ]);
        $this->hasMany('Products', [
            'foreignKey' => 'brand_id',
        ]);

        $this->belongsToMany('Categories', [
            'joinTable' => 'brand_category_mappings',
            'foreignKey' => 'brand_id',
            'targetForeignKey' => 'category_id',
            'through' => 'BrandCategoryMappings', // specifies the table for the join
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('url_key')
            ->maxLength('url_key', 255)
            ->allowEmptyString('url_key');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('brand_logo')
            ->maxLength('brand_logo', 255)
            ->allowEmptyString('brand_logo');

        // $validator
        //     ->scalar('web_banner')
        //     ->maxLength('web_banner', 255)
        //     ->allowEmptyString('web_banner');

        $validator
            ->scalar('mobile_banner')
            ->maxLength('mobile_banner', 255)
            ->allowEmptyString('mobile_banner');

        $validator
            ->boolean('is_featured')
            ->allowEmptyString('is_featured');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 255)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_keyword')
            ->maxLength('meta_keyword', 255)
            ->allowEmptyString('meta_keyword');

        $validator
            ->scalar('meta_description')
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    //S
    public function getBrandSuggestions($query)
    {
        return $this->find('all')
            ->select(['id', 'name', 'type' => "'brand'"])
            ->where(['name LIKE' => '%' . $query . '%'])
            ->limit(10)
            ->toArray();
    }


    public function getBrandList($category_id = null)
    {
        $query = $this->find();

        if (!empty($category_id)) {
            $query = $query->matching('BrandCategoryMappings', function ($q) use ($category_id) {
                return $q->where(['BrandCategoryMappings.category_id' => $category_id]);
            });
        }

        $query = $query
            ->where(['Brands.status' => 'A'])
            ->select($this);

        $brands = $query
            ->distinct(['Brands.id'])
            ->all();

        return $brands;
    }

    public function GetBrands()
    {
        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');
        if ($isArabic) {
            $select_col = [
                'id',
                'name' => 'Brands.name_ar',
                'description' => 'Brands.description_ar',
                'brand_logo',
                'web_banner'
            ];
        } else {
            $select_col = [
                'id',
                'name' => 'Brands.name',
                'description' => 'Brands.description',
                'brand_logo',
                'web_banner'
            ];
        }
        $order = ['Brands.name' => 'ASC'];

        $condition  = [
            'Brands.status' => 'A',
            'Brands.country_id' => $this->country_id,
            ];

        $brands = $this->find()
            ->select($select_col)
            ->where($condition)
            ->order($order)
            ->disableHydration()
            ->toArray();

        return $brands;
    }
}
