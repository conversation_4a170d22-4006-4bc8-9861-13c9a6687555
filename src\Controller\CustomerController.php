<?php

declare(strict_types=1);

namespace App\Controller;
use App\Controller\AppController;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\Time;
use Cake\I18n\FrozenTime;
use App\Mailer\UserMailer;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use League\OAuth2\Client\Provider\Google;

class CustomerController extends AppController
{
    protected $Users;
    protected $Customers;

    public function initialize(): void
    {
        parent::initialize();
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->viewBuilder()->setLayout('customer');
        $this->loadComponent('Global');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'login', 'logout','sendEmailOtp','googleLogin','googleCallback']);
    }

    public function login()
    {
        $this->viewBuilder()->setLayout('auth');
        $this->request->allowMethod(['get', 'post']);
        $result = $this->Authentication->getResult();

        if ($this->request->is('post')) {
            if ($result->isValid()) {
                $customer = $result->getData();
                
                if ($customer->user_type === 'Admin') {
                    return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => __('Admins have a different Login Page.')]));
                } else {
                    $this->request->getSession()->write('Auth.User', $customer);
                    return $this->response->withType('application/json')->withStringBody(json_encode(['success' => true, 'message' => __('Login Successful.')]));
                }
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => __('Invalid Email or Password.')]));
            }
        }
    }


    public function logout()
    {   
        $this->request->allowMethod(['get', 'post']);
        $result = $this->Authentication->getResult();
        if ($result->isValid()) {
            $this->Authentication->logout();
            return $this->redirect(['controller' => 'Home', 'action' => 'home']);
        } else {
            $this->Flash->error('Logout failed. Please try again.');
            return $this->redirect(['controller' => 'Home', 'action' => 'home']);
        }
    }

    public function forgotPassword()
    {
        $this->viewBuilder()->setLayout('auth');

        if ($this->request->is('post')) {
            $email = $this->request->getData('email');
            $user = $this->Users->findByEmail($email)->first();

            if ($user) {
                $token = Security::hash(Security::randomBytes(25));
                $token = $token;
                $user->password_reset_token = $token;
                $user->token_created_at = date('Y-m-d H:i:s');

                if ($this->Users->save($user)) {
                    $resetLink = Router::url(['controller' => 'Customer', 'action' => 'resetPassword', $token], true);

                    $to = trim($user->email);
                    $subject = "Reset your Password for Ozone";
                    $template = "forgot_password";

                    $viewVars = array('resetLink' => $resetLink, 'token' => $token, 'userId' => $user->id, 'username' => $user->first_name . ' ' . $user->last_name, 'datetime' => date('d-m-Y H:i:s'));

                    $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                    if ($send_email) {
                        $this->request->getSession()->write('toast_message', [
                            'message' => __('Please check your email to reset your password.'),
                            'type' => 'success'
                        ]);
                    } else {
                        $this->request->getSession()->write('toast_message', [
                            'message' => __('Unable to send the password reset email. Please try again.'),
                            'type' => 'error'
                        ]);
                    }

                } else {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('Unable to send the password reset email. Please try again.'),
                        'type' => 'error'
                    ]);
                }
            } else {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Email address not found. Please try again.'),
                    'type' => 'error'
                ]);
            }
            return $this->redirect(['controller' => 'Customer', 'action' => 'forgotPassword']);
        }
    }

    public function resetPassword($token = null)
    {
        $this->viewBuilder()->setLayout('auth');

        if (!$token) {
            $this->request->getSession()->write('toast_message', [
                'message' => __('Invalid token. Please try again.'),
                'type' => 'error'
            ]);
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        $user = $this->Users->find('all', [
            'conditions' => [
                'password_reset_token' => $token,
                'token_created_at >' => FrozenTime::now()->modify('-1 hours')
            ]
        ])->first();

        if (!$user) {
            $this->request->getSession()->write('toast_message', [
                'message' => __('Invalid or expired token. Please try again.'),
                'type' => 'error'
            ]);
            return $this->redirect(['controller' => 'Customer', 'action' => 'forgotPassword']);
        }

        if ($this->request->is(['post', 'put'])) {
            $password = $this->request->getData('password');
            $confirmPassword = $this->request->getData('confirm_password');

            if ($password !== $confirmPassword) {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Passwords do not match. Please try again.'),
                    'type' => 'error'
                ]);
            } else {
                $user->password = $password;
                $user->password_reset_token = null;
                $user->token_created_at = null;

                if ($this->Users->save($user)) {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('Your password has been updated successfully.'),
                        'type' => 'success'
                    ]);
                } else {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('Unable to update your password. Please try again.'),
                        'type' => 'error'
                    ]);
                }
            }
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        $this->set(compact('token'));
    }

    public function signup()
    {
        $this->viewBuilder()->setLayout('auth');
        $this->request->allowMethod(['post']);
        $data = $this->request->getData();

        $user = $this->Users->findByEmail($data['email'])->first();
        if ($user) {
            $nameParts = explode(' ', $data['name'], 2);
            $first_name = trim($nameParts[0]);
            $last_name = trim($nameParts[1] ?? '');
            $user->first_name = $first_name;
            $user->last_name = $last_name;
            $user->password = $data['password'];
            $user->status = 'A';

            if($this->Users->save($user)){
                $lastInsertedId = $user->id;
                $customer = $this->Customers->newEmptyEntity();
                $customer->user_id = $lastInsertedId;

                if ($this->Customers->save($customer)) {

                    $to = trim($data['email']);
                    $subject = "Welcome to Ozone";
                    $template = "welcome_user";

                    $viewVars = array('username' => $customer->first_name . ' ' . $customer->last_name, 'email' => $data['email']);

                    $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                    if ($send_email) {
                        return $this->response->withType('application/json')->withStringBody(json_encode(['success' => true, 'message' => 'Registration successful, Please login to you account.']));
                    } else {
                        return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'User could not be saved, Please try again!']));
                    }
                } else {
                    return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'Registration Failed, please try again.']));
                }
            }
        }
        else{
            return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'Email not verified, please verify Email.']));
        }
    }

    public function sendEmailOtp()
    {
        $this->viewBuilder()->setLayout('auth');
        $this->request->allowMethod(['post']);

        $email = $this->request->getData('email');
        $user = $this->Users->findByEmail($email)->first();

            if (!$user) {
                $user = $this->Users->newEmptyEntity();
                $user->email = $email;
                $user->user_type = 'Customer';
                $user->role_id = 2;
                $otp = rand(100000, 999999);
                //$otp = '12345';
                $user->token = $otp;
                $user->token_created_at = date('Y-m-d H:i:s');

                if ($this->Users->save($user)) {
                    $to = trim($email);
                    $subject = "Email Verification OTP";
                    $template = "customer_otp";
                    $viewVars = array('otp' => $otp);
                    $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                    if ($send_email) {
                        return $this->response->withType('application/json')->withStringBody(json_encode(['success' => true, 'message' => 'Please check your email to verify your account.']));
                    } else {
                        return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'Failed to send OTP. Please try again later.']));
                    }
                } else {
                    return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'Failed to save OTP. Please try again later.']));
                }
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'User already Exists.']));
            }
    }


    public function verifyEmail()
    {
        if ($this->request->is('post')) {
            $email = $this->request->getData('email');
            $verification_code = $this->request->getData('verification_code');
            $user = $this->Users->findByEmail($email)->first();

            if ($user && $user->token == $verification_code && $user->token_created_at > date('Y-m-d H:i:s', strtotime('-10 minutes'))) {
                $user->is_email_verified = 1;
                $user->token = null;

                if ($this->Users->save($user)) {
                    return $this->response->withType('application/json')->withStringBody(json_encode(['success' => true, 'message' => 'Your email has been successfully verified.']));
                } else {
                    return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'Email verification failed.']));
                }
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode(['success' => false, 'message' => 'Invalid verification link or the link has expired.']));
            }
        }
    }

    public function googleLogin()
    {
        $provider = new Google([
            'clientId'     => '1017234760939-tgmjv7hh81sba2icuf9fukmcibdq7v19.apps.googleusercontent.com',
            'clientSecret' => '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',
            'redirectUri'  => 'http://ozone.com/customer/googleCallback',
        ]);

        $authUrl = $provider->getAuthorizationUrl([
            'scope' => ['email', 'profile']
        ]);

        // Store the state for security
        $this->getRequest()->getSession()->write('oauth2state', $provider->getState());

        return $this->redirect($authUrl);
    }

    public function googleCallback()
    {
        $session = $this->getRequest()->getSession();

        $provider = new Google([
            'clientId'     => '1017234760939-tgmjv7hh81sba2icuf9fukmcibdq7v19.apps.googleusercontent.com',
            'clientSecret' => '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',
            'redirectUri'  => 'http://ozone.com/customer/googleCallback',
        ]);

        $state = $this->getRequest()->getQuery('state');
        $storedState = $session->read('oauth2state');

        if (empty($state) || ($state !== $storedState)) {
            $session->delete('oauth2state');
            $this->request->getSession()->write('toast_message', [
                'message' => __('Invalid Auth State.'),
                'type' => 'error'
            ]);
            return $this->redirect(['controller' => 'Home', 'action' => 'home']);
        }

        try {
            $accessToken = $provider->getAccessToken('authorization_code', [
                'code' => $this->getRequest()->getQuery('code')
            ]);

            // Get user profile
            $googleUser = $provider->getResourceOwner($accessToken);
            $googleData = $googleUser->toArray();

            $email = $googleData['email'] ?? null;
            $googleId = $googleData['sub'] ?? null;

            if (!$email || !$googleId) {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Missing google data.'),
                    'type' => 'error'
                ]);
                return $this->redirect(['controller' => 'Home', 'action' => 'home']);
            }

            // Find or create user
            $user = $this->Users->find()->where(['email' => $email])->first();
            if (!$user) {
                $user = $this->Users->newEntity([
                    'email' => $email,
                    'name' => $googleData['name'] ?? '',
                    'provider' => 'google',
                    'provider_uid' => $googleId,
                    'password' => '', // or a random value
                ]);
                if($this->Users->save($user)){
                    $lastInsertedId = $user->id;
                    $customer = $this->Customers->newEmptyEntity();
                    $customer->user_id = $lastInsertedId;
                    $this->Customers->save($customer);
                }
            }

            $this->request->getSession()->write('Auth.User', $user);
            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);;

        } catch (IdentityProviderException $e) {
            $this->request->getSession()->write('toast_message', [
                    'message' => __('Google Authentication failed.'),
                    'type' => 'error'
            ]);
            return $this->redirect(['controller' => 'Home', 'action' => 'home']);
        }
    }
}