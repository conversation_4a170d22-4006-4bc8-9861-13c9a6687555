<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * OfferProduct Entity
 *
 * @property int $id
 * @property int $offer_id
 * @property int $product_id
 * @property string|null $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Offer $offer
 * @property \App\Model\Entity\Product $product
 */
class OfferProduct extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'offer_id' => true,
        'product_id' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'offer' => true,
        'product' => true,
    ];
}
