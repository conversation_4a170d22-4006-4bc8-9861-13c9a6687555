<?php
/**
 * Country Filter Example Dashboard Template
 * Demonstrates the country filtering system in action
 */
?>

<div class="section-header">
    <h1><?= __('Country Filter Dashboard Example') ?></h1>
    <div class="section-header-breadcrumb">
        <div class="breadcrumb-item active">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'adminDashboard']) ?>">
                <?= __('Dashboard') ?>
            </a>
        </div>
        <div class="breadcrumb-item"><?= __('Country Filter Example') ?></div>
    </div>
</div>

<div class="section-body">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
        
        <!-- Country Filter Status Card -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Current Country Filter Status') ?></h4>
                        <div class="card-header-action">
                            <?php if ($selectedCountryId): ?>
                                <span class="badge badge-success">
                                    <i class="fas fa-filter me-1"></i><?= __('Filtered') ?>
                                </span>
                            <?php else: ?>
                                <span class="badge badge-secondary">
                                    <i class="fas fa-globe me-1"></i><?= __('All Countries') ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong><?= __('Selected Country:') ?></strong> 
                                    <?= $stats['country_name'] ?>
                                </p>
                                <p><strong><?= __('Country ID:') ?></strong> 
                                    <?= $selectedCountryId ?: __('None') ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="text-muted">
                                    <?= __('Use the country dropdown in the header to filter data by country. The selection will persist across all admin pages.') ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card card-statistic-1">
                    <div class="card-icon bg-primary">
                        <i class="far fa-shopping-cart"></i>
                    </div>
                    <div class="card-wrap">
                        <div class="card-header">
                            <h4><?= __('Total Orders') ?></h4>
                        </div>
                        <div class="card-body">
                            <?= number_format($stats['total_orders']) ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card card-statistic-1">
                    <div class="card-icon bg-success">
                        <i class="far fa-users"></i>
                    </div>
                    <div class="card-wrap">
                        <div class="card-header">
                            <h4><?= __('Total Customers') ?></h4>
                        </div>
                        <div class="card-body">
                            <?= number_format($stats['total_customers']) ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card card-statistic-1">
                    <div class="card-icon bg-warning">
                        <i class="far fa-dollar-sign"></i>
                    </div>
                    <div class="card-wrap">
                        <div class="card-header">
                            <h4><?= __('Total Revenue') ?></h4>
                        </div>
                        <div class="card-body">
                            $<?= number_format($stats['total_revenue'], 2) ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card card-statistic-1">
                    <div class="card-icon bg-info">
                        <i class="far fa-globe"></i>
                    </div>
                    <div class="card-wrap">
                        <div class="card-header">
                            <h4><?= __('Filter Status') ?></h4>
                        </div>
                        <div class="card-body">
                            <?= $selectedCountryId ? __('Active') : __('Inactive') ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Implementation Examples -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __('How to Use Country Filtering in Your Controllers') ?></h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><?= __('Basic Usage:') ?></h6>
                                <pre><code class="language-php">// In your controller action
$query = $this->YourModel->find();

// Apply country filter
$query = $this->applyCountryFilter($query, 'YourModel.country_id');

$data = $this->paginate($query);</code></pre>
                            </div>
                            <div class="col-md-6">
                                <h6><?= __('Get Current Filter:') ?></h6>
                                <pre><code class="language-php">// Get selected country ID
$countryId = $this->getCurrentCountryFilter();

if ($countryId) {
    // Country is selected
    $country = $this->Countries->getCountryById($countryId);
}</code></pre>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h6><?= __('Available Helper Methods:') ?></h6>
                            <ul class="list-unstyled">
                                <li><code>getCurrentCountryFilter()</code> - Get current country ID from session</li>
                                <li><code>applyCountryFilter($query, $field)</code> - Apply country filter to query</li>
                                <li><code>Countries->getCountryById($id)</code> - Get country entity by ID</li>
                                <li><code>Countries->getCountriesForDropdown()</code> - Get countries for dropdown</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Test Country Filtering') ?></h4>
                    </div>
                    <div class="card-body">
                        <p><?= __('Try the following actions to test the country filtering system:') ?></p>
                        <div class="btn-group" role="group">
                            <a href="<?= $this->Url->build(['action' => 'orders']) ?>" class="btn btn-primary">
                                <i class="fas fa-shopping-cart me-1"></i><?= __('View Filtered Orders') ?>
                            </a>
                            <a href="<?= $this->Url->build(['action' => 'customers']) ?>" class="btn btn-success">
                                <i class="fas fa-users me-1"></i><?= __('View Filtered Customers') ?>
                            </a>
                            <a href="<?= $this->Url->build(['action' => 'showrooms']) ?>" class="btn btn-info">
                                <i class="fas fa-store me-1"></i><?= __('View Filtered Showrooms') ?>
                            </a>
                            <a href="<?= $this->Url->build(['action' => 'ajaxData']) ?>" class="btn btn-warning" target="_blank">
                                <i class="fas fa-code me-1"></i><?= __('Test AJAX Endpoint') ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Highlight code blocks if Prism.js is available
    if (typeof Prism !== 'undefined') {
        Prism.highlightAll();
    }
});
</script>
