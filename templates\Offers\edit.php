<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Offer $offer
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #redeem-method {
        appearance: auto;
        -webkit-appearance: auto;
        -moz-appearance: auto;
        background-color: #fff;
        padding-right: 30px;
    }

    .coupon-code .input {
        width: 70% !important;
    }

    .is-invalid-select {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }

    .web-media-container {
        position: relative;
        width: 50%;
    }

    .mob-media-container {
        position: relative;
        width: 50%;
    }
</style>

<?php $this->end(); ?>

<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'index']) ?>">
                <?= __('Coupons And Discounts') ?>
            </a>
        </li>
        <li class="breadcrumb-item active">
            <?= __('Edit') ?>
        </li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
    </a>
</div>

<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20"><?= __('Edit Coupon And Discount') ?></h6>
            <?php echo $this->Form->create($offer, ['id' => 'editCouponsandDiscountsForm', 'novalidate' => true, 'type' => 'patch', 'enctype' => 'multipart/form-data']); ?>

            <div class="form-group row">
                <label for="offer-name" class="col-sm-2 col-form-label fw-bold"><?= __('Offer Name') ?><sup
                        class="text-danger font-11">*</sup>
                </label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('offer_name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'offer-name',
                        'placeholder' => __('Enter Offer Name'),
                        'label' => false,
                        'required' => true
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="coupon-id" class="col-sm-2 col-form-label fw-bold"><?= __('Coupon Code') ?><sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-7">
                    <div class="input-group coupon-code">
                        <?php echo $this->Form->control('offer_code', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'coupon-id',
                            'placeholder' => __('Coupon Code'),
                            'label' => false,
                            'required' => true,
                            'aria-describedby' => 'btnCodeRefresh'
                        ]); ?>
                        <button type="button" class="btn btn-outline-secondary" id="btnCodeRefresh"
                            style="height: 42px;">
                            ↻
                        </button>
                    </div>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="offer-description" class="col-sm-2 col-form-label fw-bold"><?= __('Coupon Description') ?>
                </label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('offer_description', [
                        'type' => 'textarea',
                        'class' => 'form-control',
                        'id' => 'offer-description',
                        'placeholder' => __('Coupon Description'),
                        'label' => false
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="start-date-time" class="col-sm-2 col-form-label fw-bold"><?= __('Start Date &
                                Time') ?><sup class="text-danger font-11">*</sup> </label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('offer_start_date', [
                        'type' => 'datetime',
                        'class' => 'form-control',
                        'id' => 'start-date-time',
                        'label' => false,
                        'required' => true
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="end-date-time" class="col-sm-2 col-form-label fw-bold"><?= __('End Date &
                                Time') ?><sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('offer_end_date', [
                        'type' => 'datetime',
                        'class' => 'form-control',
                        'id' => 'end-date-time',
                        'label' => false,
                        'required' => true
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

        

            <div class="form-group row">
                <label for="offer-type" class="col-sm-2 col-form-label fw-bold"><?= __('Offer Type') ?><sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('offer_type', [
                        'type' => 'select',
                        'id' => 'offer-type',
                        'options' => $offerType,
                        'class' => 'form-control',
                        'label' => false,
                        'empty' => __('Select a Offer Type'),
                        'value' => $offer->offer_type,
                        'required' => true
                    ]) ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="discount-amount" class="col-sm-2 col-form-label fw-bold"><?= __('Discount Amount /
                                Percentage') ?><sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-7">
                    <div class="input-group currency-container">
                        <?php echo $this->Form->control('discount', [
                            'type' => 'number',
                            'class' => 'form-control',
                            'id' => 'discount-amount',
                            'placeholder' => __('Discount Amount'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <!-- <div class="currency-block" id="discount-suffix">
                            <?= __(h($currencySymbol)) ?>
                        </div> -->

                    </div>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row" id="max-discount-perc-amount-div" style="display: none;">
                <label for="max-discount-perc-amount" class="col-sm-2 col-form-label fw-bold"><?= __('Maximum Amount for Percent Discount ') ?></label>
                <div class="col-sm-7">
                    <div class="input-group currency-container">
                        <?php echo $this->Form->control('max_amt_per_disc_value', [
                            'type' => 'number',
                            'class' => 'form-control',
                            'id' => 'max-discount-perc-amount',
                            'placeholder' => __('Maximum Amount for Percent Discount in ' . h($currencySymbol)),
                            'label' => false
                        ]); ?>
                        <!-- <div class="currency-block">
                            <?= __(h($currencySymbol)) ?>
                        </div> -->

                    </div>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="min-cart-amount" class="col-sm-2 col-form-label fw-bold"><?= __('Minimum Cart
                                Amount') ?></label>
                <div class="col-sm-7">
                    <div class="input-group currency-container">
                        <?php echo $this->Form->control('min_cart_value', [
                            'type' => 'number',
                            'class' => 'form-control',
                            'id' => 'min-cart-amount',
                            'placeholder' => __('Minimum Cart Amount'),
                            'label' => false
                        ]); ?>
                        <!-- <div class="currency-block">
                            <?= __(h($currencySymbol)) ?>
                        </div> -->
                    </div>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="terms-conditions"
                    class="col-sm-2 col-form-label fw-bold"><?= __('Terms & Conditions') ?></label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('terms_conditions', [
                        'type' => 'textarea',
                        'class' => 'form-control',
                        'id' => 'terms-conditions',
                        'placeholder' => __('Terms & Conditions'),
                        'label' => false
                    ]); ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label for="free-shipping" class="col-sm-2 col-form-label fw-bold"><?= __('Free Shipping') ?></label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('free_shipping', [
                        'type' => 'checkbox',
                        'class' => 'form-check-input',
                        'id' => 'free-shipping',
                        'label' => false,
                        'default' => $offer->free_shipping == 1
                    ]); ?>
                    <div class="invalid-feedback"></div>
                </div>
            </div> -->

            <div class="form-group row">
                <label for="country-id" class="col-sm-2 col-form-label fw-bold"><?= __('Country') ?></label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('country_id', [
                        'type' => 'select',
                        'id' => 'country-id',
                        'options' => $countries,
                        'class' => 'form-control select2',
                        'label' => false,
                        'empty' => __('Select a Country'),
                        'value' => $offer->country_id
                    ]) ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="catgory-id" class="col-sm-2 col-form-label fw-bold"><?= __('Categories') ?></label>
                <div class="col-sm-5">
                    <?php
                    $options = [];
                    foreach ($formattedCategories as $id => $category) {
                        $options[$id] = [
                            'text' => $category['text'],
                            'value' => $id,
                            'data-level' => $category['level']
                        ];
                    }

                    $selectedCategories = [];
                    if (!empty($offer->offer_categories)) {
                        foreach ($offer->offer_categories as $offerCategory) {
                            $selectedCategories[] = $offerCategory->category_id;
                        }
                    }

                    echo $this->Form->select('offer_categories._ids', $options, [
                        'id' => 'category-id',
                        'class' => 'form-control select2',
                        'multiple' => 'multiple',
                        'label' => false,
                        'empty' => __('All'),
                        'value' => $selectedCategories
                    ]);
                    ?>
                    <small class="form-text text-muted"><?= __('Select categories to apply this coupon to all products in these categories') ?></small>

                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="product-id" class="col-sm-2 col-form-label fw-bold"><?= __('Specific Products') ?></label>
                <div class="col-sm-5">
                    <?php
                    $selectedProducts = [];
                    if (!empty($offer->offer_products)) {
                        foreach ($offer->offer_products as $offerProduct) {
                            $selectedProducts[] = $offerProduct->product_id;
                        }
                    }

                    echo $this->Form->select('offer_products._ids', $products, [
                        'id' => 'product-id',
                        'class' => 'form-control select2',
                        'multiple' => 'multiple',
                        'label' => false,
                        'empty' => __('Select Products'),
                        'value' => $selectedProducts
                    ]);
                    ?>
                    <small class="form-text text-muted"><?= __('Select specific products to apply this coupon (optional - use instead of or in addition to categories)') ?></small>

                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __('Status') ?><sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5">
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'id' => 'status',
                        'options' => $statuses,
                        'class' => 'form-control select2',
                        'label' => false,
                        'empty' => __('Select a Status'),
                        'required' => true,
                        'value' => $offer->status
                    ]) ?>
                    <div class="invalid-feedback">
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" id="btnSubmit" class="btn"><?= __('Save') ?></button>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";
    const swalInvalidDimensions = "<?= addslashes(__('Image dimensions should be between')); ?>";
</script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>">
</script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>


<script>
    $(document).ready(function() {

        var now = new Date().toISOString().slice(0, 16);


        var startDate = $('#start-date-time').val();
        $('#end-date-time').attr('min', startDate);


        $('#start-date-time').on('change', function() {
            $('#end-date-time').val('');
            var startDate = $(this).val();
            $('#end-date-time').attr('min', startDate);
        });

        $('.select2').select2({
            minimumResultsForSearch: 0
        });

        $('#customer-group').select2({
            allowClear: false,
            multiple: true,
            width: '100%'
        });

        $('#showroom-id').select2({
            allowClear: false,
            multiple: true,
            width: '100%'
        });

        function toggleShowrooms() {
            var offerType = $('#redeem-method').val();
            if (offerType === 'Store' || offerType === 'Both') {
                $('#divShowrooms').show();
            } else {
                $('#divShowrooms').hide();
            }
        }

        toggleShowrooms();

        $('#redeem-method').change(function() {
            toggleShowrooms();
        });

        function validateForm() {
            let isValid = true;

            $('#editCouponsandDiscountsForm').find('input[required], select[required]').each(function() {
                let value = $(this).val().trim();
                let isSelect2 = $(this).hasClass('select2');

                if (value === '') {
                    $(this).addClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                    feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                    isValid = false;
                    if (isSelect2) {
                        $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    feedback.hide();
                    if (isSelect2) {
                        $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                    }
                }
            });

            if (isValid) {

                let couponCode = $('#coupon-id').val().trim();
                if (couponCode.length > 10) {
                    $('#coupon-id').addClass('is-invalid');
                    let feedback = $('#coupon-id').closest('.form-group').find('.invalid-feedback');
                    feedback.text('<?= __('Coupon Code cannot be more than 10 characters') ?>.').show();
                    isValid = false;
                } else if (couponCode.length < 5) {
                    $('#coupon-id').addClass('is-invalid');
                    let feedback = $('#coupon-id').closest('.form-group').find('.invalid-feedback');
                    feedback.text('<?= __('Coupon Code cannot be less than 5 characters') ?>.').show();
                    isValid = false;
                } else {
                    $('#coupon-id').removeClass('is-invalid');
                    let feedback = $('#coupon-id').closest('.form-group').find('.invalid-feedback');
                    feedback.hide();
                }


                let offer_type = $('#offer-type').val().trim();
                let discount_amount = $('#discount-amount').val().trim();

                if (offer_type === 'Percentage') {
                    if (discount_amount < 1 || discount_amount > 100) {
                        $('#discount-amount').addClass('is-invalid');
                        let feedback = $('#discount-amount').closest('.form-group').find('.invalid-feedback');
                        feedback.text('<?= __('Discount percentage must be between 1 and 100') ?>.').show();
                        isValid = false;
                    } else {
                        $('#discount-amount').removeClass('is-invalid');
                        let feedback = $('#discount-amount').closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                }
            }
            return isValid;
        }

        $('#btnSubmit').click(function(event) {
            event.preventDefault();
            if (!validateForm()) {
                return;
            }
            var form = $('#editCouponsandDiscountsForm')[0];
            form.action = "<?= $this->Url->build(['controller' => 'Offers', 'action' => 'edit', $offer->id]) ?>";
            $('#btnSubmit').attr('disabled', true);
            form.submit();
        });


        $('#btnCodeRefresh').click(function(event) {
            event.preventDefault();
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Offers', 'action' => 'getUniqueCode']) ?>",
                type: 'GET',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#coupon-id').val(response.code);
                    } else {
                        swal('<?= __('Failed') ?>', '<?= __('Failed to fetch coupon code. Please try again.') ?>', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch coupon code. Please try again.') ?>', 'error');
                    console.error('Error fetching coupon code:', error);
                }
            });
        });

        $('#web-image').on('change', async function() {
            let isValid = false;
            $('.web-media-container').empty();


            isValid = await asyncvalidateFile(
                this,
                <?= $webImageSize ?> * 1024 * 1024,
                <?= json_encode($webImageType) ?>,
                <?= $webImageMinWidth ?>,
                <?= $webImageMaxWidth ?>,
                <?= $webImageMinHeight ?>,
                <?= $webImageMaxHeight ?>
            );


            if (isValid) {
                renderMediaPreview();
            }
        });

        $('#mobile-image').on('change', async function() {
            let isValid = false;
            $('.mob-media-container').empty();
            isValid = await asyncvalidateFile(
                this,
                <?= $mobImageSize ?> * 1024 * 1024,
                <?= json_encode($mobImageType) ?>,
                <?= $mobImageMinWidth ?>,
                <?= $mobImageMaxWidth ?>,
                <?= $mobImageMinHeight ?>,
                <?= $mobImageMaxHeight ?>
            );

            if (isValid) {
                renderMediaPreview();
            }
        });

        $('.delete-img-btn').on('click', function() {
            var $button = $(this);
            var imageId = $button.data('id');
            var imageType = $button.data('type');

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Offers', 'action' => 'deleteImage']) ?>",
                type: 'POST',
                dataType: 'json',
                data: {
                    image_type: imageType,
                    image_id: imageId
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        if (imageType === 'web') {
                            $('#existing_web_image').val('');
                            $('.web-image-container').remove();
                        } else if (imageType === 'mobile') {
                            $('#existing_mobile_image').val('');
                            $('.mobile-image-container').remove();
                        }

                        swal('Success', response.message, 'success');
                    } else {
                        swal('Failed', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    swal('Failed', 'Failed to delete image. Please try again.', 'error');
                }
            });
        });

        $('#web-image').on('click', function(e) {
            var existingImage = $('#existing_web_image').val();
            if (existingImage !== '' && existingImage !== undefined) {
                e.preventDefault();
                swal({
                    title: 'Existing Image Found',
                    text: 'You already have an existing image. Please delete the current image before uploading a new one.',
                    icon: 'warning',
                    buttons: {
                        cancel: 'Cancel',
                        confirm: 'Delete Image and Upload New'
                    },
                }).then((willDelete) => {
                    if (willDelete) {

                        $('.web-image').trigger('click');
                        $('#existing_web_image').val('');
                        $('#web-image').trigger('click');
                    }
                });
            }
        });

        $('#mobile-image').on('click', function(e) {
            var existingImage = $('#existing_mobile_image').val();
            if (existingImage !== '' && existingImage !== undefined) {
                e.preventDefault();
                swal({
                    title: 'Existing Image Found',
                    text: 'You already have an existing image. Please delete the current image before uploading a new one.',
                    icon: 'warning',
                    buttons: {
                        cancel: 'Cancel',
                        confirm: 'Delete Image and Upload New'
                    },
                }).then((willDelete) => {
                    if (willDelete) {

                        $('.mobile-image').trigger('click');
                        $('#existing_mobile_image').val('');
                        $('#mobile-image').trigger('click');
                    }
                });
            }
        });

        $('#offer-type').on('change', function() {
            var selectedValue = $(this).val();
            if (selectedValue === 'Percentage') {
                $('#discount-suffix').text('%');
                $('#max-discount-perc-amount-div').show();
            } else {
                var currencySymbol = "<?= __(h($currencySymbol)) ?>";
                $('#discount-suffix').text(currencySymbol);
                $('#max-discount-perc-amount-div').hide();
            }
        });

        $('#offer-type').trigger('change');

        function renderMediaPreview() {

            $('.web-media-container').empty();
            $('.mob-media-container').empty();
            const webFile = $('#web-image')[0].files[0];
            const mobileFile = $('#mobile-image')[0].files[0];

            if (webFile) {
                const webReader = new FileReader();
                const webfileName = $('#web-image')[0].files[0].name;
                webReader.onload = function(e) {
                    $('.web-media-container').html(`<img src="${e.target.result}" alt="Web Media Preview" style="max-width:100%; height:auto;">
                    <span class="image-name" title="Web Media Preview">${webfileName}</span>
                    <button type="button" class="delete-img-btn delete-web-media">
                    <i class="fas fa-times"></i>
                    </button>
                    `);
                };
                webReader.readAsDataURL(webFile);
            }

            if (mobileFile) {
                const mobileReader = new FileReader();
                const mobilefileName = $('#mobile-image')[0].files[0].name;
                mobileReader.onload = function(e) {
                    $('.mob-media-container').html(`
                    <img src="${e.target.result}" alt="Mobile Media Preview" style="max-width:100%; height:auto;">
                    <span class="image-name" title="Mobile Media Preview" >${mobilefileName}</span>
                    <button type="button" class="delete-img-btn delete-mob-media">
                    <i class="fas fa-times"></i>
                    </button>
                    `);
                };
                mobileReader.readAsDataURL(mobileFile);
            }
        }

        $(document).on('click', '.delete-web-media', function(event) {
            $('.web-media-container').empty();
            $('#web-image').val('');
        });

        $(document).on('click', '.delete-mob-media', function(event) {
            $('.mob-media-container').empty();
            $('#mobile-image').val('');
        });
    });
</script>
<?php $this->end(); ?>