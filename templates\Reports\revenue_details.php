<?php
/**
 * Revenue Report Details
 */
?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= __('Revenue Report Details') ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <?= $this->Html->link(__('Dashboard'), ['controller' => 'Dashboards', 'action' => 'adminDashboard']) ?>
                    </li>
                    <li class="breadcrumb-item">
                        <?= $this->Html->link(__('Reports'), ['action' => 'salesReports']) ?>
                    </li>
                    <li class="breadcrumb-item active"><?= __('Revenue Details') ?></li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <?= $this->Html->link(
                '<i class="fas fa-arrow-left me-2"></i>' . __('Back to Reports'),
                ['action' => 'salesReports'],
                ['class' => 'btn btn-secondary', 'escape' => false]
            ) ?>
            <?= $this->Html->link(
                '<i class="fas fa-download me-2"></i>' . __('Export CSV'),
                ['action' => 'exportRevenue'] + $filters,
                ['class' => 'btn btn-success', 'escape' => false]
            ) ?>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i><?= __('Filter Revenue Data') ?>
            </h6>
        </div>
        <div class="card-body">
            <?= $this->Form->create(null, [
                'type' => 'get',
                'class' => 'row g-3',
                'id' => 'revenueFiltersForm'
            ]) ?>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Date From') ?></label>
                    <?= $this->Form->control('date_from', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_from'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Date To') ?></label>
                    <?= $this->Form->control('date_to', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_to'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Country') ?></label>
                    <?= $this->Form->control('country_id', [
                        'type' => 'select',
                        'options' => ['' => __('All Countries')] + array_column($countries, 'name', 'id'),
                        'class' => 'form-select',
                        'value' => $filters['country_id'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Status') ?></label>
                    <?= $this->Form->control('status', [
                        'type' => 'select',
                        'options' => [
                            '' => __('All Statuses'),
                            'Pending' => __('Pending'),
                            'Confirmed' => __('Confirmed'),
                            'Processing' => __('Processing'),
                            'Shipped' => __('Shipped'),
                            'Out for Delivery' => __('Out for Delivery'),
                            'Delivered' => __('Delivered'),
                            'Rejected' => __('Rejected')
                        ],
                        'class' => 'form-select',
                        'value' => $filters['status'],
                        'label' => false
                    ]) ?>
                </div>
                <!-- <div class="col-md-2">
                    <label class="form-label"><?= __('Order Type') ?></label>
                    <?= $this->Form->control('order_type', [
                        'type' => 'select',
                        'options' => [
                            '' => __('All Types'),
                            'Online' => __('Online'),
                            'Showroom' => __('Showroom')
                        ],
                        'class' => 'form-select',
                        'value' => $filters['order_type'],
                        'label' => false
                    ]) ?>
                </div> -->
                <div class="col-md-2">
                    <label class="form-label"><?= __('Per Page') ?></label>
                    <?= $this->Form->control('limit', [
                        'type' => 'select',
                        'options' => [
                            '25' => '25',
                            '50' => '50',
                            '100' => '100',
                            '250' => '250'
                        ],
                        'class' => 'form-select',
                        'value' => $filters['limit'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i><?= __('Apply Filters') ?>
                    </button>
                    <?= $this->Html->link(
                        '<i class="fas fa-undo me-2"></i>' . __('Reset'),
                        ['action' => 'revenueDetails'],
                        ['class' => 'btn btn-secondary', 'escape' => false]
                    ) ?>
                </div>
            <?= $this->Form->end() ?>
        </div>
    </div>

    <!-- Revenue Data Table -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table me-2"></i><?= __('Revenue Details') ?>
            </h6>
            <div class="text-muted">
                <?= __('Showing {0} orders', count($revenueData)) ?>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="revenueTable">
                    <thead class="table-dark">
                        <tr>
                            <th><?= __('Order #') ?></th>
                            <th><?= __('Date') ?></th>
                            <th><?= __('Customer') ?></th>
                            <th><?= __('Country') ?></th>
                            <th><?= __('Amount') ?></th>
                            <th><?= __('Items') ?></th>
                            <th><?= __('Qty') ?></th>
                            <th><?= __('Payment') ?></th>
                            <!-- <th><?= __('Type') ?></th> -->
                            <th><?= __('Status') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($revenueData)): ?>
                        <tr>
                            <td colspan="10" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-3x mb-3 d-block"></i>
                                <?= __('No revenue data found for the selected criteria') ?>
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php 
                            $totalRevenue = 0;
                            $totalOrders = count($revenueData);
                            $totalItems = 0;
                            $totalQuantity = 0;
                            ?>
                            <?php foreach ($revenueData as $order): ?>
                                <?php 
                                $totalRevenue += $order->total_amount;
                                $totalItems += $order->items_count;
                                $totalQuantity += $order->total_quantity;
                                ?>
                            <tr>
                                <td>
                                    <strong>#<?= h($order->order_number) ?></strong>
                                </td>
                                <td>
                                    <?= $order->order_date->format('M j, Y') ?><br>
                                    <small class="text-muted"><?= $order->order_date->format('H:i') ?></small>
                                </td>
                                <td>
                                    <?= h($order->customer_name) ?><br>
                                    <small class="text-muted"><?= h($order->customer_email) ?></small>
                                </td>
                                <td><?= h($order->country_name) ?></td>
                                <td>
                                    <strong class="text-success">
                                        <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                            <?= $currencyInfo['currency_symbol'] ?> <?= number_format($order->total_amount, 2) ?>
                                        <?php else: ?>
                                            <?= number_format($order->total_amount, 2) ?>
                                        <?php endif; ?>
                                    </strong>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-info"><?= $order->items_count ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-secondary"><?= $order->total_quantity ?></span>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $order->payment_method === 'COD' ? 'warning' : 'primary' ?>">
                                        <?= h($order->payment_method) ?>
                                    </span>
                                </td>
                                <!-- <td>
                                    <span class="badge badge-<?= $order->order_type === 'Online' ? 'primary' : 'secondary' ?>">
                                        <?= h($order->order_type) ?>
                                    </span>
                                </td> -->
                                <td>
                                    <span class="badge badge-<?= $this->element('status_color', ['status' => $order->status]) ?>">
                                        <?= h($order->status) ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                    <?php if (!empty($revenueData)): ?>
                    <tfoot class="table-dark">
                        <tr>
                            <th colspan="4" class="text-end"><?= __('Totals:') ?></th>
                            <th>
                                <strong class="text-success">
                                    <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                        <?= $currencyInfo['currency_symbol'] ?> <?= number_format($totalRevenue, 2) ?>
                                    <?php else: ?>
                                        <?= number_format($totalRevenue, 2) ?>
                                    <?php endif; ?>
                                </strong>
                            </th>
                            <th class="text-center">
                                <span class="badge badge-info"><?= number_format($totalItems) ?></span>
                            </th>
                            <th class="text-center">
                                <span class="badge badge-secondary"><?= number_format($totalQuantity) ?></span>
                            </th>
                            <th colspan="3">
                                <small class="text-muted">
                                    <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                        <?= __('Avg: {0} {1}', [$currencyInfo['currency_symbol'], number_format($totalRevenue / $totalOrders, 2)]) ?>
                                    <?php else: ?>
                                        <?= __('Avg: {0}', [number_format($totalRevenue / $totalOrders, 2)]) ?>
                                    <?php endif; ?>
                                </small>
                            </th>
                        </tr>
                    </tfoot>
                    <?php endif; ?>
                </table>
            </div>

            <!-- Pagination -->
            <?php if (!empty($revenueData) && count($revenueData) >= $filters['limit']): ?>
            <nav aria-label="Revenue pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($filters['page'] > 1): ?>
                    <li class="page-item">
                        <?= $this->Html->link(
                            __('Previous'),
                            ['action' => 'revenueDetails'] + array_merge($filters, ['page' => $filters['page'] - 1]),
                            ['class' => 'page-link']
                        ) ?>
                    </li>
                    <?php endif; ?>
                    
                    <li class="page-item active">
                        <span class="page-link"><?= $filters['page'] ?></span>
                    </li>
                    
                    <?php if (count($revenueData) >= $filters['limit']): ?>
                    <li class="page-item">
                        <?= $this->Html->link(
                            __('Next'),
                            ['action' => 'revenueDetails'] + array_merge($filters, ['page' => $filters['page'] + 1]),
                            ['class' => 'page-link']
                        ) ?>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.badge-primary { background-color: #4e73df; }
.badge-secondary { background-color: #858796; }
.badge-success { background-color: #1cc88a; }
.badge-danger { background-color: #e74a3b; }
.badge-warning { background-color: #f6c23e; color: #000; }
.badge-info { background-color: #36b9cc; }

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.075);
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const filterForm = document.getElementById('revenueFiltersForm');
    const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
    
    filterInputs.forEach(input => {
        if (input.name !== 'limit') { // Don't auto-submit on limit change
            input.addEventListener('change', function() {
                // Reset to page 1 when filters change
                const pageInput = filterForm.querySelector('input[name="page"]');
                if (pageInput) {
                    pageInput.value = 1;
                }
                filterForm.submit();
            });
        }
    });
});
</script>
