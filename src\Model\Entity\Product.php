<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Product Entity
 *
 * @property int $id
 * @property string $url_key
 * @property int $brand_id
 * @property int|null $supplier_id
 * @property string $name
 * @property string $reference_name
 * @property string $description
 * @property string|null $details
 * @property string|null $features
 * @property string|null $features_ar
 * @property string $product_image
 * @property string|null $product_preference
 * @property string $product_size
 * @property string|null $product_weight
 * @property string|null $product_model
 * @property string|null $product_tags
 * @property string|null $sku
 * @property string|null $barcode
 * @property string|null $qrcode
 * @property int $quantity
 * @property string|null $purchase_price
 * @property string|null $product_price
 * @property string|null $sales_price
 * @property string|null $promotion_price
 * @property \Cake\I18n\Date|null $promotion_start_date
 * @property \Cake\I18n\Date|null $promotion_end_date
 * @property int|null $max_buy_limit
 * @property int $COD_in_city
 * @property int $COD_out_city
 * @property int $avl_on_credit
 * @property bool|null $return_allow
 * @property int|null $return_time_period
 * @property string|null $meta_title
 * @property string|null $meta_keyword
 * @property string|null $meta_description
 * @property string|null $catalogue
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Brand $brand
 * @property \App\Model\Entity\Supplier $supplier
 * @property \App\Model\Entity\BannersAd[] $banners_ads
 * @property \App\Model\Entity\CartItem[] $cart_items
 * @property \App\Model\Entity\Inventory[] $inventories
 * @property \App\Model\Entity\OrderItem[] $order_items
 * @property \App\Model\Entity\ProductAttribute[] $product_attributes
 * @property \App\Model\Entity\ProductCategory[] $product_categories
 * @property \App\Model\Entity\ProductImage[] $product_images
 * @property \App\Model\Entity\ProductVariant[] $product_variants
 * @property \App\Model\Entity\ReturnItem[] $return_items
 * @property \App\Model\Entity\Review[] $reviews
 * @property \App\Model\Entity\ShowroomStock[] $showroom_stocks
 * @property \App\Model\Entity\SupplierPurchaseOrderItem[] $supplier_purchase_order_items
 * @property \App\Model\Entity\SupplierStock[] $supplier_stocks
 * @property \App\Model\Entity\WarehouseStock[] $warehouse_stocks
 * @property \App\Model\Entity\Wishlist[] $wishlists
 */
class Product extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        '*' => true,
        'id' => false,
        'installation_charge' => true,
    ];
}
