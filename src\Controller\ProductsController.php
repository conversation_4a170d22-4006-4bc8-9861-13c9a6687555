<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;
use Cake\Routing\Router;
use <PERSON>c<PERSON>er\Barcode\BarcodeGeneratorPNG;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Products Controller
 *
 * @property \App\Model\Table\ProductsTable $Products
 */
class ProductsController extends AppController
{
    protected $Categories;
    protected $ProductImages;
    protected $ProductShowroomPrices;
    protected $AttributeValues;
    protected $Attributes;
    protected $Suppliers;
    protected $Showrooms;
    protected $Warehouses;
    protected $Partners;
    protected $Roles;
    protected $RelatedProducts;
    protected $Brands;
    protected $ProductCategories;
    protected $Users;
    protected $QueuedJobs;
    protected $ProductAttributes;
    protected $ProductVariants;
    protected $ProductVariantImages;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Categories = $this->fetchTable('Categories');
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->ProductShowroomPrices = $this->fetchTable('ProductShowroomPrices');
        $this->AttributeValues = $this->fetchTable('AttributeValues');
        $this->Attributes = $this->fetchTable('Attributes');
        //$this->Suppliers = $this->fetchTable('Suppliers');
        //$this->Showrooms = $this->fetchTable('Showrooms');
        //$this->Warehouses = $this->fetchTable('Warehouses');
        //$this->Partners = $this->fetchTable('Partners');
        $this->Roles = $this->fetchTable('Roles');

        $this->Brands = $this->fetchTable('Brands');
        $this->ProductCategories = $this->fetchTable('ProductCategories');
        $this->Users = $this->fetchTable('Users');
        $this->QueuedJobs = $this->fetchTable('Queue.QueuedJobs');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductVariantImages = $this->fetchTable('ProductVariantImages');
        // $this->RelatedProducts = $this->fetchTable('RelatedProducts');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    public function index()
    {
        // Configure::write('debug', true);
        $query = $this->Products->find()
            ->contain([
                'Brands',
                'ProductCategories' => ['Categories'],
                'ProductImages' => function ($q) {
                    return $q->where([
                        'ProductImages.image_default' => '1', // Select only default images
                        'ProductImages.status' => 'A' // Ensure the image status is 'A'
                    ]);
                },
            ])
            ->order(['Products.name' => 'ASC']);

        if (!empty($brandFilter)) {
            $query->where(['Brands.name LIKE' => '%' . $brandFilter . '%']);
        }

        // Apply country filter if country_id field exists in products table
        // Uncomment the line below if your products table has a country_id field
        $query = $this->applyCountryFilter($query, 'Products.country_id');

        $query->where(['Products.status !=' => 'D']);

        $products = $query->toArray();

        $categories = $this->Products->ProductCategories->Categories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
            'conditions' => ['parent_id IS' => null]
        ])->toArray();

        foreach ($products as $product) {
            $product->deepestCategory = null;
            $product->parentCategory = null;

            if (!empty($product->product_categories)) {
                $product->parentCategory = $product->product_categories[0]->category;

                if (count($product->product_categories) > 1) {
                    $product->deepestCategory = $product->product_categories[0]->category;

                    foreach ($product->product_categories as $productCategory) {
                        if ($productCategory->level < $product->parentCategory->level) {
                            $product->parentCategory = $productCategory->category;
                        }
                        if ($productCategory->level > $product->deepestCategory->level) {
                            $product->deepestCategory = $productCategory->category;
                        }
                    }
                } else {
                    $product->parentCategory = $product->product_categories[0]->category;
                }
            }

            if (!empty($product->product_images)) {
                foreach ($product->product_images as $image) {
                    $product->product_image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        // $user = $this->Authentication->getIdentity();
        // $canApproveProd = false;
        // if (!empty($user)) {
        //     $role = $this->Roles->get($user->role_id);

        //     if (strtolower($role->name) == 'admin') {
        //         $canApproveProd = true;
        //     } else {
        //         $canApproveProd = false;
        //     }
        // }

        $title = 'Manage Products';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';
        $this->set(compact('products', 'title', 'status', 'statusMap', 'decimalSeparator', 'thousandSeparator', 'currencySymbol'));
    }

    public function filterSearch()
    {
        $this->request->allowMethod(['get']);
        $filterBrand = $this->request->getQuery('filterBrand');
        $filterStatus = $this->request->getQuery('filterStatus');
        $approvalStatus = $this->request->getQuery('approvalStatus');

        if ($this->request->is('ajax')) {
            $query = $this->Products->find('all')
                ->contain(['Brands'])->where(['Products.status !=' => 'D']);

            if (!empty($filterBrand)) {
                $query->matching('Brands', function ($q) use ($filterBrand) {
                    return $q->where(['Brands.name LIKE' => '%' . $filterBrand . '%']);
                });
            }

            if (!empty($filterStatus)) {
                $query->where(['Products.status' => $filterStatus]);
            }

            if (!empty($approvalStatus)) {
                $query->where(['Products.approval_status' => $approvalStatus]);
            }

            $query->order(['Products.id' => 'DESC']);

            $products = [];
            $i = 1;
            foreach ($query as $product) {

                $statusMap = [
                    'A' => ['label' => __('Active'), 'class' => 'label-sucess'],
                    'I' => ['label' => __('Inactive'), 'class' => 'label-progress']
                ];

                $status = $statusMap[$product->status] ?? ['label' => __('Unknown'), 'class' => 'label-fail'];

                $approvalMap = [
                    'Pending' => ['label' => __('Pending'), 'class' => 'label-progress'],
                    'Approved' => ['label' => __('Approved'), 'class' => 'label-sucess'],
                    'Rejected' => ['label' => __('Rejected'), 'class' => 'label-fail']
                ];

                $approval_status = $approvalMap[$product->approval_status] ?? ['label' => __('Unknown'), 'class' => 'label-fail'];

                if (!$product->product_image) {
                    $img = '<img alt="image" src="' . Router::url('/img/user.png', true) . '" style="width: 50px; height: auto;">';
                } else {
                    $prodimage = $this->Media->getCloudFrontURL($product->product_image);
                    $img = '<img src="' . Router::url($prodimage, true) . '" style="width: 50px; height: auto;">';
                }

                $actions = '<a href="' . Router::url(['controller' => 'Products', 'action' => 'view', $product->id], true) . '" class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>' .
                    '<a href="' . Router::url(['controller' => 'Products', 'action' => 'edit', $product->id], true) . '" class="" data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>' .
                    '<a href="' . Router::url(['controller' => 'Products', 'action' => 'clone', $product->id], true) . '" class="" data-toggle="tooltip" title="Clone Product"><i class="fas fa-copy m-r-10"></i></a>' .
                    '<a href="' . Router::url(['controller' => 'Products', 'action' => 'delete', $product->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>' .
                    '<a href="javascript:void(0);" class="" data-toggle="tooltip" title="' . __("Price settings") . '" data-target="#pricesettings_modal" onclick="loadPriceSettingsModal(' . h($product->id) . ', this)">' .
                    '<i class="fa fa-money-bill-alt m-r-10"></i></a>';

                if ($product->approval_status !== 'Approved') {
                    $actions .= '<a href="#" onclick="approveProduct(this, ' . h($product->id) . ')" class="approve-btn" data-toggle="tooltip" title="' . __("Approve") . '"><i class="fa fa-check m-r-10"></i></a>';
                }

                $products[] = [
                    'id' => $i,
                    'image' => $img,
                    'name' => h($product->name),
                    'reference_name' => !empty($product->reference_name) ? h($product->reference_name) : '-',
                    'brand' => h($product->brand->name),
                    'category' => '<h6 style="margin-bottom: 1px; font-size: 14px;">Category</h6>' .
                        (!empty($product->parentCategory) ? h($product->parentCategory->name) : 'None') .
                        '<h6 style="margin-top: 5px;margin-bottom: 1px;font-size: 14px;">Sub Category</h6>' .
                        (!empty($product->deepestCategory) ? h($product->deepestCategory->category->name) : 'None'),
                    'sku' => h($product->sku),
                    'size' => h($product->product_size ?? '-'),
                    'weight' => h($product->product_weight ?? '-'),
                    'price' => h($product->sales_price ?? '-'),
                    'status' => '<span class="' . h($status['class']) . '">' . h($status['label']) . '</span>',
                    'approval_status' => '<span class="' . h($approval_status['class']) . '">' . h($approval_status['label']) . '</span>',
                    'actions' => $actions
                ];
                $i++;
            }

            $this->set([
                'products' => $products,
                '_serialize' => ['products'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $products]));
        }
        return null;
    }
    public function view($id = null)
    {
        //$product = $this->Products->get($id, contain: ['Brands', 'Suppliers', 'BannerAds', 'Inventories', 'OrderItems', 'ProductAttributes', 'ProductCategories', 'ProductImages', 'ProductVariants', 'ReturnItems', 'Reviews', 'ShowroomStocks', 'SupplierPurchaseOrderItems', 'SupplierStocks', 'WarehouseStocks', 'Wishlists']);
        $product = $this->Products->get($id, [
            'contain' => [
                'Brands',
                // 'Suppliers',
//                'BannerAds',
                'OrderItems',
                'ProductCategories' => ['Categories'], // Include Categories association
                'ProductImages' => function ($q) {
                    return $q->where([
                        'ProductImages.image_default' => '1', // Select only default images
                        'ProductImages.status' => 'A' // Ensure the image status is 'A'
                    ]);
                },
                'ProductVariants' => [
                    'ProductVariantImages' => function ($q) {
                        return $q->where(['ProductVariantImages.status' => 'A']);
                    }
                ],
                // 'ProductDeals' => function ($q) {
                //     return $q->where(['ProductDeals.status' => 'A']);
                // },
                'Reviews',
                // 'SupplierStocks',
                'Wishlists'
            ]
        ]);

        $product->parentCategory = null;
        $product->subCategory = null;

        if (!empty($product->product_categories)) {
            $product->parentCategory = $product->product_categories[0]->category;

            if (count($product->product_categories) > 1) {
                $product->subCategory = $product->product_categories[0]->category;

                foreach ($product->product_categories as $productCategory) {
                    if ($productCategory->level > $product->subCategory->level) {
                        $product->subCategory = $productCategory->category;
                    }

                    if ($productCategory->level < $product->parentCategory->level) {
                        $product->parentCategory = $productCategory->category;
                    }
                }
            } else {
                $product->subCategory = null;
            }
        }


        if (!empty($product->product_images)) {
            foreach ($product->product_images as $image) {
                $image->image_url = $this->Media->getCloudFrontURL($image->image);
            }
        }

        $attribute_val = $this->AttributeValues->find('list', [
            'keyField' => 'value',
            'valueField' => 'value'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($attribute_val)) {
            $attribute_val = ['' => 'No attributes available'];
        }

        $prod_attribute_val = $this->AttributeValues->find('list', [
            'keyField' => 'id',
            'valueField' => 'value'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($prod_attribute_val)) {
            $prod_attribute_val = ['' => 'No attributes available'];
        }
        $productId = $product->id;

        $parentCategoryId = $product->parentCategory->id ?? null;
        $subCategoryId = $product->subCategory->id ?? null;

        $prod_attributes = $this->Attributes->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->innerJoinWith('CategoryAttributes', function ($q) {
                return $q->where(['CategoryAttributes.status' => 'A']);
            })
            ->where(['Attributes.status' => 'A'])
            ->where(['CategoryAttributes.category_id IN ' => [$parentCategoryId, $subCategoryId]])
            ->distinct(['Attributes.id'])
            ->toArray();

        if (empty($prod_attributes)) {
            $prod_attributes = ['' => 'No attributes available'];
        }

        // $supplier_id = $this->Suppliers->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])->where(['status' => 'A'])->all()->toArray();

        // if (empty($supplier_id)) {
        //     $supplier_id = ['' => 'No suppliers available'];
        // }

        $showrooms = array();
        // $this->Showrooms->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])->where(['status' => 'A'])->all()->toArray();

        if (empty($showrooms)) {
            $showrooms = ['' => 'No showrooms available'];
        }

        $warehouses = array();
        // $this->Warehouses->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])->where(['status' => 'A'])->all()->toArray();

        if (empty($warehouses)) {
            $warehouses  = ['' => 'No warehouses available'];
        }

        $partners = array();
        // $this->Partners->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'business_name'
        // ])->where(['status' => 'A'])->all()->toArray();

        if (empty($partners)) {
            $partners  = ['' => 'No partners available'];
        }

        $conditions = ['status' => 'A'];
        if (!empty($product->id)) {
            $conditions['id <>'] = $product->id;
        }

        $relatedProductIds = $this->Products->RelatedProducts->find()
            ->select(['related_id'])
            ->where(['status <>' => 'D'])
            ->enableHydration(false)
            ->toArray();

        $relatedProductIds = array_column($relatedProductIds, 'related_id');

        $relatedProductIds[] = $product->id;

        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->where($conditions)
            ->where(['id NOT IN' => $relatedProductIds])
            ->all()
            ->toArray();

        if (empty($products)) {
            $products = ['' => 'No products available'];
        }

        $this->set([
            'ImageSize' => Configure::read('Constants.PRODUCT_IMAGE_SIZE'),
            'VideoSize' => Configure::read('Constants.PRODUCT_VIDEO_SIZE'),
            'productImageTypeDisp' => Configure::read('Constants.PRODUCT_IMAGE_TYPE_DISP'),
            'productVideoTypeDisp' => Configure::read('Constants.PRODUCT_VIDEO_TYPE_DISP'),
            'ImageType' => Configure::read('Constants.PRODUCT_IMAGE_JS_TYPE'),
            'VideoType' => Configure::read('Constants.PRODUCT_VIDEO_JS_TYPE'),
            'ImageMinWidth' => Configure::read('Constants.PRODUCT_IMAGE_MIN_WIDTH'),
            'ImageMaxWidth' => Configure::read('Constants.PRODUCT_IMAGE_MAX_WIDTH'),
            'ImageMinHeight' => Configure::read('Constants.PRODUCT_IMAGE_MIN_HEIGHT'),
            'ImageMaxHeight' => Configure::read('Constants.PRODUCT_IMAGE_MAX_HEIGHT'),
        ]);

        $statuses = Configure::read('Constants.STATUS');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $payment_status = Configure::read('Constants.STATUS');
        $payment_terms = Configure::read('Constants.PAYMENT_TERMS');
        $product_preferences = Configure::read('Constants.PRODUCT_PREFERNECES');

        $user = $this->Authentication->getIdentity();
        $canEditprice = false;
        if (!empty($user)) {
            $role = $this->Roles->get($user->role_id);

            if (strtolower($role->name) == 'supervisor') {
                $canEditprice = true;
            } else {
                $canEditprice = false;
            }
        }

        if (!empty($product->barcode)) {
            $barcodeImg = $this->Media->getCloudFrontURL($product->barcode);
        } else {
            $barcodeImg = null;
        }

        if (!empty($product->qrcode)) {
            $qrcodeImg = $this->Media->getCloudFrontURL($product->qrcode);
        } else {
            $qrcodeImg = null;
        }

        $variant_sizes = Configure::read('Constants.VARIANT_SIZES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $title = 'Product | View';
        $this->set(compact('product', 'title', 'statuses', 'dateFormat', 'currencySymbol', 'attribute_val', 'prod_attribute_val', 'prod_attributes', 'statusMap', 'status', 'supplier_id', 'showrooms', 'warehouses', 'payment_status', 'partners', 'payment_terms', 'canEditprice', 'product_preferences', 'products', 'barcodeImg', 'qrcodeImg', 'variant_sizes', 'decimalSeparator', 'thousandSeparator'));
    }

    public function add()
    {

        $this->set([
            'productSize' => Configure::read('Constants.PRODUCT_IMAGE_SIZE'),
            'productType' => Configure::read('Constants.PRODUCT_IMAGE_JS_TYPE'),
            'productMinWidth' => Configure::read('Constants.PRODUCT_IMAGE_MIN_WIDTH'),
            'productMaxWidth' => Configure::read('Constants.PRODUCT_IMAGE_MAX_WIDTH'),
            'productMinHeight' => Configure::read('Constants.PRODUCT_IMAGE_MIN_HEIGHT'),
            'productMaxHeight' => Configure::read('Constants.PRODUCT_IMAGE_MAX_HEIGHT'),
            'productImageType' => Configure::read('Constants.PRODUCT_IMAGE_TYPE'),
        ]);

        $product = $this->Products->newEmptyEntity();

        $selectedCategoryId = null;
        $selectedSubCategoryId = null;

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            if (empty($data['product_size'])) {
                $data['product_size'] = null;
            }


            $data['purchase_price'] = 9999999;
            $data['product_price'] = 9999999;
            $data['sales_price'] = 9999999;

            // Set default values for required fields that might be missing
            $data['COD_in_city'] = $data['COD_in_city'] ?? 0;
            $data['COD_out_city'] = $data['COD_out_city'] ?? 0;
            $data['avl_on_credit'] = $data['avl_on_credit'] ?? 0;

            // Ensure brand_id is set and valid
            if (empty($data['brand_id']) || !is_numeric($data['brand_id'])) {
                // Get the first available brand
                $firstBrand = $this->Products->Brands->find()->first();
                $data['brand_id'] = $firstBrand ? $firstBrand->id : 1;
            }

            // Handle country_id assignment based on current country filter
            $selectedCountryId = $this->getCurrentCountryFilter();
            if ($selectedCountryId) {
                // If a specific country is selected, assign it to the product
                $data['country_id'] = $selectedCountryId;
            } else {
                // If "All Countries" is selected, you have several options:

                // Option 1: Leave country_id as null (recommended for global products)
                $data['country_id'] = null;

                // Option 2: Set a default country (uncomment if needed)
                // $defaultCountry = $this->Countries->find()->where(['iso_code_2' => 'QA'])->first(); // Qatar as default
                // $data['country_id'] = $defaultCountry ? $defaultCountry->id : null;

                // Option 3: Require user to select a country (uncomment if needed)
                // if (empty($data['country_id'])) {
                //     $this->Flash->error(__('Please select a country for this product.'));
                //     $selectedCategoryId = $this->request->getData('category_id');
                //     $selectedSubCategoryId = $this->request->getData('sub_category_id');
                //     // Set form data and return to form
                //     $this->set(compact('product', 'selectedCategoryId', 'selectedSubCategoryId'));
                //     return;
                // }
            }

            $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Products');
            // Only set default Arabic values if not provided
            if (empty($data['name_ar'])) {
                $data['name_ar'] = $data['name'];
            }
            if (empty($data['description_ar'])) {
                $data['description_ar'] = $data['description'];
            }
            if (empty($data['details_ar'])) {
                $data['details_ar'] = $data['details'];
            }
            if (empty($data['features_ar'])) {
                $data['features_ar'] = $data['features'];
            }

            // Remove catalogue from data if it's an UploadedFile object
            if (isset($data['catalogue']) && is_object($data['catalogue'])) {
                unset($data['catalogue']);
            }

            $product = $this->Products->patchEntity($product, $data);

            // Handle catalogue file upload before saving
            $catalogueFile = $this->request->getData('catalogue');
            $hasCatalogueFile = false;

            // Check if catalogue file is valid and has content
            if ($catalogueFile && is_object($catalogueFile) && method_exists($catalogueFile, 'getError')) {
                $catalogueError = $catalogueFile->getError();
                $catalogueFileName = trim($catalogueFile->getClientFilename());

                $this->log('Catalogue file detected: ' . $catalogueFileName . ', Error: ' . $catalogueError, 'debug');

                // Only process if file is uploaded successfully and has a name
                if ($catalogueError === UPLOAD_ERR_OK && !empty($catalogueFileName)) {
                    $hasCatalogueFile = true;
                } elseif ($catalogueError !== UPLOAD_ERR_NO_FILE) {
                    // Log error if it's not just "no file uploaded"
                    $this->log('Catalogue upload error: ' . $catalogueError . ' for file: ' . $catalogueFileName, 'error');
                }
            }

            if ($this->Products->save($product)) {

                $productId = $product->id;
                if ($product['scanned_barcode'] == '') {
                    $this->generateAndUploadBarcode($productId, $product->sku);
                }
                $this->generateAndUploadQRCode($productId, $product->sku);

                // Handle catalogue file upload after successful save
                if ($hasCatalogueFile) {
                    $this->log('Processing catalogue upload for product ID: ' . $productId, 'debug');
                    $cataloguePath = $this->handleCatalogueUpload($catalogueFile, $productId);
                    if ($cataloguePath) {
                        $product->catalogue = $cataloguePath;
                        if (!$this->Products->save($product)) {
                            $this->log('Failed to save catalogue path: ' . json_encode($product->getErrors()), 'error');
                            $this->Flash->error(__('Catalogue was uploaded but could not be linked to product.'));
                        } else {
                            $this->log('Catalogue saved successfully: ' . $cataloguePath, 'debug');
                        }
                    }
                }

                $uploadedImages = !empty($this->request->getData('product_image')) ? $this->handleFileUploads($productId) : [];

                if (!empty($uploadedImages)) {
                    $firstImage = $uploadedImages[0];
                    $product->product_image = $firstImage;
                    $this->Products->save($product);
                }

                $selectedCategory = $this->request->getData('category_id');
                $selectedSubCategory = $this->request->getData('sub_category_id');

                $this->saveProductCategories($productId, [$selectedCategory, $selectedSubCategory]);

                $productShowroomsPricesTable = $this->getTableLocator()->get('ProductShowroomPrices');

                if ($this->request->getData('showroom_based_price') === 'Yes') {
                    $this->saveShowroomPrices($productId);
                }

                $this->Flash->success(__('The product has been saved.'));
                return $this->redirect(['action' => 'index']);
            }

            $selectedCategoryId = $this->request->getData('category_id');
            $selectedSubCategoryId = $this->request->getData('sub_category_id');

            // Clear catalogue field if it contains UploadedFile object to prevent URL helper error
            if (isset($product->catalogue) && is_object($product->catalogue)) {
                $product->catalogue = null;
            }

            // Log validation errors for debugging
            $this->log('Product save failed. Validation errors: ' . json_encode($product->getErrors()), 'debug');
            $this->Flash->error(__('The product could not be saved. Please, try again.'));
        }

        $brands = $this->Products->Brands->find('list', [
            'conditions' => ['status' => 'A']
        ])->all();

        $suppliers = '';

        $showrooms = '';
        $categories = $this->Products->ProductCategories->Categories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
            'conditions' => ['parent_id IS' => null, 'status' => 'A']
        ])->toArray();
        $canAddBrand = $this->hasPermission('Brands', 'add');

        // Get countries for dropdown (already available from AppController)
        // $countries is already set in AppController initialize method

        $title = 'Product | Add';
        $this->set(compact('product', 'brands', 'suppliers', 'categories', 'showrooms', 'selectedCategoryId', 'selectedSubCategoryId', 'title', 'canAddBrand'));
    }

    private function handleFileUploads($productId)
    {
        $files = $this->request->getData('product_image');
        $uploadedImages = [];
        $i = 0;
        $hasErrors = false;

        if (empty($files)) {
            $this->log('No files provided for upload', 'debug');
            return $uploadedImages;
        }

        $exists = $this->ProductImages->find()
            ->where([
                'product_id' => $productId,
                'media_type' => 'Image',
                'image_default' => 1,
                'status' => 'A'
            ])
            ->first();

        foreach ($files as $file) {
            try {
                // Skip if file is not a valid upload object
                if (!is_object($file) || !method_exists($file, 'getError')) {
                    $this->log('Invalid file object provided', 'debug');
                    continue;
                }

                $fileName = trim($file->getClientFilename());

                // Skip empty files or files with no name
                if (empty($fileName)) {
                    $this->log('Empty filename provided', 'debug');
                    continue;
                }

                // Check for upload errors, but skip UPLOAD_ERR_NO_FILE (no file uploaded)
                if ($file->getError() !== UPLOAD_ERR_OK) {
                    if ($file->getError() === UPLOAD_ERR_NO_FILE) {
                        $this->log('No file uploaded (UPLOAD_ERR_NO_FILE) - skipping', 'debug');
                        continue;
                    }
                    $this->log('File upload error: ' . $file->getError() . ' for file: ' . $fileName, 'error');
                    $this->Flash->error(__('Upload error for file: {0}', $fileName));
                    $hasErrors = true;
                    continue;
                }

                $imageTmpName = $file->getStream()->getMetadata('uri');

                $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                $filePath = Configure::read('Settings.PRODUCT');
                $folderPath = $uploadFolder . $filePath;
                $targetdir = WWW_ROOT . $folderPath;
                $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                $this->log('Attempting to upload image: ' . $fileName . ' to: ' . $folderPath . $imageFile, 'debug');

                $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                if ($uploadResult !== 'Success') {
                    $this->log('Image upload failed: ' . $uploadResult . ' for file: ' . $fileName, 'error');
                    $this->Flash->error(__('Image {0} could not be uploaded: {1}', $fileName, $uploadResult));
                    $hasErrors = true;
                } else {
                    $productImage = $this->Products->ProductImages->newEmptyEntity();
                    $productImage->product_id = $productId;
                    $productImage->image = $folderPath . $imageFile;
                    $productImage->media_type = 'Image';
                    $productImage->status = 'A';

                    if (!$exists && $i == 0) {
                        $productImage->image_default = 1;
                    } else {
                        $productImage->image_default = 0;
                    }
                    $i++;

                    if ($this->Products->ProductImages->save($productImage)) {
                        $uploadedImages[] = $folderPath . $imageFile;
                        $this->log('Image saved successfully: ' . $fileName, 'debug');
                    } else {
                        $this->log('Failed to save product image: ' . json_encode($productImage->getErrors()), 'error');
                        $this->Flash->error(__('Image {0} could not be saved to database.', $fileName));
                        $hasErrors = true;
                    }
                }
            } catch (\Exception $e) {
                $this->log('Exception during image upload: ' . $e->getMessage(), 'error');
                $this->Flash->error(__('An error occurred while processing image: {0}', $fileName ?? 'unknown'));
                $hasErrors = true;
            }
        }

        if ($hasErrors && empty($uploadedImages)) {
            $this->log('All image uploads failed for product ID: ' . $productId, 'error');
        }

        return $uploadedImages;
    }

    private function saveCategoryEntry($productCategoriesTable, $productId, $categoryId, $level)
    {
        $existingEntry = $productCategoriesTable->find()
            ->where(['product_id' => $productId, 'category_id' => $categoryId])
            ->first();

        if (!$existingEntry) {
            $productCategory = $productCategoriesTable->newEmptyEntity();
            $productCategory = $productCategoriesTable->patchEntity($productCategory, [
                'product_id' => $productId,
                'category_id' => $categoryId,
                'level' => $level,
            ]);
            $productCategoriesTable->save($productCategory);
        }
    }

    public function importProducts()
    {

        $title = 'Upload CSV';
        $this->set(compact('title'));
    }

    public function uploadProducts()
    {

        $this->autoRender = false;

        $successfullyImportedRows = 0;

        if (!ob_start()) {
            ob_start();
        }

        // Set the response header type
        $this->response = $this->response->withType('application/json');

        if ($this->request->is('post')) {
            $error_happened = false;
            $image_upload_dir_created = false;

            // Check if the zip file is present
            if ($_FILES['zip_file']['tmp_name'] === '') {
                $compressed_image_directory_exists = false;
            } else {
                $compressed_image_directory_exists = true;

                // Create a directory for the images
                $image_upload_dir = WWW_ROOT . 'uploads/import/image_dir_' . date('Y_m_d_H_i_s_') . rand(1, 1000);
                if (!mkdir($image_upload_dir, 0777, true)) {
                    $error_happened = true;
                } else {
                    $image_upload_dir_created = true;
                    $zip = new \ZipArchive;
                    if ($zip->open($_FILES['zip_file']['tmp_name']) !== TRUE) {
                        $error_happened = true;
                    } else {
                        if ($zip->extractTo($image_upload_dir . '/') === false) {
                            $error_happened = true;
                            $zip->close();
                        } else {
                            $image_parent_dir = $image_upload_dir . '/';
                            $entry_details = $zip->statIndex(0);
                            $slash_pos = strpos($entry_details['name'], '/');
                            if ($slash_pos !== false) {
                                $entry_directory = substr($entry_details['name'], 0, $slash_pos);
                                $image_parent_dir .= $entry_directory . '/';
                            }
                            $zip->close();
                        }
                    }
                }
            }

            if ($error_happened === false) {
                $not_imported_some_rows = false;
                $rows_processed = 0;
                $total_rows = count(file($_FILES['csv_file']['tmp_name'])) - 1;
                $error_messages = [];

                $file = fopen($_FILES['csv_file']['tmp_name'], 'r');
                $header = fgetcsv($file);

                while (($columns = fgetcsv($file)) !== false) {
                    $row_error = false;
                    $rows_processed++;

                    for ($i = 0; $i < count($columns); $i++) {
                        $columns[$i] = str_replace('“', '"', $columns[$i]);
                        $columns[$i] = str_replace('”', '"', $columns[$i]);
                    }

                    // Extract product data from CSV columns
                    $supplier_ref_title = trim($columns[0]);
                    $product_title = trim($columns[1]);
                    $category = trim($columns[2]);
                    $sub_category = trim($columns[3]);
                    $product_size = trim($columns[4]);
                    $product_weight = trim($columns[5]);
                    $brand = trim($columns[6]);
                    $product_description = trim($columns[7]);
                    if (!empty($product_description)) {
                        $product_description = mb_convert_encoding($product_description, 'UTF-8', 'ISO-8859-1');
                    }
                    $product_tags = trim($columns[8]);
                    $product_images = trim($columns[9]);
                    $sku = trim($columns[10]);
                    $widget_configuration = trim($columns[11]);
                    $meta_title = trim($columns[12]);
                    $meta_description = trim($columns[13]);
                    if (!empty($meta_description)) {
                        $meta_description = mb_convert_encoding($meta_description, 'UTF-8', 'ISO-8859-1');
                    }
                    $meta_keywords = trim($columns[14]);
                    $status = trim($columns[15]);
                    $attributes = trim($columns[16]);
                    $variants = trim($columns[17]);

                    // Error checking
                    if (empty($sku)) {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': SKU cannot be empty.';
                        continue;
                    }

                    if (!preg_match('/^[a-zA-Z0-9]+$/', $sku)) {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': SKU "' . $sku . '" is not valid. SKU ID can only contain letters and numbers.';
                        continue;
                    }

                    $existingProduct = $this->Products->find()->where(['sku' => $sku])->first();
                    if ($existingProduct) {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': SKU "' . $sku . '" already exists.';
                        continue;
                    }

                    // Additional checks
                    if ($supplier_ref_title === '') {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': Please enter supplier reference title.';
                        continue;
                    }

                    if ($product_title === '') {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': Please enter product title.';
                        continue;
                    }

                    if ($category === '') {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': Please select category.';
                        continue;
                    }

                    if ($product_weight === '') {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': Please enter product weight.';
                        continue;
                    }

                    if ($product_description === '') {
                        $row_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': Please enter product detail.';
                        continue;
                    }

                    // Check Brand Existence
                    if ($brand !== '') {
                        $brandRecord = $this->Brands->find()->where(['name' => $brand, 'status' => 'A'])->first();
                        if (!$brandRecord) {
                            $row_error = true;
                            $error_messages[] = 'Row ' . $rows_processed . ': Brand does not exist.';
                            continue;
                        }
                        $brand_id = $brandRecord['id'];
                    }

                    // Check Category Existence
                    if ($category !== '') {
                        $categoryRecord = $this->Categories->find()->where(['name' => $category, 'status' => 'A'])->first();
                        if (!$categoryRecord) {
                            $row_error = true;
                            $error_messages[] = 'Row ' . $rows_processed . ': Category does not exist.';
                            continue;
                        }
                        $category_id = $categoryRecord['id'];
                    }

                    // Check Sub-category Existence
                    if ($sub_category !== '') {
                        $subCategoryRecord = $this->Categories->find()->where(['name' => $sub_category, 'status' => 'A'])->first();
                        if (!$subCategoryRecord) {
                            $row_error = true;
                            $error_messages[] = 'Row ' . $rows_processed . ': Sub Category does not exist.';
                            continue;
                        }
                        $sub_category_id = $subCategoryRecord['id'];
                    }

                    if (!empty($attributes)) {
                        // Decode the JSON attributes
                        $decodedAttributes = json_decode($attributes, true); // Convert JSON to an array
                        $validAttributes = []; // Store valid attributes for processing

                        // Check if JSON is valid
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            $row_error = true;
                            $error_messages[] = 'Row ' . $rows_processed . ": Invalid JSON format for attributes.";
                        } else {
                            // Loop through each attribute in the JSON array
                            foreach ($decodedAttributes as $attributeData) {
                                if (!isset($attributeData['attribute_name']) || !isset($attributeData['attribute_value'])) {
                                    $row_error = true;
                                    $error_messages[] = 'Row ' . $rows_processed . ": Missing required attribute_name or attribute_value in JSON.";
                                    continue;
                                }

                                $attributeName = trim($attributeData['attribute_name']);
                                $attributeValue = trim($attributeData['attribute_value']);
                                $attributeDescription = isset($attributeData['attribute_description']) ? trim($attributeData['attribute_description']) : null;

                                // Validate Attribute Name
                                $attribute = $this->Attributes->find()
                                    ->where([
                                        'name' => $attributeName,
                                        'key_name' => 'product',
                                        'status' => 'A',
                                    ])
                                    ->first();

                                if (!$attribute) {
                                    $row_error = true;
                                    $error_messages[] = 'Row ' . $rows_processed . ": Attribute '{$attributeName}' does not exist.";
                                    continue;
                                }

                                $attribute_id = $attribute->id;

                                // Validate Attribute Value
                                $attributeValueRecord = $this->AttributeValues->find()
                                    ->where([
                                        'attribute_id' => $attribute_id,
                                        'value' => $attributeValue,
                                    ])
                                    ->first();

                                if (!$attributeValueRecord) {
                                    $row_error = true;
                                    $error_messages[] = 'Row ' . $rows_processed . ": Value '{$attributeValue}' for attribute '{$attributeName}' does not exist.";
                                    continue;
                                }

                                $attribute_value_id = $attributeValueRecord->id;

                                // Add valid attribute to array for further processing
                                $validAttributes[] = [
                                    'attribute_id' => $attribute_id,
                                    'attribute_value_id' => $attribute_value_id,
                                    'attribute_name' => $attributeName,
                                    'attribute_value' => $attributeValue,
                                    'attribute_description' => $attributeDescription
                                ];
                            }
                        }
                    }
                    // Check Image Paths and Upload
                    $image_paths = explode(',', $product_images);
                    $imagePathsToSave = [];
                    $isFirstImage = true;

                    foreach ($image_paths as $image_path) {
                        $imgPath = $image_parent_dir . $image_path;
                        if (!file_exists($imgPath)) {
                            $row_error = true;
                            $error_messages[] = 'Row ' . $rows_processed . ': Product image ' . $image_path . ' does not exist in the compressed file.';
                            continue;
                        } else {
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $filePath = Configure::read('Settings.PRODUCT');
                            $media_product_path = $uploadFolder . $filePath;
                            $targetdir = WWW_ROOT . $media_product_path;
                            $newName = strtoupper(substr(uniqid(sha1((string) time())), -5));
                            $ext = pathinfo($image_path, PATHINFO_EXTENSION);
                            $newNameWithExt = pathinfo($image_path, PATHINFO_FILENAME) . '_' . $newName . '.' . $ext;

                            $uploadStatus = $this->Media->upload_resize($imgPath, $targetdir, $newNameWithExt, $media_product_path);
                            if ($uploadStatus !== 'Success') {
                                $row_error = true;
                                $error_messages[] = 'Row ' . $rows_processed . ': Failed to upload image to S3: ' . $image_path;
                                continue;
                            }
                            $imagePathsToSave[] = $media_product_path . $newNameWithExt;

                            if ($isFirstImage) {
                                $isFirstImage = false;
                                $default_image_path = $media_product_path . '/' . $newNameWithExt;
                            }
                        }
                    }

                    if (!$row_error) {
                        $product = $this->Products->newEmptyEntity();
                        $product->reference_name = $supplier_ref_title;
                        $product->name = $product_title;
                        $product->description = $product_description;
                        $product->product_reference = $widget_configuration;
                        $product->product_size = $product_size;
                        $product->product_weight = $product_weight;
                        $product->product_tags = $product_tags;
                        $product->sku = $sku;
                        $product->brand_id = $brand_id ?? null;
                        $product->meta_title = $meta_title;
                        $product->meta_description = $meta_description;
                        $product->meta_keywords = $meta_keywords;
                        $product->approval_status = 'Approved';
                        $product->status = $status;

                        $savedProduct = $this->Products->save($product);

                        if ($savedProduct) {
                            $product_id = $savedProduct->id;

                            if (isset($imagePathsToSave)) {
                                $this->addImagesToProduct($product_id, $imagePathsToSave, $error_messages, $rows_processed);
                            }

                            $this->saveProductCategories($product_id, [$category_id, $sub_category_id]);

                            if (!empty($validAttributes)) {
                                $this->addAttributesToProduct($product_id, $validAttributes, $error_messages, $rows_processed);
                            }

                            if (!empty($variants)) {
                                $this->addVariantsToProduct($product_id, $variants, $image_parent_dir, $error_messages, $rows_processed);
                            }
                        } else {
                            $error_messages[] = 'Row ' . $rows_processed . ': Failed to save product.';
                            break;
                        }
                    }
                }
                fclose($file);

                $response = [
                    'status' => $error_messages ? 'error' : 'success',
                    'message' => $error_messages ? 'There was an error during file processing.' : 'Products imported successfully.',
                    'successfully_imported_rows' => $successfullyImportedRows,
                    'errors' => $error_messages,
                    'rows_processed' => $rows_processed,
                    'total_rows' => $total_rows,
                ];

                echo json_encode($response);
            }
        }

        ob_end_flush();
    }

    public function addImagesToProduct($product_id, $imagePathsToSave, &$error_messages, $rows_processed)
    {
        if (empty($imagePathsToSave)) {
            return;
        }

        foreach ($imagePathsToSave as $key => $imagePath) {
            $productImage = $this->Products->ProductImages->newEmptyEntity();
            $productImage->product_id = $product_id;
            $productImage->image = $imagePath;
            $productImage->media_type = 'Image';
            $productImage->status = 'A';
            $productImage->image_default = ($key === 0) ? 1 : 0;

            if (!$this->Products->ProductImages->save($productImage)) {
                $error_messages[] = 'Row ' . $rows_processed . ': Failed to save product image (' . $imagePath . ').';
            }
        }
    }

    public function addAttributesToProduct($product_id, $validAttributes, &$error_messages, $rows_processed)
    {
        foreach ($validAttributes as $validAttribute) {
            $attribute_id = $validAttribute['attribute_id'];
            $attribute_value_id = $validAttribute['attribute_value_id'];
            $attributeName = $validAttribute['attribute_name'];
            $attributeValue = $validAttribute['attribute_value'];
            $attributeDescription = $validAttribute['attribute_description'];

            // Check if the product attribute already exists
            $existingAttribute = $this->ProductAttributes->find()
                ->where([
                    'product_id' => $product_id,
                    'attribute_id' => $attribute_id,
                    'attribute_value_id' => $attribute_value_id,
                    'status' => 'A',
                ])
                ->first();

            if ($existingAttribute) {
                $error_messages[] = 'Row ' . $rows_processed . ": The product attribute '{$attributeName}' with value '{$attributeValue}' already exists.";
                continue;
            }

            // Prepare data for saving
            $attributesArray = [
                'attribute_id' => $attribute_id,
                'attribute_value_id' => $attribute_value_id,
                'product_id' => $product_id,
                'attribute_description' => $attributeDescription, // optional field
                'status' => 'A', // Default status
            ];

            $productAttribute = $this->ProductAttributes->newEmptyEntity();
            $productAttribute = $this->ProductAttributes->patchEntity($productAttribute, $attributesArray);

            // Attempt to save the new attribute
            if (!$this->ProductAttributes->save($productAttribute)) {
                $error_messages[] = 'Row ' . $rows_processed . ": Failed to save the product attribute '{$attributeName}' with value '{$attributeValue}'.";
            }
        }
    }

    public function addVariantsToProduct($product_id, $variants, $image_upload_dir, &$error_messages, &$rows_processed)
    {
        // Initialize error flag
        $variant_error = false;
        $variant_images = [];
        $validVariants = [];

        // Get product name based on the product_id
        $product = $this->Products->find()->where(['id' => $product_id])->first();
        $product_name = $product ? $product->name : 'Unknown Product'; // Fallback to 'Unknown Product' if not found

        $decodedVariants = json_decode($variants, true);
        // Loop through each variant
        foreach ($decodedVariants as $variantData) {
            $variant_error = false;

            // Extract variant data from JSON
            $variant_name = isset($variantData['variant_name']) ? trim($variantData['variant_name']) : '';
            $variant_sku = trim($variantData['variant_sku']);
            $variant_size = trim($variantData['variant_size']);
            $variant_weight = trim($variantData['variant_weight']);
            $variant_description = isset($variantData['variant_description']) ? trim($variantData['variant_description']) : null;

            // Error checking for variant SKU
            if (empty($variant_sku)) {
                $variant_error = true;
                $error_messages[] = 'Row ' . $rows_processed . ': Variant SKU cannot be empty for product "' . $product_name . '"';
                continue;
            }

            // Check if variant SKU already exists for this product (in the product_variants table)
            $existingVariant = $this->ProductVariants->find()
                ->where(['sku' => $variant_sku, 'product_id' => $product_id])
                ->first();

            if ($existingVariant) {
                $variant_error = true;
                $error_messages[] = 'Row ' . $rows_processed . ': Variant SKU "' . $variant_sku . '" already exists for product "' . $product_name . '"';
                continue;
            }

            // Additional validation for other fields like variant_size and variant_weight
            if (empty($variant_size)) {
                $variant_error = true;
                $error_messages[] = 'Row ' . $rows_processed . ': Variant size cannot be empty for SKU "' . $variant_sku . '" in product "' . $product_name . '"';
                continue;
            }

            if (empty($variant_weight)) {
                $variant_error = true;
                $error_messages[] = 'Row ' . $rows_processed . ': Variant weight cannot be empty for SKU "' . $variant_sku . '" in product "' . $product_name . '"';
                continue;
            }

            // Check if the variant images exist (if provided)
            if (isset($variantData['variant_images']) && !empty($variantData['variant_images'])) {

                if (is_array($variantData['variant_images'])) {
                    $variant_images = $variantData['variant_images'];
                } else {
                    $variant_images = explode(',', $variantData['variant_images']);
                }

                $imagePathsToSave = [];
                $isFirstImage = true;

                foreach ($variant_images as $image_path) {
                    $imgPath = $image_upload_dir . $image_path;
                    if (!file_exists($imgPath)) {
                        $variant_error = true;
                        $error_messages[] = 'Row ' . $rows_processed . ': Variant image ' . $image_path . ' does not exist in the compressed file for product "' . $product_name . '"';
                        continue;
                    } else {
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Settings.PRODUCT_VARIANT_IMAGE');
                        $media_product_path = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $media_product_path;
                        $newName = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $ext = pathinfo($image_path, PATHINFO_EXTENSION);
                        $newNameWithExt = pathinfo($image_path, PATHINFO_FILENAME) . '_' . $newName . '.' . $ext;

                        $uploadStatus = $this->Media->upload_resize($imgPath, $targetdir, $newNameWithExt, $media_product_path);
                        if ($uploadStatus !== 'Success') {
                            $variant_error = true;
                            $error_messages[] = 'Row ' . $rows_processed . ': Failed to upload image to S3: ' . $image_path . ' for variant SKU "' . $variant_sku . '" in product "' . $product_name . '"';
                            continue;
                        }
                        $imagePathsToSave[] = $media_product_path . $newNameWithExt;

                        if ($isFirstImage) {
                            $isFirstImage = false;
                            $default_image_path = $media_product_path . '/' . $newNameWithExt;
                        }
                    }
                }
            }

            if (!$variant_error) {
                // Save the variant to the database
                $variant = $this->ProductVariants->newEmptyEntity();
                $variant->product_id = $product_id;
                $variant->sku = $variant_sku;
                $variant->size = $variant_size; // Save the variant size
                $variant->weight = $variant_weight; // Save the variant weight
                $variant->description = $variant_description; // Save the variant description if exists
                $variant->status = 'A';

                $savedVariant = $this->ProductVariants->save($variant);

                if ($savedVariant) {
                    // Associate the images with the variant
                    if (!empty($imagePathsToSave)) {
                        foreach ($imagePathsToSave as $key => $imagePath) {
                            $variantImage = $this->ProductVariantImages->newEmptyEntity();
                            $variantImage->variant_id = $savedVariant->id;
                            $variantImage->image = $imagePath;
                            $variantImage->media_type = 'Image';
                            $variantImage->status = 'A';
                            $variantImage->image_default = ($key === 0) ? 1 : 0;

                            if (!$this->ProductVariantImages->save($variantImage)) {
                                $error_messages[] = 'Row ' . $rows_processed . ': Failed to save image (' . $imagePath . ') for variant SKU "' . $variant_sku . '" in product "' . $product_name . '"';
                            }
                        }
                    }
                } else {
                    $error_messages[] = 'Row ' . $rows_processed . ': Failed to save variant for product "' . $product_name . '"';
                }
            }
        }

        return $rows_processed;
    }

    private function removeDir($dir_path)
    {
        $rdi = new \RecursiveDirectoryIterator($dir_path, \RecursiveDirectoryIterator::SKIP_DOTS);
        $files = new \RecursiveIteratorIterator($rdi, \RecursiveIteratorIterator::CHILD_FIRST);
        foreach ($files as $file) {
            if ($file->isDir()) {
                rmdir($file->getRealPath());
            } else {
                unlink($file->getRealPath());
            }
        }
        rmdir($dir_path);
    }

    private function addCategoryWithHierarchy($productCategoriesTable, $productId, $categoryId)
    {
        $level = $this->determineCategoryLevel($categoryId);
        $category = $this->Products->ProductCategories->Categories->get($categoryId);
        $this->saveCategoryEntry($productCategoriesTable, $productId, $category->id, $level);
        while ($category->parent_id) {
            $category = $this->Products->ProductCategories->Categories->get($category->parent_id);
            $level--;
            $this->saveCategoryEntry($productCategoriesTable, $productId, $category->id, $level);
        }
    }

    private function determineCategoryLevel($categoryId)
    {
        $level = 1;
        $category = $this->Products->ProductCategories->Categories->get($categoryId);
        while ($category->parent_id) {
            $category = $this->Products->ProductCategories->Categories->get($category->parent_id);
            $level++;
        }

        return $level;
    }

    private function saveProductCategories($productId, $categoryIds)
    {
        $productCategoriesTable = $this->getTableLocator()->get('ProductCategories');

        foreach ($categoryIds as $categoryId) {
            if ($categoryId) {
                $this->addCategoryWithHierarchy($productCategoriesTable, $productId, $categoryId);
            }
        }
    }

    private function saveShowroomPrices($productId)
    {
        $productShowroomsPricesTable = $this->getTableLocator()->get('ProductShowroomPrices');
        $showroomIds = $this->request->getData('showroom_id');
        $transportationCosts = $this->request->getData('transportation_cost');

        foreach ($showroomIds as $index => $showroomId) {

            $existingPrice = $productShowroomsPricesTable->find()
                ->where([
                    'product_id' => $productId,
                    'showroom_id' => $showroomId
                ])
                ->first();

            if (!$existingPrice) {
                $priceData = [
                    'product_id' => $productId,
                    'showroom_id' => $showroomId,
                    'price' => $transportationCosts[$index],
                ];

                $showroomPrice = $productShowroomsPricesTable->newEmptyEntity();
                $showroomPrice = $productShowroomsPricesTable->patchEntity($showroomPrice, $priceData);
                $productShowroomsPricesTable->save($showroomPrice);
            }
        }
    }

        public function edit($id = null)
        {
            $this->set([
                'productSize' => Configure::read('Constants.PRODUCT_IMAGE_SIZE'),
                'productType' => Configure::read('Constants.PRODUCT_IMAGE_JS_TYPE'),
                'productMinWidth' => Configure::read('Constants.PRODUCT_IMAGE_MIN_WIDTH'),
                'productMaxWidth' => Configure::read('Constants.PRODUCT_IMAGE_MAX_WIDTH'),
                'productMinHeight' => Configure::read('Constants.PRODUCT_IMAGE_MIN_HEIGHT'),
                'productMaxHeight' => Configure::read('Constants.PRODUCT_IMAGE_MAX_HEIGHT'),
                'productImageType' => Configure::read('Constants.PRODUCT_IMAGE_TYPE'),
            ]);

            $selectedCategoryId = null;
            $selectedSubCategoryId = null;

            $product = $this->Products->get($id, [
                'contain' => [
                    'ProductCategories.Categories',
                    'ProductImages' => function ($q) {
                        return $q->where(['ProductImages.status' => 'A', 'ProductImages.media_type' => 'Image']);
                    }
                  //  'ProductShowroomPrices.Showrooms'
                ]
            ]);

            if ($this->request->is(['patch', 'post', 'put'])) {

                $data = $this->request->getData();

                if (empty($data['product_size'])) {
                    $data['product_size'] = null;
                }

                $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Products', $id);
                $data['name'] = $data['name']; // English
                $data['name_ar'] = $data['name_ar'];
                $data['features'] = $data['features'];
                $data['features_ar'] = $data['features_ar'];
                $data['description'] = $data['description'];
                $data['description_ar'] = $data['description_ar'];
                $data['details'] = $data['details']; // specification
                $data['details_ar'] = $data['details_ar']; // specification arbic

                // Set default values for required fields that might be missing
                $data['COD_in_city'] = $data['COD_in_city'] ?? 0;
                $data['COD_out_city'] = $data['COD_out_city'] ?? 0;
                $data['avl_on_credit'] = $data['avl_on_credit'] ?? 0;

                // Ensure brand_id is set and valid
                if (empty($data['brand_id']) || !is_numeric($data['brand_id'])) {
                    // Get the first available brand
                    $firstBrand = $this->Products->Brands->find()->first();
                    $data['brand_id'] = $firstBrand ? $firstBrand->id : 1;
                }

                // If product_preference is not set (unchecked), set it to null
                if (!isset($data['product_preference'])) {
                    $data['product_preference'] = null;
                }

                // Remove catalogue from data if it's an UploadedFile object
                if (isset($data['catalogue']) && is_object($data['catalogue'])) {
                    unset($data['catalogue']);
                }

                $product = $this->Products->patchEntity($product, $data);

                if ($this->request->getData('deleted_images')) {
                    $deletedImages = json_decode($this->request->getData('deleted_images'), true);
                    if (!empty($deletedImages)) {
                        foreach ($deletedImages as $id) {
                            $productImage = $this->ProductImages->get($id);
                            $this->Media->awsDelete($productImage->image);
                            $this->ProductImages->delete($productImage);
                        }
                    }
                }

                $firstImage = $this->ProductImages->find()
                    ->where(['product_id' => $id])
                    ->order(['id' => 'ASC'])
                    ->first();
                if ($firstImage) {
                    $product['product_image'] = $firstImage->image;
                } else {
                    $product['product_image'] = null;
                }

                if (!empty($deletedImages)) {
                    $this->ProductImages->deleteAll(['id IN' => $deletedImages]);
                }

                // Handle catalogue file upload before saving
                $catalogueFile = $this->request->getData('catalogue');
                $hasCatalogueFile = false;

                // Check if catalogue file is valid and has content
                if ($catalogueFile && is_object($catalogueFile) && method_exists($catalogueFile, 'getError')) {
                    $catalogueError = $catalogueFile->getError();
                    $catalogueFileName = trim($catalogueFile->getClientFilename());

                   // $this->log('Catalogue file detected: ' . $catalogueFileName . ', Error: ' . $catalogueError, 'debug');

                    // Only process if file is uploaded successfully and has a name
                    if ($catalogueError === UPLOAD_ERR_OK && !empty($catalogueFileName)) {
                        $hasCatalogueFile = true;
                    } elseif ($catalogueError !== UPLOAD_ERR_NO_FILE) {
                        // Log error if it's not just "no file uploaded"
                        $this->log('Catalogue upload error: ' . $catalogueError . ' for file: ' . $catalogueFileName, 'error');
                    }
                }

                if ($this->Products->save($product)) {

                    $productId = $product->id;
                    if ($product['scanned_barcode'] == '') {
                        $this->generateAndUploadBarcode($productId, $product->sku);
                    }
                    $this->generateAndUploadQRCode($productId, $product->sku);

                    // Handle catalogue file upload after successful save
                    if ($hasCatalogueFile) {
                        $this->log('Processing catalogue upload for product ID: ' . $productId, 'debug');
                        $cataloguePath = $this->handleCatalogueUpload($catalogueFile, $productId);
                        if ($cataloguePath) {
                            $product->catalogue = $cataloguePath;
                            if (!$this->Products->save($product)) {
                                $this->log('Failed to save catalogue path: ' . json_encode($product->getErrors()), 'error');
                                $this->Flash->error(__('Catalogue was uploaded but could not be linked to product.'));
                            } else {
                                $this->log('Catalogue saved successfully: ' . $cataloguePath, 'debug');
                            }
                        }
                    }

                    $uploadedImages = !empty($this->request->getData('product_image')) ? $this->handleFileUploads($productId) : [];

                    if (!empty($uploadedImages)) {
                        $firstImage = $uploadedImages[0];
                        $product->product_image = $firstImage;
                        $this->Products->save($product);
                    }

                    $this->Products->ProductCategories->deleteAll(['product_id' => $productId]);

                    $selectedCategory = $this->request->getData('category_id');
                    $selectedSubCategory = $this->request->getData('sub_category_id');

                    $this->saveProductCategories($productId, [$selectedCategory, $selectedSubCategory]);

                //    $productShowroomsPricesTable = $this->getTableLocator()->get('ProductShowroomPrices');

                 //   $this->Products->ProductShowroomPrices->deleteAll(['product_id' => $productId]);

                    // if ($this->request->getData('showroom_based_price') === 'Yes') {
                    //     $this->saveShowroomPrices($productId);
                    // }

                    $this->Flash->success(__('The product has been saved.'));

                    return $this->redirect(['action' => 'index']);
                }

                $selectedCategoryId = $this->request->getData('category_id');
                $selectedSubCategoryId = $this->request->getData('sub_category_id');

                // Clear catalogue field if it contains UploadedFile object to prevent URL helper error
                if (isset($product->catalogue) && is_object($product->catalogue)) {
                    $product->catalogue = null;
                }

                // Log validation errors for debugging
                $this->log('Product save failed. Validation errors: ' . json_encode($product->getErrors()), 'debug');
                $this->Flash->error(__('The product could not be saved. Please, try again.'));
            }
            $brands = $this->Products->Brands->find('list', [
                'conditions' => ['status' => 'A']
            ])->all();

            $suppliers = "";
            // $this->Products->Suppliers->find('list', [
            //     'conditions' => ['status' => 'A']
            // ])->all();

            $showrooms = "";
            // $showrooms = $this->fetchTable('Showrooms')->find('list', [
            //     'conditions' => ['status' => 'A']
            // ])->all();

            $categories = $this->Products->ProductCategories->Categories->find('list', [
                'keyField' => 'id',
                'valueField' => 'name',
                'conditions' => ['parent_id IS' => null, 'status' => 'A']
            ])->toArray();

            $deepestCategory = null;
            if (!empty($product->product_categories)) {
                $deepestCategory = $product->product_categories[0]; // Assuming the first is the deepest
                foreach ($product->product_categories as $productCategory) {
                    if ($productCategory->level > $deepestCategory->level) {
                        $deepestCategory = $productCategory;
                    }
                }
            }

            $subcategories = [];
            if (!empty($product->product_categories)) {
                $categoryId = $product->product_categories[0]->category_id;
                $subcategories = $this->Categories
                    ->find('children', ['for' => $categoryId])
                    ->find('treeList')
                    ->toArray();
            }


            if (!empty($product->product_images)) {
                foreach ($product->product_images as &$image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }

            if (!empty($product->catalogue)) {
                $product->catalogue = $this->Media->getCloudFrontURL($product->catalogue);
            }

            $canAddBrand = $this->hasPermission('Brands', 'add');

            $title = 'Product | Edit';
            $this->set(compact('product', 'brands', 'suppliers', 'categories', 'showrooms', 'deepestCategory', 'subcategories', 'selectedCategoryId', 'selectedSubCategoryId', 'title', 'canAddBrand'));
        }


    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The product could not be deleted. Please, try again.'];

        try {
            $product = $this->Products->get($id);
            $product->status = 'D';


            $productImages = $this->Products->ProductImages->find('all', [
                'conditions' => ['ProductImages.product_id' => $id]
            ])->all();

            if ($this->Products->save($product)) {

                foreach ($productImages as $image) {
                    $this->Media->awsDelete($image->image);
                    $productImage = $this->ProductImages->get($image->id);
                    $this->ProductImages->delete($productImage);
                }
                $response = ['success' => true, 'message' => 'The product has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function editProductPrice($productId)
    {
        $this->request->allowMethod(['post']);

        $response = ['status' => 'error', 'message' => 'Something went wrong'];

        $product = $this->Products->get($productId);

        if ($this->request->is('post')) {
            $product = $this->Products->patchEntity($product, $this->request->getData(), [
                'fields' => ['purchase_price', 'product_price', 'sales_price', 'promotion_price']
            ]);
            $currencyConfig = Configure::read('Settings.Currency.format');
            $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

            if ($this->Products->save($product)) {
                $response = [
                    'status' => 'success',
                    'message' => 'Product prices updated successfully',
                    'data' => [
                        'purchase_price' => $product->purchase_price !== null ? number_format((float)($product->purchase_price), 2, '.', '') : '-',
                        'product_price' => $product->product_price !== null ? number_format((float)($product->product_price), 2, '.', '') : '-',
                        'sales_price' => $product->sales_price !== null ? number_format((float)($product->sales_price), 2, '.', '') : '-',
                        'promotion_price' => $product->promotion_price !== null ? number_format((float)($product->promotion_price), 2, '.', '') : '-',
                        'currencySymbol' => $currencySymbol
                    ]
                ];
            } else {
                $this->log('saved' . json_encode($product->getErrors()), 'debug');
                $response = ['status' => 'error', 'message' => 'Failed to update product prices'];
            }
        }
        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function fetchProductPrices($id = null)
    {
        $this->request->allowMethod(['get']);

        $product = $this->Products->get($id, [
            'fields' => ['purchase_price', 'product_price', 'sales_price', 'promotion_price']
        ]);

        if ($product) {
            $response = [
                'status' => 'success',
                'data' => [
                    'purchase_price' => $product->purchase_price,
                    'product_price' => $product->product_price,
                    'sales_price' => $product->sales_price,
                    'promotion_price' => $product->promotion_price,
                ]
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => __('Product not found.')
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function getProductPrice()
    {
        $this->autoRender = false;
        $this->request->allowMethod(['post']);

        $productId = $this->request->getData('product_id');

        $query = $this->Products->find();

    //     $caseExpression = $query->newExpr()->add("
    //     CASE
    //         WHEN ProductDeals.status = 'A'
    //              AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
    //         THEN ProductDeals.offer_price
    //         ELSE Products.promotion_price
    //     END
    // ");

        $product = $query
            ->select([
                'id' => 'Products.id',
                // 'promotion_price' => $caseExpression,
                'promotion_price' => 'Products.promotion_price',
                'product_size' => 'Products.product_size',
                'product_weight' => 'Products.product_weight',
                'sales_price' => 'Products.sales_price'
            ])
            // ->leftJoinWith('ProductDeals')
            ->where(['Products.id' => $productId])
            ->first();

        if ($product) {
            $response = [
                'status' => 'success',
                'promotion_price' => $product->promotion_price,
                'product_size' => $product->product_size,
                'product_weight' => $product->product_weight,
                'discount_price' => ($product->sales_price ?? 0) - ($product->promotion_price ?? 0)
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Product not found.'
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }


    public function approveProduct($id = null)
    {

        $this->request->allowMethod(['post']);
        $id = $this->request->getData('id');
        $product = $this->Products->get($id);

        $response = [];

        if (!$product) {
            $response = [
                'status' => 'error',
                'message' => __('Product not found.')
            ];
        } else {
            $product->approval_status = 'Approved';

            if ($this->Products->save($product)) {
                $response = [
                    'status' => 'success',
                    'message' => __('Product has been approved.')
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to approve the product. Please try again.')
                ];
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function getRelatedProductList()
    {
        $this->request->allowMethod(['ajax']);
        $productId = $this->request->getData('product_id');

        $relatedProductIds = [];
        $relatedProducts = $this->Products->RelatedProducts->find()
            ->select(['related_id'])
            ->where(['status' => 'A']);

        foreach ($relatedProducts as $relatedProduct) {
            $relatedProductIds[] = $relatedProduct->related_id;
        }

        if ($productId) {
            $relatedProductIds[] = $productId;
        }

        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->where(['id NOT IN' => $relatedProductIds])
            ->all()
            ->toArray();

        if (empty($products)) {
            $products = ['' => 'No products available'];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($products));
    }

    public function generateAndUploadBarcode($productid, $code)
    {
        $generator = new BarcodeGeneratorPNG();
        $barcodeData = $generator->getBarcode($code, $generator::TYPE_CODE_128);

        $tempFileName = $code . '_' . strtoupper(substr(uniqid(), -5)) . '.png';
        $tempFilePath = TMP . $tempFileName;
        file_put_contents($tempFilePath, $barcodeData);

        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
        $filePath = Configure::read('Constants.PRODUCT_BAR_CODE');
        $barcodeFileName = pathinfo($tempFileName, PATHINFO_FILENAME) . '_' . strtoupper(substr(uniqid(), -5)) . '.png';
        $folderPath = $uploadFolder . $filePath;

        $targetdir = WWW_ROOT . $folderPath;

        $uploadResult = $this->Media->upload($tempFilePath, $targetdir, $barcodeFileName, $folderPath);

        if ($uploadResult === 'Success') {

            $product = $this->Products->get($productid);

            $product->barcode = $folderPath . $barcodeFileName;

            if ($this->Products->save($product)) {
                unlink($tempFilePath);
                return $product->barcode;
            } else {
                unlink($tempFilePath);
                return false;
            }
        } else {
            unlink($tempFilePath);
            return false;
        }
    }

    public function generateAndUploadQRCode($productid, $code)
    {
        $qrCode = new QrCode($code);
        $writer = new PngWriter();

        $tempFileName = $code . '_' . strtoupper(substr(uniqid(), -5)) . '.png';
        $tempFilePath = TMP . $tempFileName;

        $result = $writer->write($qrCode);
        file_put_contents($tempFilePath, $result->getString());

        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
        $filePath = Configure::read('Constants.PRODUCT_QR_CODE');
        $qrCodeFileName = pathinfo($tempFileName, PATHINFO_FILENAME) . '_' . strtoupper(substr(uniqid(), -5)) . '.png';
        $folderPath = $uploadFolder . $filePath;
        $targetdir = WWW_ROOT . $folderPath;

        $uploadResult = $this->Media->upload($tempFilePath, $targetdir, $qrCodeFileName, $folderPath);

        if ($uploadResult === 'Success') {
            $product = $this->Products->get($productid);

            $product->qrcode = $folderPath . $qrCodeFileName;

            if ($this->Products->save($product)) {
                unlink($tempFilePath);
                return $product->qr_code;
            } else {
                unlink($tempFilePath);
                return false;
            }
        } else {
            unlink($tempFilePath);
            return false;
        }
    }

    public function getProductPrices()
    {
        $this->request->allowMethod(['post']);
        $id = $this->request->getData('id');

        $product = $this->Products->find()
            ->where(['id' => $id])
            ->first();

        $supplier_latest_price = array();
        //  $this->Products->SupplierProducts
        //     ->find()
        //     ->select(['supplier_price'])
        //     ->where(['product_id' => $id, 'status' => 'A'])
        //     ->order(['id' => 'DESC'])
        //     ->first();


        if ($product) {
            $response = [
                'status' => 'success',
                'message' => __('Product data retrieved successfully.'),
                'data' => $product,
                'supplier_latest_price' =>  '0,00'
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => __('No product found.'),
                'data' => null,
                'supplier_latest_price' => ''
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function addProductPrices()
    {

        $product = $this->Products->newEmptyEntity();
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $product_id = $data['product_id'];
            $product = $this->Products->get($product_id);
            $product = $this->Products->patchEntity($product, $this->request->getData());
            $this->log('POST data: ' . json_encode($product), 'debug');
            if ($this->Products->save($product)) {
                $response = [
                    'status' => 'success',
                    'message' => __('The product prices have been saved.'),
                    'data' => number_format((float)$product->sales_price, 2)
                ];
            } else {
                $response = ['status' => 'error', 'message' => __('The product prices could not be saved. Please, try again.')];
            }
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($response));
        }
    }

    public function exportToExcel()
    {
        Configure::write('debug', true);
        $spreadsheet = new Spreadsheet();
        $sheet1 = $spreadsheet->getActiveSheet();
        $sheet1->setTitle('Product Upload Template');

        $sheet2 = $spreadsheet->createSheet();
        $sheet2->setTitle('Dropdown Values');

        $headers = [
            'A1' => 'Supplier Reference Title',
            'B1' => 'Product Title',
            'C1' => 'Category',
            'D1' => 'Sub Category',
            'E1' => 'Product Size',
            'F1' => 'Product Weight (KG)',
            'G1' => 'Brand',
            'H1' => 'Product Description',
            'I1' => 'Product Images (Relative path)',
            'J1' => 'SKU'
        ];

        foreach ($headers as $cell => $value) {
            $sheet1->setCellValue($cell, $value);
        }

        $Dropdownheaders = [
            'A1' => 'Categories',
            'B1' => 'Sub Categories',
            'C1' => 'Product Size',
            'D1' => 'Brand',
        ];

        foreach ($Dropdownheaders as $cell => $value) {
            $sheet2->setCellValue($cell, $value);
        }

        $data = [
            ['Sample Supplier Title', 'Sample Product Title', '', '', '', '5', '', 'Sample description', '', 'SKU001'],
            ['Sample Supplier Title', 'Sample Product Title', '', '', '', '6', '', 'Another description', '', 'SKU002'],
        ];

        $rowNumber = 2;
        foreach ($data as $row) {
            $sheet1->fromArray($row, null, "A$rowNumber");
            $rowNumber++;
        }

        $categories = $this->Products->ProductCategories->Categories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
            'conditions' => ['parent_id IS' => null, 'status' => 'A']
        ])->toArray();

        $subCategories = $this->Products->ProductCategories->Categories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
            'conditions' => ['parent_id IS NOT' => null, 'status' => 'A'],
        ])->toArray();

        $sizes = [
            'Very Small' => __('Very Small'),
            'Small' => __('Small'),
            'Medium' => __('Medium'),
            'Large' => __('Large'),
            'Very Large' => __('Very Large')
        ];
        $brands = $this->Brands->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
            'conditions' => ['status' => 'A'],
        ])->all()->toArray();

        $categoriesWithIds = array_map(function ($id, $name) {
            $sanitized_name = $this->sanitizeString($name);
            return $id . '-' . $sanitized_name;
        }, array_keys($categories), $categories);

        $subCategoriesWithIds = array_map(function ($id, $name) {
            $sanitized_name = $this->sanitizeString($name);
            return $id . '-' . $sanitized_name;
        }, array_keys($subCategories), $subCategories);

        $brandsWithIds = array_map(function ($id, $name) {
            $sanitized_name = $this->sanitizeString($name);
            return $id . '-' . $sanitized_name;
        }, array_keys($brands), $brands);

        $spreadsheet->addNamedRange(new \PhpOffice\PhpSpreadsheet\NamedRange(
            'CategoryRange',
            $sheet2,
            'C2:C100'
        ));

        $spreadsheet->addNamedRange(new \PhpOffice\PhpSpreadsheet\NamedRange(
            'SubCategoryRange',
            $sheet2,
            'D2:D100'
        ));

        $spreadsheet->addNamedRange(new \PhpOffice\PhpSpreadsheet\NamedRange(
            'SizeRange',
            $sheet2,
            'E2:E100'
        ));

        $spreadsheet->addNamedRange(new \PhpOffice\PhpSpreadsheet\NamedRange(
            'BrandRange',
            $sheet2,
            'G2:G100'
        ));

        $this->addDropdown($sheet1, $sheet2, 'C2:C100', array_values($categoriesWithIds), 'A');
        $this->addDropdown($sheet1, $sheet2, 'D2:D100', array_values($subCategoriesWithIds), 'B');
        $this->addDropdown($sheet1, $sheet2, 'E2:E100', array_keys($sizes), 'C');
        $this->addDropdown($sheet1, $sheet2, 'G2:G100', array_values($brandsWithIds), 'D');

        $spreadsheet->setActiveSheetIndex(0);
        $spreadsheet->getActiveSheet()->setSelectedCell('A1');

        $writer = new Xlsx($spreadsheet);

        $fileName = 'sample_product_upload_format.xlsx';
        $filePath = TMP . $fileName;

        $writer->save($filePath);

        $this->response = $this->response->withType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $this->response = $this->response->withFile($filePath, ['download' => true, 'name' => $fileName]);

        return $this->response;
    }

    function sanitizeString($string)
    {
        $string = preg_replace('/[^a-zA-Z0-9_ -]/', '', $string);
        return $string;
    }

    private function addDropdown($sheet1, $sheet2, $range, $values, $tempColumn)
    {
        $startRow = 2;
        foreach ($values as $index => $value) {
            $sheet2->setCellValue($tempColumn . ($startRow + (int)$index), $value);
        }

        $endRow = $startRow + count($values) - 1;
        $validation = new \PhpOffice\PhpSpreadsheet\Cell\DataValidation();
        $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
        $validation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_STOP);
        $validation->setAllowBlank(false);
        $validation->setShowInputMessage(true);
        $validation->setShowErrorMessage(true);
        $validation->setShowDropDown(true);
        $validation->setFormula1("'Dropdown Values'!$tempColumn$startRow:$tempColumn$endRow");

        foreach (\PhpOffice\PhpSpreadsheet\Cell\Coordinate::extractAllCellReferencesInRange($range) as $cell) {
            $sheet1->getCell($cell)->setDataValidation(clone $validation);
        }

        $sheet2->getStyle("$tempColumn$startRow:$tempColumn$endRow")->getNumberFormat()->setFormatCode(';;');
    }

    private function addNamedRange($spreadsheet, $name, $values)
    {
        $spreadsheet->addNamedRange(
            new \PhpOffice\PhpSpreadsheet\NamedRange(
                $name,
                $spreadsheet->getActiveSheet(),
                '=$' . implode('$', $values)
            )
        );
    }

    public function uploadExcel()
    {
        if ($this->request->is('post')) {
            $excelFile = $this->request->getData('excelFile');
            $zipFile = $this->request->getData('zipFile');
            $zipFileName = pathinfo($zipFile->getClientFilename(), PATHINFO_FILENAME);


            if (!$excelFile || $excelFile->getError() !== UPLOAD_ERR_OK || !$zipFile || $zipFile->getError() !== UPLOAD_ERR_OK) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Please upload both Excel and ZIP files.')
                ]));
            }

            // Extract ZIP file
            $TEMP_IMAGE_FOLDER = Configure::read('Constants.TEMP_IMAGE_FOLDER');
            $extractPath = WWW_ROOT . $TEMP_IMAGE_FOLDER . 'uploaded_images_' . time();
            $fullExtractPath = rtrim($extractPath, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $zipFileName;

            if (!file_exists($extractPath)) {
                mkdir($extractPath, 0777, true);
            }

            $zip = new \ZipArchive;
            if ($zip->open($zipFile->getStream()->getMetadata('uri')) === true) {
                $zip->extractTo($extractPath);
                $zip->close();

                // Check extracted files
                $extractedFiles = scandir($extractPath);
                $this->log(json_encode($extractPath), 'debug');
                if (count($extractedFiles) <= 2) { // Only '.' and '..' exist
                    $this->log(json_encode($extractPath), 'debug');
                }
            } else {
                $this->log('failed', 'debug');
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Failed to extract ZIP file.')
                ]));
            }

            if ($excelFile && $excelFile->getError() === UPLOAD_ERR_OK) {
                $allowedFileNames = ['sample_product_upload_format.xlsx', 'product_list_template.xlsx'];
                $fileName = $excelFile->getClientFilename();

                // Optionally validate the file name here...
                // if (!in_array($fileName, $allowedFileNames)) { ... }

                $spreadsheet = IOFactory::load($excelFile->getStream()->getMetadata('uri'));
                $sheet = $spreadsheet->getActiveSheet();

                $expectedHeaders = [
                    'A' => 'Supplier Reference Title',
                    'B' => 'Product Title',
                    'C' => 'Category',
                    'D' => 'Sub Category',
                    'E' => 'Product Size',
                    'F' => 'Product Weight (KG)',
                    'G' => 'Brand',
                    'H' => 'Product Description',
                    'I' => 'Product Images (Relative path)',
                    'J' => 'SKU'
                ];

                // Validate headers
                foreach ($expectedHeaders as $column => $expectedHeader) {
                    $cellValue = trim($sheet->getCell($column . '1')->getValue());
                    if ($cellValue !== $expectedHeader) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'status' => 'error',
                            'message' => __('Invalid file format. Please use the correct template.')
                        ]));
                    }
                }

                $tempPath = $excelFile->getStream()->getMetadata('uri');
                $spreadsheet = IOFactory::load($tempPath);
                $sheetData = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
                $headers = $sheetData[1];
                unset($sheetData[1]);

                $productsTable = $this->fetchTable('Products');
                $erroredProducts = [];

                foreach ($sheetData as $rowIndex => $row) {
                    // Skip completely empty rows
                    if (!array_filter($row)) {
                        continue;
                    }
                    try {
                        $productData = [
                            'reference_name' => $row['A'] ?? null,
                            'name' => $row['B'] ?? null,
                            'product_size' => $row['E'] ?? null,
                            'product_weight' => $row['F'] ?? null,
                            'brand_id' => isset($row['G']) ? explode('-', $row['G'])[0] : null,
                            'description' => $row['H'] ?? null,
                            'sku' => $row['J'] ?? null,
                        ];

                        $productEntity = $productsTable->newEntity($productData);
                        if (!$productsTable->save($productEntity)) {
                            $erroredProducts[] = [
                                'row_index' => $rowIndex,
                                'data' => $productData,
                                'errors' => $productEntity->getErrors()
                            ];
                            continue;
                        } else {
                            $productId = $productEntity->id;
                            $this->generateAndUploadBarcode($productId, $productEntity->sku);
                            $this->generateAndUploadQRCode($productId, $productEntity->sku);
                            $selectedCategory = isset($row['C']) ? explode('-', $row['C'])[0] : null;
                            $selectedSubCategory = isset($row['D']) ? explode('-', $row['D'])[0] : null;
                            $this->saveProductCategories($productId, [$selectedCategory, $selectedSubCategory]);

                            // Process images from Excel column I (comma separated list)
                            $imageFilePaths = explode(',', $row['I'] ?? '');


                            $uploadedImages = $this->uploadProductImagesFromExcel(
                                $productEntity->id,
                                $imageFilePaths,
                                $fullExtractPath
                            );
                            if ($uploadedImages['success'] === 'failed') {
                                $errorMessageCombined = implode(', ', $uploadedImages['errors']);
                                $erroredProducts[] = [
                                    'row_index' => $rowIndex,
                                    'data' => $productData,
                                    'errors' => $errorMessageCombined
                                ];
                                // Optionally, remove related product categories and the product record itself
                                $productCategoriesTable = $this->getTableLocator()->get('ProductCategories');
                                $productCategoriesTable->deleteAll(['product_id' => $productId]);
                                $productsTable->delete($productEntity);
                                continue;
                            } else {
                                if (!empty($uploadedImages['uploadedImages'])) {
                                    // Update product with first uploaded image as default
                                    $productEntity->product_image = $uploadedImages['uploadedImages'][0];
                                    $productsTable->save($productEntity);
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        if (is_dir($fullExtractPath)) {
                            $files = array_diff(scandir($fullExtractPath), ['.', '..']);
                            foreach ($files as $file) {
                                unlink($fullExtractPath . DIRECTORY_SEPARATOR . $file);
                            }
                            rmdir($fullExtractPath);
                            rmdir($extractPath);
                        }
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'status' => 'error',
                            'message' => __('Failed to process the file: ') . $e->getMessage()
                        ]));
                    }
                }
                if (is_dir($fullExtractPath)) {
                    $files = array_diff(scandir($fullExtractPath), ['.', '..']);
                    foreach ($files as $file) {
                        unlink($fullExtractPath . DIRECTORY_SEPARATOR . $file);
                    }
                    rmdir($fullExtractPath);
                    rmdir($extractPath);
                }

                if (!empty($erroredProducts)) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Some rows or images could not be saved.'),
                        'erroredProducts' => $erroredProducts
                    ]));
                } else {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'success',
                        'message' => __('File uploaded successfully, and all data saved.')
                    ]));
                }
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('No valid file was uploaded.')
                ]));
            }
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'error',
            'message' => __('Invalid request.')
        ]));
    }

    public function uploadProductImagesFromExcel($productId, $imageFilePaths, $extractPath)
    {
        $uploadedImages = [];
        $errors = [];
        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
        $filePathFolder = Configure::read('Settings.PRODUCT');
        $folderPath = $uploadFolder . $filePathFolder;
        $targetdir = WWW_ROOT . $folderPath;
        $i = 0;

        $allowedImageFormats = Configure::read('Constants.PRODUCT_IMAGE_TYPE');
        $maxImageSize = Configure::read('Constants.PRODUCT_IMAGE_SIZE') * 1024 * 1024;
        $minWidth = Configure::read('Constants.PRODUCT_IMAGE_MIN_WIDTH');
        $maxWidth = Configure::read('Constants.PRODUCT_IMAGE_MAX_WIDTH');
        $minHeight = Configure::read('Constants.PRODUCT_IMAGE_MIN_HEIGHT');
        $maxHeight = Configure::read('Constants.PRODUCT_IMAGE_MAX_HEIGHT');

        // Folder where the images were uploaded from the client (temporary folder)
        $tempImageFolder = WWW_ROOT . Configure::read('Constants.TEMP_IMAGE_FOLDER');

        foreach ($imageFilePaths as $filePath) {
            if (empty($filePath)) {
                continue;
            }

            // Clean up the file path from Excel
            $filePath = trim($filePath, " \t\n\r\x0B\"'");

            // First, check if the file exists at the given path.
            $fullFilePath = rtrim($extractPath, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . basename($filePath);

            if (!file_exists($fullFilePath)) {
                $tempFilePath = rtrim($tempImageFolder, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . basename($filePath);
                if (file_exists($tempFilePath)) {
                    $fullFilePath = $tempFilePath;
                } else {
                    $errors[] = __('Image file not found: {0}', $filePath);
                    continue;
                }
            }

            $imageExt = strtolower(pathinfo($fullFilePath, PATHINFO_EXTENSION));
            $imageName = basename($fullFilePath);

            if (!in_array($imageExt, $allowedImageFormats)) {
                $errors[] = __('Invalid file type for product image: {0}. Only the following formats are allowed: {1}', [$imageName, implode(', ', $allowedImageFormats)]);
                continue;
            }

            $imageSize = filesize($fullFilePath);
            if ($imageSize > $maxImageSize) {
                $errors[] = __('Product image size exceeds the maximum allowed size of {0} MB for image: {1}.', [($maxImageSize / (1024 * 1024)), $imageName]);
                continue;
            }

            list($width, $height) = getimagesize($fullFilePath);
            if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                $errors[] = __('Product image dimensions must be between {0}x{1} and {2}x{3} pixels for image: {4}.', [$minWidth, $minHeight, $maxWidth, $maxHeight, $imageName]);
                continue;
            }

            // Proceed with upload
            $file = new \SplFileInfo($fullFilePath);
            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
            $imageFile = pathinfo($file->getFilename(), PATHINFO_FILENAME) . '_' . $rand . '.' . $file->getExtension();

            $uploadResult = $this->Media->upload($fullFilePath, $targetdir, $imageFile, $folderPath);

            if ($uploadResult !== 'Success') {
                $errors[] = __('Image ' . $file->getFilename() . ' could not be uploaded.');
            } else {
                $productImage = $this->Products->ProductImages->newEmptyEntity();
                $productImage->product_id = $productId;
                $productImage->image = $folderPath . $imageFile;
                $productImage->media_type = 'Image';
                $productImage->image_default = ($i == 0) ? 1 : 0;

                if ($this->Products->ProductImages->save($productImage)) {
                    $uploadedImages[] = $folderPath . $imageFile;
                    $i++;
                } else {
                    $errors[] = __('Image ' . $file->getFilename() . ' could not be saved.');
                }
            }
        }

        if (!empty($errors)) {
            return ['success' => 'failed', 'errors' => $errors];
        }

        return ['success' => 'success', 'uploadedImages' => $uploadedImages];
    }

    private function handleCatalogueUpload($catalogueFile, $productId)
    {
        try {
            $this->log('Starting catalogue upload for product ID: ' . $productId, 'debug');

            $fileName = trim($catalogueFile->getClientFilename());

            if (empty($fileName)) {
                $this->log('Catalogue upload failed: Empty filename', 'debug');
                $this->Flash->error(__('Catalogue filename is empty.'));
                return false;
            }

            $this->log('Catalogue filename: ' . $fileName, 'debug');

            // Validate file type
            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
            if (strtolower($ext) !== 'pdf') {
                $this->Flash->error(__('Only PDF files are allowed for catalogue.'));
                $this->log('Catalogue upload failed: Invalid file type - ' . $ext, 'debug');
                return false;
            }

            // Validate file size (10MB)
            $fileSize = $catalogueFile->getSize();
            $this->log('Catalogue file size: ' . $fileSize . ' bytes', 'debug');
            if ($fileSize > 10485760) {
                $this->Flash->error(__('Catalogue file size must be 10 MB or less.'));
                $this->log('Catalogue upload failed: File too large - ' . $fileSize, 'debug');
                return false;
            }

            $catalogueTmpName = $catalogueFile->getStream()->getMetadata('uri');
            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
            $filePath = Configure::read('Settings.PRODUCT'); // Using same folder as products
            $folderPath = $uploadFolder . $filePath;
            $targetdir = WWW_ROOT . $folderPath;
            $catalogueFileName = pathinfo($fileName, PATHINFO_FILENAME) . '_catalogue_' . $rand . '.' . $ext;

            $this->log('Attempting catalogue upload: ' . $catalogueFileName, 'debug');
            $uploadResult = $this->Media->upload($catalogueTmpName, $targetdir, $catalogueFileName, $folderPath);

            if ($uploadResult !== 'Success') {
                $this->Flash->error(__('Catalogue file could not be uploaded. Please, try again.'));
                $this->log('Catalogue upload failed: ' . $uploadResult, 'debug');
                return false;
            }

            $this->log('Catalogue uploaded successfully: ' . $folderPath . $catalogueFileName, 'debug');
            return $folderPath . $catalogueFileName;
        } catch (\Exception $e) {
            $this->log('Catalogue upload exception: ' . $e->getMessage(), 'error');
            $this->Flash->error(__('An error occurred while uploading the catalogue.'));
            return false;
        }
    }

    public function clone($id = null)
    {
        $originalProduct = $this->Products->get($id, [
            'contain' => [
                'ProductCategories',
                'ProductImages' => function ($q) {
                    return $q->where(['ProductImages.status' => 'A']);
                },
                'ProductAttributes'
            ]
        ]);

        if (!$originalProduct) {
            $this->Flash->error(__('Product not found.'));
            return $this->redirect(['action' => 'index']);
        }

        // Create new product entity
        $clonedProduct = $this->Products->newEmptyEntity();

        // Copy all fields except id, created, modified, and add "Copy of " prefix to text fields
        $fieldsToPrefix = ['name', 'reference_name', 'meta_title'];
        $fieldsToSkip = ['id', 'created', 'modified', 'sku', 'url_key', 'catalogue'];

        foreach ($originalProduct->toArray() as $field => $value) {
            if (!in_array($field, $fieldsToSkip)) {
                if (in_array($field, $fieldsToPrefix) && !empty($value)) {
                    $clonedProduct->$field = 'Copy of ' . $value;
                } else {
                    $clonedProduct->$field = $value;
                }
            }
        }

        // Generate unique SKU and URL key
        $baseSku = $originalProduct->sku . '_copy';
        $counter = 1;
        $newSku = $baseSku;

        while ($this->Products->find()->where(['sku' => $newSku])->first()) {
            $newSku = $baseSku . '_' . $counter;
            $counter++;
        }

        $clonedProduct->sku = $newSku;
        $clonedProduct->url_key = $this->generateUniqueUrlKey($clonedProduct->name, 'Products');
        $clonedProduct->approval_status = 'Pending';

        if ($this->Products->save($clonedProduct)) {
            $newProductId = $clonedProduct->id;

            // Clone product categories
            if (!empty($originalProduct->product_categories)) {
                foreach ($originalProduct->product_categories as $category) {
                    $newCategory = $this->Products->ProductCategories->newEmptyEntity();
                    $newCategory->product_id = $newProductId;
                    $newCategory->category_id = $category->category_id;
                    $newCategory->level = $category->level;
                    $this->Products->ProductCategories->save($newCategory);
                }
            }

            // Clone product images
            if (!empty($originalProduct->product_images)) {
                foreach ($originalProduct->product_images as $image) {
                    $newImage = $this->Products->ProductImages->newEmptyEntity();
                    $newImage->product_id = $newProductId;
                    $newImage->image = $image->image;
                    $newImage->media_type = $image->media_type;
                    $newImage->image_default = $image->image_default;
                    $newImage->video = $image->video;
                    $newImage->status = $image->status;
                    $this->Products->ProductImages->save($newImage);
                }
            }

            // Clone product attributes
            if (!empty($originalProduct->product_attributes)) {
                foreach ($originalProduct->product_attributes as $attribute) {
                    $newAttribute = $this->Products->ProductAttributes->newEmptyEntity();
                    $newAttribute->product_id = $newProductId;
                    $newAttribute->attribute_id = $attribute->attribute_id;
                    $newAttribute->attribute_value_id = $attribute->attribute_value_id;
                    $newAttribute->attribute_description = $attribute->attribute_description;
                    $newAttribute->status = $attribute->status;
                    $this->Products->ProductAttributes->save($newAttribute);
                }
            }

            $this->Flash->success(__('Product has been cloned successfully.'));
            return $this->redirect(['action' => 'edit', $newProductId]);
        } else {
            $this->Flash->error(__('Unable to clone the product. Please try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
