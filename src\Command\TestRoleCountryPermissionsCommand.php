<?php
declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use <PERSON>ake\Console\Arguments;
use <PERSON>ake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\ORM\TableRegistry;

/**
 * TestRoleCountryPermissions command.
 */
class TestRoleCountryPermissionsCommand extends Command
{
    /**
     * Hook method for defining this command's option parser.
     *
     * @param \Cake\Console\ConsoleOptionParser $parser The parser to be defined
     * @return \Cake\Console\ConsoleOptionParser The built parser.
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser = parent::buildOptionParser($parser);
        $parser->setDescription('Test role country permissions system');

        return $parser;
    }

    /**
     * Test the role country permissions system
     *
     * @param \Cake\Console\Arguments $args The command arguments.
     * @param \Cake\Console\ConsoleIo $io The console io
     * @return null|void|int The exit code or null for success
     */
    public function execute(Arguments $args, ConsoleIo $io)
    {
        $io->out('Testing Role Country Permissions System...');
        $io->hr();
        
        try {
            // Test 1: Check if tables exist and have data
            $this->testTablesExist($io);
            
            // Test 2: Check roles and their access types
            $this->testRolesAccessTypes($io);
            
            // Test 3: Check role country permissions
            $this->testRoleCountryPermissions($io);
            
            // Test 4: Test role methods
            $this->testRoleMethods($io);
            
            $io->hr();
            $io->success('All tests completed successfully!');
            
        } catch (\Exception $e) {
            $io->error('Test failed: ' . $e->getMessage());
            return static::CODE_ERROR;
        }
        
        return static::CODE_SUCCESS;
    }
    
    private function testTablesExist($io)
    {
        $io->out('Test 1: Checking if tables exist...');
        
        $rolesTable = TableRegistry::getTableLocator()->get('Roles');
        $roleCountryPermissionsTable = TableRegistry::getTableLocator()->get('RoleCountryPermissions');
        $countriesTable = TableRegistry::getTableLocator()->get('Countries');
        
        $rolesCount = $rolesTable->find()->count();
        $permissionsCount = $roleCountryPermissionsTable->find()->count();
        $countriesCount = $countriesTable->find()->count();
        
        $io->out("  - Roles table: {$rolesCount} records");
        $io->out("  - RoleCountryPermissions table: {$permissionsCount} records");
        $io->out("  - Countries table: {$countriesCount} records");
        $io->out('  ✓ All tables exist and have data');
    }
    
    private function testRolesAccessTypes($io)
    {
        $io->out('Test 2: Checking roles access types...');
        
        $rolesTable = TableRegistry::getTableLocator()->get('Roles');
        $roles = $rolesTable->find()->toArray();
        
        foreach ($roles as $role) {
            $accessType = $role->country_access_type ?? 'not set';
            $io->out("  - Role: {$role->name} (ID: {$role->id}) - Access Type: {$accessType}");
        }
        $io->out('  ✓ Roles access types checked');
    }
    
    private function testRoleCountryPermissions($io)
    {
        $io->out('Test 3: Checking role country permissions...');
        
        $roleCountryPermissionsTable = TableRegistry::getTableLocator()->get('RoleCountryPermissions');
        $permissions = $roleCountryPermissionsTable->find()
            ->contain(['Roles', 'Countries'])
            ->toArray();
        
        foreach ($permissions as $permission) {
            $roleName = $permission->role->name ?? 'Unknown';
            $countryName = $permission->country ? $permission->country->name : 'All Countries';
            $access = $permission->can_access ? 'Yes' : 'No';
            $io->out("  - Role: {$roleName} → Country: {$countryName} → Access: {$access}");
        }
        $io->out('  ✓ Role country permissions checked');
    }
    
    private function testRoleMethods($io)
    {
        $io->out('Test 4: Testing role methods...');
        
        $rolesTable = TableRegistry::getTableLocator()->get('Roles');
        $roles = $rolesTable->find()->limit(3)->toArray();
        
        foreach ($roles as $role) {
            $io->out("  Testing role: {$role->name} (ID: {$role->id})");
            
            // Test getAccessibleCountries
            try {
                $accessibleCountries = $rolesTable->getAccessibleCountries($role->id);
                if ($accessibleCountries === null) {
                    $io->out("    - Can access: All countries");
                } elseif ($accessibleCountries === 'user_country') {
                    $io->out("    - Can access: User's assigned country only");
                } elseif (is_array($accessibleCountries)) {
                    $countryCount = count($accessibleCountries);
                    $io->out("    - Can access: {$countryCount} specific countries");
                }
            } catch (\Exception $e) {
                $io->out("    - Error testing getAccessibleCountries: " . $e->getMessage());
            }
            
            // Test canAccessCountry
            try {
                $canAccessAll = $rolesTable->canAccessCountry($role->id, null);
                $canAccessSpecific = $rolesTable->canAccessCountry($role->id, 1); // Test with country ID 1
                $io->out("    - Can access all countries: " . ($canAccessAll ? 'Yes' : 'No'));
                $io->out("    - Can access country ID 1: " . ($canAccessSpecific ? 'Yes' : 'No'));
            } catch (\Exception $e) {
                $io->out("    - Error testing canAccessCountry: " . $e->getMessage());
            }
        }
        $io->out('  ✓ Role methods tested');
    }
}
