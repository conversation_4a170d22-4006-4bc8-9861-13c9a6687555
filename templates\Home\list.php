<style>
    #loader { text-align: center; padding: 20px; display: none; }
  </style>
    <section class="product-breadcrumb ">
        <div class="container">
            <nav aria-label="breadcrumb" class="pt-3 pb-1 pb-lg-4 pt-lg-5">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/"><?= __('Home') ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page">Split AC</li>

                </ol>
            </nav>
        </div>
    </section>

<section class="o-general-banner py-5 mobile">
<div class="container">
<div class="row align-items-center">
<div class="col-lg-5 ">
<div class="blur-bg"><img class="img-fluid" src="../../img/ozone/o-general.png">
<div id="carouselExampleRide" class="carousel slide" data-bs-ride="true">
                            <div class="carousel-inner">
                                <div class="carousel-item active">
                                    <p class="my-4">
                                        Celebrate comfort and sustainable quality with the original pioneers of
                                        perfect cooling.
                                    </p>
                                    <button class="btn btn-outline-dark px-4">Shop O General <span>→</span></button>
                                </div>
                                <div class="carousel-item">
                                    <p class="my-4">
                                        Celebrate comfort and sustainable quality with the original pioneers of
                                        perfect cooling.
                                    </p>
                                    <button class="btn btn-outline-dark px-4">Shop O General <span>→</span></button>
                                </div>
                                <div class="carousel-item">
                                    <p class="my-4">
                                        Celebrate comfort and sustainable quality with the original pioneers of
                                        perfect cooling.
                                    </p>
                                    <button class="btn btn-outline-dark px-4">Shop O General <span>→</span></button>
                                </div>
                            </div>
                        </div>
                        <div class="arrow">
                            <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleRide"
                                data-bs-slide="prev">
                                <span class="carousel-control-prev-icon" aria-hidden="true">←</span>
                                <span class="visually-hidden">Previous</span>
                            </button>
                            <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleRide"
                                data-bs-slide="next">
                                <span class="carousel-control-next-icon" aria-hidden="true">→</span>
                                <span class="visually-hidden">Next</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7">&nbsp;</div>
            </div>
        </div>
    </section>
    <!-- Brand Banner -->
    <section class="product-general-banner py-lg-5 desktop">
        <div class="blur-bg">
            <div class="container">

                <div class="row align-items-center">
                    <div class="col-lg-12 ">

                        <img src="../../img/ozone/Ozonex_Logo_4.png" class="img-fluid">
                        <div id="carouselExampleRide" class="carousel slide" data-bs-ride="true">
                            <div class="carousel-inner">
                                <div class="carousel-item active">
                                    <p class="my-4">
                                        Celebrate comfort and sustainable quality with the original pioneers of
                                        perfect cooling.
                                    </p>
                                    <button class="btn btn-outline-dark px-4">Shop O General <span>→</span></button>
                                </div>
                                <div class="carousel-item">
                                    <p class="my-4">
                                        Celebrate comfort and sustainable quality with the original pioneers of
                                        perfect cooling.
                                    </p>
                                    <button class="btn btn-outline-dark px-4">Shop O General <span>→</span></button>
                                </div>
                                <div class="carousel-item">
                                    <p class="my-4">
                                        Celebrate comfort and sustainable quality with the original pioneers of
                                        perfect cooling.
                                    </p>
                                    <button class="btn btn-outline-dark px-4">Shop O General <span>→</span></button>
                                </div>
                            </div>
                        </div>
                        <div class="arrow">
                            <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleRide"
                                data-bs-slide="prev">
                                <span class="carousel-control-prev-icon" aria-hidden="true">←</span>
                                <span class="visually-hidden">Previous</span>
                            </button>
                            <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleRide"
                                data-bs-slide="next">
                                <span class="carousel-control-next-icon" aria-hidden="true">→</span>
                                <span class="visually-hidden">Next</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7"></div>
            </div>
        </div>
    </section>

    <!-- Leading Company Section -->


    <!-- Air Quality Section -->
    <section class="py-5 products">
        <div class="container">                                                          
            <div class="row">
                <div class="col-lg-2 desktop-filter">
                    <div class="Filters" style="max-width: 300px;">
                        <div class="title d-flex justify-content-between align-items-center mb-3">
                            <h5>Filters</h5>
                            <a href="#" class="text-success" onClick="clearAllFilters()">Clear all</a>
                        </div>

                        <!-- Brand -->
                        <div class="filter-section">
                            <div class="filter-title" data-bs-toggle="collapse" data-bs-target="#brandFilter">
                                Brands <span>&#9660;</span>
                            </div>
                            <div id="brandFilter" class="collapse show mt-2">
                                <?php
                                // Get selected brands from URL
                                $selectedBrands = [];
                                if (!empty($this->request->getQuery('brands'))) {
                                    $selectedBrands = explode(',', $this->request->getQuery('brands'));
                                }

                                // Check if brands are available in the response
                                $availableBrands = $productList['available_brands'] ?? $brands ?? [];

                                foreach($availableBrands as $brand):
                                    $isChecked = in_array($brand['id'], $selectedBrands);
                                ?>
                                <div class="form-check">
                                    <input class="form-check-input brand-filter"
                                           type="checkbox"
                                           id="brand<?= $brand['id'] ?>"
                                           name="brands"
                                           value="<?= $brand['id'] ?>"
                                           <?= $isChecked ? 'checked' : '' ?>
                                           onchange="updateBrandFilter()">
                                    <label class="form-check-label" for="brand<?= $brand['id'] ?>">
                                        <?= $brand['name'] ?>
                                        <span class="text-success"></span>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Price Range Filter -->
                        <div class="filter-section">
                            <div class="filter-title " data-bs-toggle="collapse" data-bs-target="#pricerange">
                                Price Range
                                <span>&#9660;</span>
                            </div>
                            <div id="pricerange">
                                <div id="priceSlider" class="mt-5"></div>

                                <div class="d-flex range-value justify-content-between mt-3">
                                    <input type="number" class="form-control text-center fw-bold" id="minPrice"
                                        value="<?= $this->request->getQuery('min_price', 0) ?>" style="max-width: 80px; border-radius: 10px;" />
                                    <input type="number" class="form-control text-center fw-bold text-primary"
                                        id="maxPrice" value="<?= $this->request->getQuery('max_price', 99999) ?>" style="max-width: 80px; border-radius: 10px;" />
                                </div>
                            </div>
                        </div>

                    <!-- Attribute Filters -->
                     <?php if(isset($selectedCategory['selectedCategory']['category_attributes'])): ?>
                        <?php foreach($selectedCategory['selectedCategory']['category_attributes'] as $category_attribute): ?>
                            <?php if(isset($category_attribute['attribute']['name'])): ?>
                                    <div class="filter-section">
                                        <div class="filter-title" data-bs-toggle="collapse" data-bs-target="#producttypeFilter<?= $category_attribute['attribute']['id'] ?>">
                                           <?= $category_attribute['attribute']['name'] ?>   <span>&#9660;</span>
                                        </div>

                                        <?php if(isset($category_attribute['attribute']['attribute_values'])): ?>
                                            <?php foreach($category_attribute['attribute']['attribute_values'] as $attribute_value): ?>
                                                    <div id="producttypeFilter<?= $category_attribute['attribute']['id'] ?>" class="collapse show mt-2">
                                                        <div class="form-check">
                                                            <?php
                                                                // Get selected attributes from URL
                                                                $selectedAttributes = [];
                                                                if (!empty($this->request->getQuery('attributes'))) {
                                                                    $selectedAttributes = explode(',', $this->request->getQuery('attributes'));
                                                                }

                                                                // Create a unique identifier for this attribute value
                                                                $attributeValueId = $category_attribute['attribute']['id'] . '-' . $attribute_value['id'];
                                                                $isChecked = in_array($attributeValueId, $selectedAttributes);
                                                            ?>
                                                            <input class="form-check-input attribute-filter"
                                                                   type="checkbox"
                                                                   id="attributevalue<?= $category_attribute['attribute']['id'] ?><?= $attribute_value['id'] ?>"
                                                                   name="attributes"
                                                                   value="<?= $attributeValueId ?>"
                                                                   <?= $isChecked ? 'checked' : '' ?>
                                                                   onchange="updateAttributeFilter()">
                                                            <label class="form-check-label" for="attributevalue<?= $category_attribute['attribute']['id'] ?><?= $attribute_value['id'] ?>"> <?= $attribute_value['value'] ?> <span
                                                            class="text-success"></span></label>
                                                        </div>
                                                    </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>




                                    </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    </div>
                </div>
                <div class="col-lg-10 ">
                    <div class="mobile-filter">
                        <div class="d-flex justify-content-between "><div class="d-flex justify-content-between align-items-center"><button class="btn filter-btn  mb-0" onclick="toggleFilterPanel()"><img src="../../img/ozone/filter-edit.png" alt="Filter" class="img-fluid filter-img">Filter</button><a href="#" class="flt-clr-btn" onClick="clearAllFilters()">Clear all</a></div><div class="popularity-filter">
                                <div class="dropdown ">
                                    <?php
                                    // Get current sort parameter or default to 'popularity'
                                    $currentSort = $this->request->getQuery('sort', 'popularity');

                                    // Map sort values to display labels
                                    $sortLabels = [
                                        'popularity' => __('Popularity'),
                                        'low-to-high' => __('Low to High'),
                                        'high-to-low' => __('High to Low'),
                                        'new-arrival' => __('New Arrival'),
                                        'relevance' => __('Relevance'),
                                        'discount' => __('Discount')
                                    ];

                                    // Get the display label for the current sort
                                    $currentSortLabel = $sortLabels[$currentSort] ?? __('Popularity');
                                    ?>
                                    <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <?= $currentSortLabel ?>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php
                                        // Get current URL parameters
                                        $params = $this->request->getQueryParams();

                                        // Define sort options
                                        $sortOptions = [
                                            'popularity' => __('Popularity'),
                                            'low-to-high' => __('Low to High'),
                                            'high-to-low' => __('High to Low'),
                                            'new-arrival' => __('New Arrival'),
                                            'relevance' => __('Relevance'),
                                            'discount' => __('Discount')
                                        ];

                                        // Create links with preserved parameters
                                        foreach ($sortOptions as $sortValue => $sortLabel) {
                                            // Update sort parameter while preserving others
                                            $newParams = $params;
                                            $newParams['sort'] = $sortValue;

                                            // Generate URL with all parameters
                                            $url = $this->Url->build(['?' => $newParams]);

                                            // Determine if this option is currently active
                                            $isActive = ($params['sort'] ?? 'popularity') == $sortValue;
                                            $activeClass = $isActive ? 'active' : '';

                                            echo "<li><a class=\"dropdown-item {$activeClass}\" href=\"{$url}\">{$sortLabel}</a></li>";
                                        }
                                        ?>
                                    </ul>
                                </div>
                            </div></div>
                    <div class="filtermenu">
                        
                        <!-- Slide Panel -->
                        <div id="filterPanel" class="filter-slide-panel">
                            <div class="Filters" style="max-width: 100%;">
                                <!-- <div class="title d-flex justify-content-between align-items-center mb-3">
                                    <h5>Filters</h5>
                                    
                                </div> -->

                                <!-- Brand -->
                                <div class="filter-section">
                                    <div class="filter-title" data-bs-toggle="collapse" data-bs-target="#brandFilter">
                                        Brands <span>&#9660;</span>
                                    </div>
                                    <div id="brandFilter" class="collapse show mt-2">
                                        <?php
                                        // Get selected brands from URL
                                        $selectedBrands = [];
                                        if (!empty($this->request->getQuery('brands'))) {
                                            $selectedBrands = explode(',', $this->request->getQuery('brands'));
                                        }

                                        // Check if brands are available in the response
                                        $availableBrands = $productList['available_brands'] ?? $brands ?? [];

                                        foreach($availableBrands as $brand):
                                            $isChecked = in_array($brand['id'], $selectedBrands);
                                        ?>
                                        <div class="form-check">
                                            <input class="form-check-input brand-filter"
                                                type="checkbox"
                                                id="brand<?= $brand['id'] ?>"
                                                name="brands"
                                                value="<?= $brand['id'] ?>"
                                                <?= $isChecked ? 'checked' : '' ?>
                                                onchange="updateBrandFilter()">
                                            <label class="form-check-label" for="brand<?= $brand['id'] ?>">
                                                <?= $brand['name'] ?>
                                                <span class="text-success"></span>
                                            </label>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <!-- Price Range Filter -->
                                <div class="filter-section">
                                    <div class="filter-title " data-bs-toggle="collapse" data-bs-target="#pricerange">
                                        Price Range
                                        <span>&#9660;</span>
                                    </div>
                                    <div id="pricerange">
                                        <div id="priceSlider" class="mt-5"></div>

                                        <div class="d-flex range-value justify-content-between mt-3">
                                            <input type="number" class="form-control text-center fw-bold" id="minPrice"
                                                value="<?= $this->request->getQuery('min_price', 0) ?>" style="max-width: 80px; border-radius: 10px;" />
                                            <input type="number" class="form-control text-center fw-bold text-primary"
                                                id="maxPrice" value="<?= $this->request->getQuery('max_price', 99999) ?>" style="max-width: 80px; border-radius: 10px;" />
                                        </div>
                                    </div>
                                </div>

                            <!-- Attribute Filters -->
                            <?php if(isset($selectedCategory['selectedCategory']['category_attributes'])): ?>
                                <?php foreach($selectedCategory['selectedCategory']['category_attributes'] as $category_attribute): ?>
                                    <?php if(isset($category_attribute['attribute']['name'])): ?>
                                            <div class="filter-section">
                                                <div class="filter-title" data-bs-toggle="collapse" data-bs-target="#producttypeFilter<?= $category_attribute['attribute']['id'] ?>">
                                                <?= $category_attribute['attribute']['name'] ?>   <span>&#9660;</span>
                                                </div>

                                                <?php if(isset($category_attribute['attribute']['attribute_values'])): ?>
                                                    <?php foreach($category_attribute['attribute']['attribute_values'] as $attribute_value): ?>
                                                            <div id="producttypeFilter<?= $category_attribute['attribute']['id'] ?>" class="collapse show mt-2">
                                                                <div class="form-check">
                                                                    <?php
                                                                        // Get selected attributes from URL
                                                                        $selectedAttributes = [];
                                                                        if (!empty($this->request->getQuery('attributes'))) {
                                                                            $selectedAttributes = explode(',', $this->request->getQuery('attributes'));
                                                                        }

                                                                        // Create a unique identifier for this attribute value
                                                                        $attributeValueId = $category_attribute['attribute']['id'] . '-' . $attribute_value['id'];
                                                                        $isChecked = in_array($attributeValueId, $selectedAttributes);
                                                                    ?>
                                                                    <input class="form-check-input attribute-filter"
                                                                        type="checkbox"
                                                                        id="attributevalue<?= $category_attribute['attribute']['id'] ?><?= $attribute_value['id'] ?>"
                                                                        name="attributes"
                                                                        value="<?= $attributeValueId ?>"
                                                                        <?= $isChecked ? 'checked' : '' ?>
                                                                        onchange="updateAttributeFilter()">
                                                                    <label class="form-check-label" for="attributevalue<?= $category_attribute['attribute']['id'] ?><?= $attribute_value['id'] ?>"> <?= $attribute_value['value'] ?> <span
                                                                    class="text-success"></span></label>
                                                                </div>
                                                            </div>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>




                                            </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            </div>
                        </div>
                    </div>
                </div>
                    <div class="px-2 product-list-container" id="productListContainer">

                        <div class="show-results mb-5 desktop-filter">
                            <p><?= __('Showing') ?> <span id='updateFilterdCount'> <?= $productList['filtered_count'] ?> </span> <?= __(' from total') ?> <?= $productList['total_count'] ?></p>
                            <div class="popularity-filter">
                                <div class="dropdown ">
                                    <?php
                                    // Get current sort parameter or default to 'popularity'
                                    $currentSort = $this->request->getQuery('sort', 'popularity');

                                    // Map sort values to display labels
                                    $sortLabels = [
                                        'popularity' => __('Popularity'),
                                        'low-to-high' => __('Low to High'),
                                        'high-to-low' => __('High to Low'),
                                        'new-arrival' => __('New Arrival'),
                                        'relevance' => __('Relevance'),
                                        'discount' => __('Discount')
                                    ];

                                    // Get the display label for the current sort
                                    $currentSortLabel = $sortLabels[$currentSort] ?? __('Popularity');
                                    ?>
                                    <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <?= $currentSortLabel ?>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php
                                        // Get current URL parameters
                                        $params = $this->request->getQueryParams();

                                        // Define sort options
                                        $sortOptions = [
                                            'popularity' => __('Popularity'),
                                            'low-to-high' => __('Low to High'),
                                            'high-to-low' => __('High to Low'),
                                            'new-arrival' => __('New Arrival'),
                                            'relevance' => __('Relevance'),
                                            'discount' => __('Discount')
                                        ];

                                        // Create links with preserved parameters
                                        foreach ($sortOptions as $sortValue => $sortLabel) {
                                            // Update sort parameter while preserving others
                                            $newParams = $params;
                                            $newParams['sort'] = $sortValue;

                                            // Generate URL with all parameters
                                            $url = $this->Url->build(['?' => $newParams]);

                                            // Determine if this option is currently active
                                            $isActive = ($params['sort'] ?? 'popularity') == $sortValue;
                                            $activeClass = $isActive ? 'active' : '';

                                            echo "<li><a class=\"dropdown-item {$activeClass}\" href=\"{$url}\">{$sortLabel}</a></li>";
                                        }
                                        ?>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <?php
                            // Get current URL parameters to preserve filters
                            $currentParams = $this->request->getQueryParams();

                            // Create URL for "ALL" category while preserving filters
                            $allUrl = $this->Url->build([
                                'controller' => 'Home',
                                'action' => 'productList',
                                '?' => $currentParams
                            ]);
                            ?>
                            <div class="d-flex flex-wrap gap-2 mb-0 mb-lg-4  catogory-md">
                                <a href="<?= $allUrl ?>" class="btn <?= $categoryUrlKey == null ? 'btn-success' : 'btn-secondary' ?>">All</a>
                                <?php foreach($categories as $category):
                                    // Create URL for each category while preserving filters
                                    $categoryUrl = $this->Url->build([
                                        'controller' => 'Home',
                                        'action' => 'productList',
                                        $category->url_key,
                                        '?' => $currentParams
                                    ]);
                                    $isActive = isset($selectedCategory['selectedCategory']) && $selectedCategory['selectedCategory']->url_key == $category->url_key;
                                ?>
                                    <a href="<?= $categoryUrl ?>" class="btn <?= $isActive ? 'btn-success' : 'btn-secondary' ?>"><?= $category->name ?></a>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Products Row 1 Main Product Listing Code -->
                        <div class="row mb-4" id="productListItems">
                            <?php if(isset($productList['data']) && !empty($productList['data'])): ?>
                            <?php foreach($productList['data'] as $product): ?>
                            
                                <!-- Product 1 -->
                                <div class="col-md-6 col-lg-4 mb-4 " >
                                    <div class="product-card clickable-product-card" data-product-id="<?= $product->id ?>" style="cursor: pointer;">                                      
                                        <div class="position-relative substract">
                                            <div class="wishlist" data-product-id="<?= $product->id ?>">
                                                <?php
                                                // Check if wishlist property exists and is true
                                                $isInWishlist = isset($product->whishlist) && $product->whishlist === true;
                                                ?>
                                                <?php if ($isInWishlist): ?>
                                                    <span class="wishlist-icon remove-wishlist-btn heart-filled" title="Remove from wishlist">❤️</span>
                                                <?php else: ?>
                                                    <span class="wishlist-icon add-wishlist-btn heart-empty" title="Add to wishlist"><img src="../../img/ozone/heart.png" class="img-fluid wishlist-img"/></span>
                                                <?php endif; ?>
                                            
                                            </div>
                                            <img src="../../img/ozone/ASGH-product.png" alt="Air Conditioner"
                                                class="img-fluid w-100 product-img">

                                                    <div class=" position-absolute">
                                                        <div class="d-flex justify-content-between align-items-center">

                                                    <?php if (isset($product['discount']) && $product['discount'] > 0): ?>
                                                                <div class="badge"><?= h($product['discount']) ?>% <?= __('Off') ?></div>
                                                    <?php endif; ?>
                                                    <div class="add-cart">
                                                        <span class="bor-top-rig"></span>
                                                        <span class="bor-bot-left"></span>
                                                        <div class="energy-rating">
                                                            <button class="add-to-cart-btn" data-product-id="<?= $product->id ?>" style="background: none; border: none; padding: 0; cursor: pointer;">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38"
                                                                    viewBox="0 0 38 38" fill="none">
                                                                    <path
                                                                        d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995"
                                                                        stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                    <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5"
                                                                        stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                    <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999" stroke="white"
                                                                        stroke-width="2" stroke-linecap="round" />
                                                                    <circle cx="8.19985" cy="33.4" r="3.6" stroke="white"
                                                                        stroke-width="2" />
                                                                    <circle cx="28.0001" cy="33.4" r="3.6" stroke="white"
                                                                        stroke-width="2" />
                                                                    <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white"
                                                                        stroke-width="2" stroke-linecap="round" />
                                                                    <path
                                                                        d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8"
                                                                        stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="pt-3">
                                           <h5 class="text-truncate-2-line"><?= h(mb_strimwidth(strip_tags($product->name ?? ''), 0, 50, '...')) ?></h5>


                                            <?php
                                                    $rating = floatval($product['avg_rating']);
                                                    $fullStars = floor($rating);
                                                    $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                                    $emptyStars = 5 - $fullStars - $halfStar;
                                                ?>
                                                <div class="rating mb-2">
                                                    <?php for ($i = 0; $i < $fullStars; $i++): ?>
                                                        <i class="fas fa-star"></i>
                                                    <?php endfor; ?>
                                                    <?php if ($halfStar): ?>
                                                        <i class="fas fa-star-half-alt"></i>
                                                    <?php endif; ?>
                                                    <?php for ($i = 0; $i < $emptyStars; $i++): ?>
                                                        <i class="far fa-star"></i>
                                                    <?php endfor; ?>
                                                    <span class="text-muted ms-2">(<?= number_format($rating, 1) ?>)</span>
                                                </div>
                                                <!-- <div class="price "><?= $this->Price->setPriceFormat($product->promotion_price) ?><span> incl. VAT</span></div> -->
                                                <div class="price">
                                                    <span class="text-decoration-line-through text-secondary d-block" style="font-weight: 500;">
                                                        <?= $this->Price->setPriceFormat($product->sales_price) ?>
                                                    </span>
                                                        <?= $this->Price->setPriceFormat($product->promotion_price) ?>
                                                    <span class="text-muted" style="font-size: 0.9rem;"> incl. VAT</span>
                                                </div>


                                                    <div class="describtion">
                                                        <p class=""><?= mb_strimwidth(strip_tags($product->description ?? ''), 0, 200, '...') ?></p>
                                                    </div>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Product end -->
                            
                            <?php endforeach; ?>
                            <?php else: ?>
                                <p style="text-align: center;"><?= __('No records found.') ?></p>
                            <?php endif; ?>
                        </div>
                        <div id="loader"><?= __('Loading more items...') ?></div>

                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
/* Wishlist Styles */
.wishlist {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    right: auto !important;
    z-index: 10;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.wishlist:hover {
    transform: scale(1.1);
}

.wishlist-icon {
    font-size: 24px;
    transition: opacity 0.2s ease, transform 0.2s ease;
    display: inline-block;
    line-height: 1;
}

.wishlist-icon:hover {
    opacity: 0.8;
    transform: scale(1.1);
}

.heart-filled {
    color:rgb(239, 109, 94);
}

.heart-empty {
    color: #bdc3c7;
}

/* Wishlist Message Styles - Now using common toast functionality from website.php */

/* Product card hover effects */
.product-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Clickable product card styles */
.clickable-product-card {
    position: relative;
}

.clickable-product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.clickable-product-card:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* Ensure cart and wishlist buttons don't interfere with card clicks */
.add-cart, .wishlist {
    position: relative;
    z-index: 10;
}

.add-to-cart-btn, .wishlist-icon {
    position: relative;
    z-index: 11;
}

/* Loading state for wishlist icons */
.wishlist-icon[style*="opacity: 0.5"] {
    pointer-events: none;
}

/* Responsive wishlist positioning */
@media (max-width: 768px) {
    .wishlist {
        top: 8px !important;
        left: 8px !important;
        right: auto !important;
    }

    .wishlist-icon {
        font-size: 20px;
    }




}

/* Cart Message Styles - Now using common toast functionality from website.php */

/* Add to cart button loading states */
.add-to-cart-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.add-to-cart-btn {
    transition: all 0.3s ease;
}

.add-to-cart-btn:hover:not(:disabled) {
    transform: scale(1.05);
}
</style>

<?php $this->start('add_js'); ?>
<script>
    const priceSlider = document.getElementById('priceSlider');
    const minInput = document.getElementById('minPrice');
    const maxInput = document.getElementById('maxPrice');

    // Get initial price range from URL parameters or use defaults
    const urlParams = new URLSearchParams(window.location.search);
    const initialMinPrice = parseInt(urlParams.get('min_price')) || 0;
    const initialMaxPrice = parseInt(urlParams.get('max_price')) || 99999;

    noUiSlider.create(priceSlider, {
        start: [initialMinPrice, initialMaxPrice],
        connect: true,
        range: {
            'min': 0,
            'max': 99999
        },
        step: 1,
        tooltips: true,
        format: {
            to: value => Math.round(value),
            from: value => parseInt(value)
        }
    });

    // Set initial values from the slider creation
    minInput.value = initialMinPrice;
    maxInput.value = initialMaxPrice;

    // Update input fields when slider changes
    priceSlider.noUiSlider.on('update', function (values, handle) {
        if (handle === 0) {
            minInput.value = values[0];
        } else {
            maxInput.value = values[1];
        }
    });

    // Apply price filter when slider stops being dragged
    priceSlider.noUiSlider.on('change', function (values) {
        updatePriceFilter(values[0], values[1]);
    });

    // Update slider when input fields change
    minInput.addEventListener('change', function () {
        priceSlider.noUiSlider.set([this.value, null]);
        updatePriceFilter(this.value, maxInput.value);
    });

    maxInput.addEventListener('change', function () {
        priceSlider.noUiSlider.set([null, this.value]);
        updatePriceFilter(minInput.value, this.value);
    });

    // Function to update URL and reload page with new price filter
    function updatePriceFilter(minPrice, maxPrice) {
        const params = new URLSearchParams(window.location.search);

        // Update price parameters
        params.set('min_price', Math.round(minPrice));
        params.set('max_price', Math.round(maxPrice));

        // Build the new URL
        const newUrl = window.location.pathname + '?' + params.toString();

        // Update URL without reloading the page
        window.history.pushState({}, '', newUrl);

        // Reload the page to apply filters
        location.reload();
    }
</script>

<script>
    let page = <?= $productList['current_page'] ?? 1 ?>;
    let isLoading = false;
    let hasMorePages = <?= isset($productList['next_page']) && $productList['next_page'] !== null ? 'true' : 'false' ?>;

    // Function to render a product card
    function renderProductCard(productData) {
        let promotionPrice = customNumberFormatWithCountry(productData.promotion_price);
        let description = productData.description ? productData.description.substring(0, 200) + '...' : '';
        let isInWishlist = productData.whishlist === true;
        let wishlistIcon = isInWishlist ?
            `<span class="wishlist-icon remove-wishlist-btn heart-filled" title="Remove from wishlist">❤️</span>` :
            `<span class="wishlist-icon add-wishlist-btn heart-empty" title="Add to wishlist"><img src="../../img/ozone/heart.png" class="img-fluid wishlist-img"/></span>`;
        let debugInfo = ``;

        return `
            <div class="col-md-6 col-lg-4 mb-4 ps-0 pe-0 pe-md-auto ps-md-auto" >
                <div class="product-card clickable-product-card" data-product-id="${productData.id}" style="cursor: pointer;">
                    <div class="position-relative substract">
                        <div class="wishlist" data-product-id="${productData.id}">
                            ${wishlistIcon}
                           
                        </div>
                        <img src="${productData.product_image}" alt="${productData.name}" class="img-fluid w-100">
                        <div class="position-absolute">
                            <div class="d-flex justify-content-between align-items-center">
                            ${productData.discount && productData.discount > 0 ? `<div class="badge">${productData.discount}% <?= __('Off') ?></div>` : ''}
                                <div class="add-cart">
                                    <span class="bor-top-rig"></span>
                                    <span class="bor-bot-left"></span>
                                    <div class="energy-rating">
                                        <button class="add-to-cart-btn" data-product-id="${productData.id}" style="background: none; border: none; padding: 0; cursor: pointer;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                                                <path d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                <circle cx="8.19985" cy="33.4" r="3.6" stroke="white" stroke-width="2" />
                                                <circle cx="28.0001" cy="33.4" r="3.6" stroke="white" stroke-width="2" />
                                                <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                <path d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8" stroke="white" stroke-width="2" stroke-linecap="round" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pt-3">
                        <h5 class="text-truncate-2-line">${productData.name}</h5>
                        <div class="rating mb-2">
                            ${(() => {
                                const rating = parseFloat(productData.avg_rating) || 0;
                                const fullStars = Math.floor(rating);
                                const halfStar = (rating - fullStars) >= 0.5 ? 1 : 0;
                                const emptyStars = 5 - fullStars - halfStar;
                                let starsHtml = '';
                                for (let i = 0; i < fullStars; i++) {
                                    starsHtml += '<i class="fas fa-star"></i>';
                                }
                                if (halfStar) {
                                    starsHtml += '<i class="fas fa-star-half-alt"></i>';
                                }
                                for (let i = 0; i < emptyStars; i++) {
                                    starsHtml += '<i class="far fa-star"></i>';
                                }
                                return starsHtml + `<span class="text-muted ms-2">(${rating.toFixed(1)})</span>`;
                            })()}
                        </div>
                        <div class="price">${promotionPrice}<span> incl. VAT</span></div>
                        <div class="describtion">
                            <p>${description}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }



    // Function to load more products via AJAX
    function loadMore() {
        if (isLoading || !hasMorePages) return;

        isLoading = true;
        $('#loader').show();

        // Get current filter values
        const categoryId = <?= json_encode($productList['params']['category'] ?? [1]) ?>;
        const sortBy = '<?= $productList['params']['sort'] ?? 'newest' ?>';
        const minPrice = <?= explode('--', $productList['params']['price'] ?? '0--99999')[0] ?>;
        const maxPrice = <?= explode('--', $productList['params']['price'] ?? '0--99999')[1] ?>;
        const rating = '<?= $productList['params']['rating'] ?? '' ?>';
        const brands = '<?= $productList['params']['brand'] ?? '' ?>';
        const attributes = '<?= $this->request->getQuery('attributes') ?? '' ?>';
        const nextPage = page + 1;

        // Make AJAX request to load more products
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Home', 'action' => 'ajaxLoadMoreProducts']) ?>',
            type: 'GET',
            data: {
                category_id: categoryId,
                sortBy: sortBy,
                min_price: minPrice,
                max_price: maxPrice,
                rating: rating,
                brands: brands,
                attributes: attributes,
                page: nextPage,
                limit: 3
            },
            success: function(response) {
                if (response.status === 'success' && response.data && response.data.length > 0) {
                    // Render each product
                    let html = '';
                    response.data.forEach(function(product) {
                        html += renderProductCard(product);
                    });

                    // Append new products to the list
                    $('#productListItems').append(html);

                    // Update page number and check if more pages are available
                    page = response.current_page;
                    let currentCount = parseInt($('#updateFilterdCount').text()) || 0;
                    $('#updateFilterdCount').text(currentCount + (response.filtered_count || 0));
                    hasMorePages = response.next_page !== null;
                    if(!hasMorePages){
                        $('#loader').text("<?= __('No more products to load') ?>");
                    }
                } else {
                    hasMorePages = false;
                    $('#loader').text("<?= __('No more products to load') ?>");
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading more products:', error);
                $('#loader').text("<?= __('Error loading products. Please try again.') ?>");
            },
            complete: function() {
                isLoading = false;
                if (hasMorePages) {
                    $('#loader').hide();
                }
            }
        });
    }


    // Initialize scroll event listener for lazy loading
    $(window).on('scroll', function() {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 300) {
            loadMore();
        }
    });

    // Hide loader initially if no more pages
    if (!hasMorePages) {
        $('#loader').hide();
    }

    // Wishlist functionality is now handled by website.php layout



    // function showToastMessage(message, type) {
    //     // Use the common toast functionality from website.php
    //     showToastMessage(message, type);
    // }



    // Add to Cart functionality
    $(document).on('click', '.add-to-cart-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const productId = $(this).data('product-id');
        const button = $(this);
        const originalContent = button.html();

        // Show loading state
        button.prop('disabled', true).html(`
            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                <circle cx="19" cy="19" r="15" stroke="white" stroke-width="2" fill="none" opacity="0.3"/>
                <path d="M19 4 L19 19 L30 19" stroke="white" stroke-width="2" stroke-linecap="round">
                    <animateTransform attributeName="transform" type="rotate" values="0 19 19;360 19 19" dur="1s" repeatCount="indefinite"/>
                </path>
            </svg>
        `);

        addToCart(productId)
            .then(response => {
                if (response.status === 'success') {
                    // Update cart count if provided
                    if (response.cartCount !== undefined && window.CartUpdater) {
                        window.CartUpdater.updateCartCount(response.cartCount);
                        console.log('Cart count updated to:', response.cartCount);
                    } else if (window.refreshCartCount) {
                        // Fallback: refresh cart count from server
                        window.refreshCartCount();
                        console.log('Refreshing cart count from server');
                    }

                    // Show success state
                    button.html(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                            <circle cx="19" cy="19" r="15" stroke="white" stroke-width="2" fill="green" opacity="0.8"/>
                            <path d="M12 19 L17 24 L26 15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    `);

                    // Show success message
                    showToastMessage(response.message || 'Product added to cart successfully!', 'success');

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.prop('disabled', false).html(originalContent);
                    }, 2000);
                } else {
                    button.prop('disabled', false).html(originalContent);
                    showToastMessage(response.message || 'Failed to add product to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                button.prop('disabled', false).html(originalContent);
                showToastMessage('Failed to add product to cart', 'error');
            });
    });

    // Helper function for add to cart
    function addToCart(productId) {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
                },
                url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToCart']) ?>/" + productId,
                type: 'POST',
                data: {quantity: 1},
                success: function (response) {
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    // // Show cart message function - using common toast functionality
    // function showCartMessage(message, type) {
    //     showToastMessage(message, type);
    // }

    // Product card click functionality - redirect to product details
    $(document).on('click', '.clickable-product-card', function(e) {
        // Don't redirect if user clicked on cart button, wishlist button, or their children
        if ($(e.target).closest('.add-to-cart-btn, .wishlist, .add-cart').length > 0) {
            return;
        }

        const productId = $(this).data('product-id');
        if (productId) {
            // Redirect to product details page (Home controller, product method)
            window.location.href = `<?= $this->Url->build(['controller' => 'Home', 'action' => 'product']) ?>/${productId}`;
        }
    });

</script>
<?php $this->end(); ?>
<script>
function updateBrandFilter() {
    // Get all checked brand checkboxes
    const checkboxes = document.querySelectorAll('.brand-filter:checked');
    const selectedBrands = Array.from(checkboxes).map(cb => cb.value);

    // Get current URL parameters
    const params = new URLSearchParams(window.location.search);

    // Update brands parameter
    if (selectedBrands.length > 0) {
        params.set('brands', selectedBrands.join(','));
    } else {
        params.delete('brands');
    }

    // Preserve other parameters like sort, category, price range, etc.
    const sortBy = params.get('sortBy');
    const minPrice = params.get('min_price');
    const maxPrice = params.get('max_price');
    const rating = params.get('rating');

    // Build the new URL
    const newUrl = window.location.pathname + '?' + params.toString();

    // Update URL without reloading the page
    window.history.pushState({}, '', newUrl);

    // Reload the page to apply filters
    // Comment this out if you want to implement AJAX filtering instead
    location.reload();
}

function updateAttributeFilter() {
    // Get all checked attribute checkboxes
    const checkboxes = document.querySelectorAll('.attribute-filter:checked');
    const selectedAttributes = Array.from(checkboxes).map(cb => cb.value);

    // Get current URL parameters
    const params = new URLSearchParams(window.location.search);

    // Update attributes parameter
    if (selectedAttributes.length > 0) {
        params.set('attributes', selectedAttributes.join(','));
    } else {
        params.delete('attributes');
    }

    // Build the new URL
    const newUrl = window.location.pathname + '?' + params.toString();

    // Update URL without reloading the page
    window.history.pushState({}, '', newUrl);

    // Reload the page to apply filters
    location.reload();
}

// Function to clear all filters
function clearAllFilters() {
    // Reset all brand checkboxes
    document.querySelectorAll('.brand-filter').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Reset all attribute checkboxes
    document.querySelectorAll('.attribute-filter').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Reset price slider if it exists
    if (priceSlider && priceSlider.noUiSlider) {
        priceSlider.noUiSlider.set([0, 99999]);
    }

    // Clear URL parameters and reload
    window.location.href = window.location.pathname;
}

// Add event listener to the "Clear all" link
document.addEventListener('DOMContentLoaded', function() {
    const clearAllLink = document.querySelector('.filter-title + a.text-success');
    if (clearAllLink) {
        clearAllLink.addEventListener('click', function(e) {
            e.preventDefault();
            clearAllFilters();
        });
    }
});
</script>

<script>
// function toggleFilterPanel() {
//   document.getElementById('filterPanel').classList.toggle('open');
// }
</script>
<script>
  function toggleFilterPanel() {
    const filterPanel = document.getElementById('filterPanel');
    const productContainer = document.getElementById('productListContainer');

    // Toggle the 'open' class on filter panel
    filterPanel.classList.toggle('open');

    // Toggle shifting of product list
    productContainer.classList.toggle('shifted');
  }

  function clearAllFilters() {
    // Optional: Your logic to clear filters
    const checkboxes = document.querySelectorAll('.brand-filter, .attribute-filter');
    checkboxes.forEach(cb => cb.checked = false);
    document.getElementById('minPrice').value = 0;
    document.getElementById('maxPrice').value = 99999;
  }
</script>
