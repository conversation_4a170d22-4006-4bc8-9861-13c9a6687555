<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * OfferCategory Entity
 *
 * @property int $id
 * @property int $offer_id
 * @property int $category_id
 * @property int $level
 * @property string|null $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Category $category
 * @property \App\Model\Entity\Offer $offer
 */
class OfferCategory extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'offer_id' => true,
        'category_id' => true,
        'product_id'=> true,
        'level' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'category' => true,
        'offer' => true,
    ];
}
