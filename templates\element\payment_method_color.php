<?php
/**
 * Payment Method Color Helper Element
 * Returns appropriate CSS class for payment method badges
 */

$method = $method ?? '';

switch (strtolower($method)) {
    case 'cod':
    case 'cash on delivery':
        echo 'warning';
        break;
    case 'credit card':
    case 'card':
        echo 'primary';
        break;
    case 'debit card':
        echo 'info';
        break;
    case 'bank transfer':
    case 'wire transfer':
        echo 'success';
        break;
    case 'paypal':
        echo 'info';
        break;
    case 'momo':
    case 'mobile money':
        echo 'success';
        break;
    case 'wallet':
    case 'digital wallet':
        echo 'primary';
        break;
    default:
        echo 'secondary';
        break;
}
?>
