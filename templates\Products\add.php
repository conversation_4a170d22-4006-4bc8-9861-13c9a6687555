<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Product $product
 * @var \Cake\Collection\CollectionInterface|string[] $brands
 * @var \Cake\Collection\CollectionInterface|string[] $suppliers
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link href="https://cdn.jsdelivr.net/npm/lightgallery@2.5.0/css/lightgallery-bundle.min.css" rel="stylesheet">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    .is-invalid-ckeditor {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }

    .is-invalid-select {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }

    .bootstrap-tagsinput {
        width: 100%;
    }

    #scanner-container video {
        width: 400px !important;
        height: auto !important;
        max-width: 640px;
        /* Adjust as needed */
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'index']) ?>"><?= __("Products") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Add") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #206331"><?= __("Add Product") ?></h6>
            <?php echo $this->Form->create($product, ['id' => 'add', 'novalidate' => true, 'type' => 'file']); ?>

            <div class="form-group row">
                <label for="reference_name" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Reference Title") ?><sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('reference_name', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label fw-bold"><?= __("Product Title") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="name_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Product Title (Arabic)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name_ar', ['class' => 'form-control', 'label' => false, 'dir' => 'rtl']); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="category" class="col-sm-2 col-form-label fw-bold"><?= __("Category") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('category_id', [
                        'type' => 'select',
                        'options' => $categories,
                        'empty' => __('Select Category'),
                        'class' => 'form-control form-select select2',
                        'label' => false,
                        'id' => 'category-id'
                    ]); ?>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label for="sub_category" class="col-sm-2 col-form-label fw-bold"><?= __("Sub Category") ?></label>
                <div class="col-sm-5 main-field">
                    <select name="sub_category_id" id="sub-category-id" class="form-control form-select select2" required>
                        <option value=""><?= __("Select Sub Category") ?></option>
                    </select>
                </div>
            </div> -->

            <?php
            $sizeOptions = [
                'Very Small' => __('Very Small'),
                'Small' => __('Small'),
                'Medium' => __('Medium'),
                'Large' => __('Large'),
                'Very Large' => __('Very Large')
            ];
            ?>
            <div class="form-group row">
                <label for="product_size" class="col-sm-2 col-form-label fw-bold"><?= __("Product Size") ?><sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('product_size', [
                        'type' => 'select',
                        'options' => $sizeOptions,
                        'empty' => __('Select Product Size'),
                        'class' => 'form-control form-select',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="product_weight" class="col-sm-2 col-form-label fw-bold"><?= __("Product Weight") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('product_weight', ['class' => 'form-control', 'label' => false]); ?>
                    <span>Product Weight in KG</span>
                </div>
            </div>

            <!-- Installation Charge Field -->
            <div class="form-group row">
                <label for="installation_charge" class="col-sm-2 col-form-label fw-bold"><?= __("Installation Charge") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('installation_charge', [
                        'class' => 'form-control',
                        'label' => false,
                        'type' => 'number',
                        'step' => '0.01',
                        'min' => '0',
                        'placeholder' => __('Enter Installation Charge')
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="brand_id" class="col-sm-2 col-form-label fw-bold"><?= __("Brand") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('brand_id', [
                        'options' => [],
                        'empty' => __('Select Brand'),
                        'class' => 'form-control form-select select2',
                        'label' => false,
                        'id' => 'brand-id'
                    ]); ?>
                    <?php if ($canAddBrand): ?>
                        <a href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'add']) ?>" target="_blank"><i
                                class="fas fa-plus"></i> <?= __('Add Brand') ?></a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Country Assignment Info (Read-only display) -->
            <?php
            $currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId');
            if ($currentCountryFilter && isset($selectedCountry)): ?>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
                <div class="col-sm-5">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?= __('This product will be assigned to: <strong>{0}</strong>', h($selectedCountry->name)) ?>
                        <br>
                        <small class="text-muted">
                            <?= __('To change country, use the country dropdown in the header.') ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php elseif (!$currentCountryFilter): ?>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Required") ?></label>
                <div class="col-sm-5">
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= __('Please select a country from the header dropdown before adding a product.') ?>
                        <br>
                        <small class="text-muted">
                            <?= __('Products must be assigned to a specific country.') ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <div class="form-group row">
                <label for="description" class="col-sm-2 col-form-label fw-bold"><?= __("Short Description") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('description', ['class' => 'form-control', 'id' => 'ckeditor', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="description_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Short Description (Arabic)") ?></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('description_ar', ['class' => 'form-control', 'id' => 'ckeditor_ar', 'label' => false, 'dir' => 'rtl']); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="features" class="col-sm-2 col-form-label fw-bold"><?= __("Features") ?></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('features', [
                        'class' => 'form-control',
                        'id' => 'features_ckeditor',
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="features_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Features (Arabic)") ?></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('features_ar', [
                        'class' => 'form-control',
                        'id' => 'features_ckeditor_ar',
                        'label' => false,
                        'dir' => 'rtl'
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="details" class="col-sm-2 col-form-label fw-bold"><?= __("Product Details") ?></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('details', ['class' => 'form-control', 'id' => 'details_ckeditor', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="details_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Product Details (Arabic)") ?></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('details_ar', ['class' => 'form-control', 'id' => 'details_ckeditor_ar', 'label' => false, 'dir' => 'rtl']); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="product_tags" class="col-sm-2 col-form-label fw-bold"><?= __("Product Tags") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('product_tags', [
                        'type' => 'text',
                        'class' => 'form-control producttags',
                        'id' => 'meta_keyword',
                        'placeholder' => __('Product Tags'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label for="promotion_start_date" class="col-sm-2 col-form-label fw-bold"><?= __("Start Date") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php //echo $this->Form->control('promotion_start_date', ['type' => 'date', 'empty' => true, 'class' => 'form-control', 'label' => false, 'data-date-format' => 'dd-mm-yyyy']);
                    ?>
                </div>
            </div> -->

            <div class="form-group row">
                <label for="product_image" class="col-sm-2 col-form-label fw-bold"><?= __("Product Images") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('product_image[]', ['type' => 'file', 'class' => 'form-control', 'label' => false, 'multiple' => 'multiple', 'id' => 'imageInput']); ?>
                    <small>Only <?= implode(', ', array_map(fn($type) => '.' . $type, $productImageType)) ?> files are allowed. Max size: <?php echo $productSize; ?> MB. Dimensions: <?= $productMinWidth ?> x <?= $productMinHeight ?> and <?= $productMaxWidth ?> x <?= $productMaxHeight ?>.</small>
                    <div id="previeContainer">
                        <ul id="imagePreviewContainer">

                        </ul>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="catalogue" class="col-sm-2 col-form-label fw-bold"><?= __("Catalogue") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('catalogue', ['type' => 'file', 'class' => 'form-control', 'label' => false, 'id' => 'catalogueInput']); ?>
                    <small>Only PDF files are allowed. Max size: 10 MB.</small>
                </div>
            </div>

            <div class="form-group row">
                <!-- <video id="barcode-scanner"></video> -->
                <label for="product_image" class="col-sm-2 col-form-label fw-bold"><?= __("Product Barcode") ?> </label>
                <div class="col-sm-5 main-field">
                    <input type="hidden" id="scanned-barcode" name="scanned_barcode">
                    <div id="scanned-product-barcode"></div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#barcodeModal">
                        Scan Barcode
                    </button>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label for="supplier_id" class="col-sm-2 col-form-label fw-bold"><?= __("Supplier Name") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php // echo $this->Form->control('supplier_id', ['options' => $suppliers, 'empty' => __('Select Supplier'), 'class' => 'form-control', 'label' => false]);
                    ?>
                </div>
            </div> -->

            <div class="form-group row">
                <label for="sku" class="col-sm-2 col-form-label fw-bold"><?= __("SKU") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('sku', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <div class="col-sm-2 fw-bold" style="color: #34395e; font-size: 12px;"><?= __("Widget Configuration") ?></div>
                <div class="col-sm-5 main-field">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="product_preference" id="featured"
                            value="Featured">
                        <label class="form-check-label" for="featured"><?= __("Featured") ?></label>
                    </div>
                    <!-- <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="product_preference" id="special-offers"
                            value="Deal" checked>
                        <label class="form-check-label" for="special-offers"><?= __("Deal of the day") ?></label>
                    </div> -->
                </div>
            </div>



            <!-- <h6 class="m-b-20" style="color: #206331"><?= __("Price Configuration") ?></h6>
            <div class="form-group row">
                <label for="purchase_price" class="col-sm-2 col-form-label fw-bold"><?= __("Purchase Price") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('purchase_price', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="product_price" class="col-sm-2 col-form-label fw-bold"><?= __("Reseller Price") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('product_price', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="sales_price" class="col-sm-2 col-form-label fw-bold"><?= __("Sales Price") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('sales_price', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="promotion_price" class="col-sm-2 col-form-label fw-bold"><?= __("Promotional Price") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('promotion_price', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div> -->

            <h6 class="m-b-20" style="color: #206331"><?= __("SEO Configuration") ?></h6>
            <div class="form-group row">
                <label for="meta_title" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Title") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_title', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="meta_keyword" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Keyword") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_keyword', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_keyword',
                        'placeholder' => __('Meta Keyword'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="meta_description" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Description") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_description', ['class' => 'form-control', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'status',
                        'options' => [
                            'A' => __('Active'),
                            'I' => __('Inactive')
                        ],
                        'empty' => __('Select Status'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="approval_status" class="col-sm-2 col-form-label fw-bold"><?= __("Approval Status") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('approval_status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'approval_status',
                        'options' => [
                            __('Pending') => __('Pending'),
                            __('Approved') => __('Approved'),
                            __('Rejected') => __('Rejected')
                        ],
                        'empty' => __('Select Approval Status'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label for="additional-showroom-price" class="col-sm-2 col-form-label fw-bold"><?= __("Additional Showroom Price?") ?></label>
                <div class="col-sm-5 main-field">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="showroom_based_price" id="yes" value="<?= __("Yes") ?>">
                        <label class="form-check-label" for="yes"><?= __("Yes") ?></label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="showroom_based_price" id="no" value="<?= __("No") ?>"
                            checked>
                        <label class="form-check-label" for="no"><?= __("No") ?></label>
                    </div>
                </div>
                <div id="showroomTable" class="table-responsive" style="display: none">
                    <div style="width: 58%">
                        <table class="table table-bordered mt-2" id="price-table">
                            <thead>
                                <tr>
                                    <th><?= __("ID") ?></th>
                                    <th><?= __("Showroom Name") ?></th>
                                    <th><?= __("Transportation Cost") ?></th>
                                    <th><?= __("Action") ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="row-id">1</td>
                                    <td>
                                        <?php echo $this->Form->control('showroom_id[]', ['options' => $showrooms, 'empty' => __('Select Showroom'), 'class' => 'form-control  form-select', 'label' => false]); ?>
                                    </td>
                                    <td>
                                        <input type="text" name="transportation_cost[]" class="form-control"
                                            placeholder="<?= __("Transportation Cost") ?>" />
                                    </td>
                                    <td>
                                        <a href="javascript:void(0);" class="add-row" data-bs-toggle="tooltip"
                                            title="<?= __("Add") ?>" aria-label="Add">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div> -->
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" class="btn btn-primary"><?= __("Save") ?></button>
                    <button type="reset" class="btn btn-primary"><?= __("Reset") ?></button>
                </div>
            </div>
        </div>
        </form>
    </div>
</div>
</div>

<!-- Bootstrap Modal -->
<div class="modal fade" id="barcodeModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="barcodeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Scan Barcode</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="scanner-container" style="width: 100%; height: 300px; position: relative;">
                    <video id="barcode-video" autoplay playsinline style="width: 100%;"></video>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="saveBarcode">Save</button>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/lightgallery@2.5.0/lightgallery.umd.js"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="https://unpkg.com/@zxing/library@latest"></script>
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.0/dist/JsBarcode.all.min.js"></script>

<script>
    $(document).ready(function() {
        let videoElement = document.getElementById("barcode-video");
        let scanner = null;

        $("#barcodeModal").on("shown.bs.modal", function() {
            scanner = new ZXing.BrowserBarcodeReader();
            scanner.decodeFromVideoDevice(null, videoElement, function(result, err) {
                if (result) {


                    let barcodeValue = result.text;
                    $("#scanned-product-barcode").html('<svg id="barcode-svg"></svg>');
                    JsBarcode("#barcode-svg", barcodeValue, {
                        format: "CODE128",
                        displayValue: false, // Hide text below barcode
                        lineColor: "#000",
                        width: 2,
                        height: 50
                    });

                    swal("Scanned!", "Barcode: " + barcodeValue, "success").then(() => {
                        $("#scanned-barcode").val(barcodeValue);
                        $("#barcodeModal").modal("hide"); // Close modal
                    });


                    scanner.reset(); // Stop scanning after detecting
                }
            });
        });

        $("#barcodeModal").on("hidden.bs.modal", function() {
            if (scanner) {
                scanner.reset(); // Stop scanner when modal is closed
            }
        });
    });



    $(document).ready(function() {
        $('.select2').select2({
            minimumResultsForSearch: 0
        });
    });
    let allFiles = [];
/*
    document.getElementById('imageInput').addEventListener('change', function(event) {

        var files = Array.from(event.target.files);

        if (files.length == 0) {
            allFiles = [];
            renderPreviews();
            return false;
        }

        var validFiles = [];
        var invalidFiles = [];

        let processedFiles = 0;
        let totalFiles = files.length;

        files.forEach(function(file, index) {

            var fileExtension = file.name.split('.').pop().toLowerCase();
            var allowedExtensions = ['jpg', 'jpeg', 'png'];

            if (allowedExtensions.includes(fileExtension)) {
                var img = new Image();
                img.src = URL.createObjectURL(file);

                img.onload = function() {
                    var width = img.naturalWidth;
                    var height = img.naturalHeight;
                    var fileSize = file.size / 1024 / 1024;

                    if (fileSize <= <?= $productSize ?> && width >= <?= $productMinWidth ?> && width <= <?= $productMaxWidth ?> && height >= <?= $productMinHeight ?> && height <= <?= $productMaxHeight ?>) {
                        validFiles.push(file);
                    } else {
                        invalidFiles.push({
                            file: file.name,
                            reason: '<?= __('Image dimensions should be between ' . $productMinWidth . 'x' . $productMinHeight . ' and ' . $productMaxWidth . 'x' . $productMaxHeight) ?>'
                        });
                    }
                    processedFiles++;
                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }

                };

                img.onerror = function() {
                    invalidFiles.push({
                        file: file,
                        reason: '<?= __('Unable to load image') ?>'
                    });
                    processedFiles++;
                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }
                };
            } else {
                invalidFiles.push({
                    file: file.name,
                    reason: '<?= __('Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.') ?>'
                });
                processedFiles++;
                if (processedFiles === totalFiles) {
                    finalizeFileProcessing(validFiles, invalidFiles);
                }
            }
        });

    });
*/
    function finalizeFileProcessing(validFiles, invalidFiles) {

        var html = "<ul>";
        for (var i = 0; i < invalidFiles.length; i++) {
            html += `<li>${invalidFiles[i].file} - ${invalidFiles[i].reason}</li>`;
        }
        html += '</ul>';

        const wrapper = document.createElement('div');
        wrapper.innerHTML = html;

        if (invalidFiles.length > 0) {
            swal({
                title: "<?= __("Invalid Files") ?>",
                content: wrapper,
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: "true"
            });
        }

        var dataTransfer = new DataTransfer();

        validFiles.forEach(function(file) {
            dataTransfer.items.add(file);
        });

        document.getElementById('imageInput').files = dataTransfer.files;

        let newFiles = validFiles;
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();

    }

    function updateFileInput(validFiles) {

        var dataTransfer = new DataTransfer();

        validFiles.forEach(function(file) {
            dataTransfer.items.add(file);
        });

        document.getElementById('imageInput').files = dataTransfer.files;

        let newFiles = validFiles;
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();
    }

    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        let filesProcessed = 0;

        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);

            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            if (file.url) {
                li.innerHTML = `
            <a href="${file.url}">
                <img src="${file.url}" alt="Image Preview" class="preview-img" />
            </a>
            <span class="image-name" title="${fileName}">${shortName}</span>
            <button type="button" class="delete-img-btn" data-index="${index}">
                <i class="fas fa-times"></i>
            </button>
            `;
                previewContainer.appendChild(li);
                filesProcessed++;
                checkIfAllFilesProcessed(filesProcessed, allFiles.length);
            } else {
                let reader = new FileReader();
                reader.onload = function(e) {
                    let base64Url = e.target.result;

                    li.innerHTML = `
            <a href="${base64Url}">
                <img src="${base64Url}" alt="Image Preview" class="preview-img" />
            </a>
            <span class="image-name" title="${fileName}">${shortName}</span>
            <button type="button" class="delete-img-btn" data-index="${index}">
                <i class="fas fa-times"></i>
            </button>
            `;
                    previewContainer.appendChild(li);
                    filesProcessed++;
                    checkIfAllFilesProcessed(filesProcessed, allFiles.length);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    function checkIfAllFilesProcessed(filesProcessed, totalFiles) {
        if (filesProcessed === totalFiles) {
            lightGallery(document.getElementById('imagePreviewContainer'), {
                selector: 'a',
                thumbnail: true,
                zoom: true
            });
        }
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function(e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            allFiles.splice(index, 1);

            //Remove file from input
            var dataTransfer = new DataTransfer();

            allFiles.forEach(function(file) {
                dataTransfer.items.add(file); // Add valid files to the DataTransfer object
            });

            document.getElementById('imageInput').files = dataTransfer.files;

            renderPreviews();
        }
    });

    $(function() {
        CKEDITOR.replace("ckeditor");
        CKEDITOR.replace("ckeditor_ar");
        CKEDITOR.replace("features_ckeditor");
        CKEDITOR.replace("features_ckeditor_ar");
        CKEDITOR.replace("details_ckeditor");
        CKEDITOR.replace("details_ckeditor_ar");
        CKEDITOR.config.height = 300;

        for (instance in CKEDITOR.instances) {
            CKEDITOR.instances[instance].on('blur', function() {
                CKEDITOR.instances[this.name].updateElement();
            });
        }
    });

    // Configure CKEditor for features fields
    CKEDITOR.replace('features_ckeditor', {
        allowedContent: true,
        extraAllowedContent: 'div(*)[*]{*};*(*);'
    });

    CKEDITOR.replace('features_ckeditor_ar', {
        allowedContent: true,
        extraAllowedContent: 'div(*)[*]{*};*(*);'
    });

    CKEDITOR.replace('details_ckeditor', {
        allowedContent: true,
        extraAllowedContent: 'div(*)[*]{*};*(*);'
    });

    CKEDITOR.replace('details_ckeditor_ar', {
        allowedContent: true,
        extraAllowedContent: 'div(*)[*]{*};*(*);'
    });


    $(document).ready(function() {
        function updateRowActions() {
            $('table tbody tr').find('.remove-row').remove();
            $('table tbody tr').find('.add-row').remove();

            var rowCount = $('table tbody tr').length;
            $('table tbody tr').each(function(index, row) {
                $(row).find('.row-id').text(index + 1);
                if (rowCount > 1 && index < rowCount - 1) {
                    $(row).find('td:last').html('<a href="javascript:void(0);" class="remove-row" data-bs-toggle="tooltip" title="<?= __("Remove") ?>" aria-label="Remove"><i class="fas fa-minus"></i></a>');
                }
            });

            if (rowCount > 1) {
                $('table tbody tr:last td:last').html(
                    '<a href="javascript:void(0);" class="remove-row" data-bs-toggle="tooltip" title="<?= __("Remove") ?>" aria-label="Remove"><i class="fas fa-minus"></i></a> ' +
                    '<a href="javascript:void(0);" class="add-row" data-bs-toggle="tooltip" title="<?= __("Add") ?>" aria-label="Add"><i class="fas fa-plus"></i></a>'
                );
            } else {
                $('table tbody tr:last td:last').html(
                    '<a href="javascript:void(0);" class="add-row" data-bs-toggle="tooltip" title="<?= __("Add") ?>" aria-label="Add"><i class="fas fa-plus"></i></a>'
                );
            }
            updateShowroomOptions();
        }

        function updateShowroomOptions() {
            var selectedShowrooms = [];
            $('select[name="showroom_id[]"]').each(function() {
                var selected = $(this).val();
                if (selected) {
                    selectedShowrooms.push(selected);
                }
            });

            $('select[name="showroom_id[]"]').each(function() {
                var $select = $(this);
                $select.find('option').each(function() {
                    var $option = $(this);
                    if (selectedShowrooms.includes($option.val()) && $option.val() !== $select.val()) {
                        $option.prop('disabled', true); // Disable already selected options
                    } else {
                        $option.prop('disabled', false); // Enable available options
                    }
                });
            });
        }

        $(document).on('change', 'select[name="showroom_id[]"]', function() {
            updateShowroomOptions();
        });

        $(document).on('click', '.add-row', function() {
            var newRow = $('table tbody tr:first').clone();
            newRow.find('input').val('');
            newRow.find('select').val('');
            newRow.appendTo('table tbody');
            updateRowActions();
        });

        $(document).on('click', '.remove-row', function() {
            $(this).closest('tr').remove();
            updateRowActions();
        });

        updateRowActions();
    });

    // document.addEventListener("DOMContentLoaded", function () {
    // const yesRadio = document.getElementById("yes");
    // const noRadio = document.getElementById("no");
    // const tableContainer = document.getElementById("showroomTable");

    // function toggleTable() {
    // if (yesRadio.checked) {
    // tableContainer.style.display = "block";
    // } else {
    // tableContainer.style.display = "none";
    // }
    // }

    // yesRadio.addEventListener("change", toggleTable);
    // noRadio.addEventListener("change", toggleTable);

    // // Initial check
    // toggleTable();
    // });

    $(document).ready(function() {
        $('.producttags').tagsinput({
            confirmKeys: [44]
        });

        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });
    });

    // $(document).ready(function() {
    //     var selectedCategoryId = <?php // echo json_encode($selectedCategoryId); ?>;
    //     var selectedSubCategoryId = <?php //echo json_encode($selectedSubCategoryId); ?>;

    //     if (selectedCategoryId) {
    //         loadSubCategories(selectedCategoryId, selectedSubCategoryId);
    //     }

    //     $('#category-id').on('change', function() {
    //         var categoryId = $(this).val();
    //         if (categoryId) {
    //             loadSubCategories(categoryId);
    //         }
    //     });
    // });
/*
    function loadSubCategories(categoryId, selectedSubCategoryId = null) {
        $.ajax({
            url: '<?php echo $this->Url->build('/'); ?>categories/subcategories/' + categoryId,
            type: 'GET',
            success: function(response) {

                var subCategoryDropdown = $('#sub-category-id');
                subCategoryDropdown.empty().append('<option value=""><?= __("Select Sub Category") ?></option>');

                if (response.status === 'success') {
                    $.each(response.data, function(name, id) {
                        var option = $('<option>', {
                            value: id,
                            text: name
                        });
                        if (selectedSubCategoryId && id == selectedSubCategoryId) {
                            option.attr('selected', 'selected');
                        }
                        subCategoryDropdown.append(option);
                    });
                } else {
                    console.error('<?= __("Failed to fetch subcategories") ?>');
                }
            }
        });
    }
*/
    $(document).ready(function() {

        $.validator.addMethod("requireImageOrAllFiles", function(value, element) {
            var productImageFiles = $('input[name="product_image[]"]')[0].files;
            return productImageFiles.length > 0 || allFiles.length > 0;
        }, "Please upload a product image.");

        $("#add").validate({
            ignore: "",
            rules: {
                category_id: {
                    required: true
                },
                sub_category_id: {
                    required: false
                },
                brand_id: {
                    required: true
                },
                product_size: {
                    required: true
                },
                product_weight: {
                    required: true,
                    number: true,
                    min: 0
                },
                installation_charge: {
                    required: false,
                    number: true,
                    min: 0
                },
                name: {
                    required: true,
                    maxlength: 100,
                    // pattern: /^[a-zA-Z0-9\s]+$/
                },
                description: {
                    required: true
                },
                product_tags: {
                    required: false,
                    maxlength: 500
                },
                // promotion_start_date: {
                // required: true,
                // date: true
                // },
                "product_image[]": {
                    requireImageOrAllFiles: true,
                    accept: "jpg,jpeg,png",
                    filesize: 10485760 // 10 MB
                },
                catalogue: {
                    required: false,
                    accept: "pdf",
                    filesize: 10485760 // 10 MB
                },
                reference_name: {
                    required: true,
                    maxlength: 50,
                    // alphanumeric: true
                },
                // supplier_id: {
                // required: true
                // },
                sku: {
                    required: true,
                    maxlength: 50,
                    // pattern: /^[a-zA-Z0-9]+$/
                },
                purchase_price: {
                    number: true,
                    min: 0
                },
                product_price: {
                    required: true,
                    number: true,
                    min: 0
                },
                sales_price: {
                    required: true,
                    number: true,
                    min: 0
                },
                promotion_price: {
                    required: true,
                    number: true,
                    min: 0
                },
                status: {
                    required: true
                },
                approval_status: {
                    required: true
                }
            },
            messages: {
                category_id: "<?= __("Please select a category") ?>",
                // sub_category_id: "<?= __("Please select a sub category") ?>",
                brand_id: {
                    required: "<?= __("Please enter a brand") ?>",
                    maxlength: "<?= __("Brand cannot exceed 100 characters") ?>"
                },
                product_size: {
                    required: "<?= __("Please enter the product size") ?>",
                },
                product_weight: {
                    required: "<?= __("Please enter the product weight") ?>",
                    number: "<?= __("Please enter a valid weight") ?>",
                    min: "<?= __("Weight cannot be negative") ?>"
                },
                installation_charge: {
                    number: "<?= __('Please enter a valid Installation Charge') ?>",
                    min: "<?= __('Installation Charge cannot be negative') ?>"
                },
                name: {
                    required: "<?= __("Please enter a product title") ?>",
                    maxlength: "<?= __("Product title cannot exceed 100 characters") ?>",
                    pattern: "<?= __("Product title can only contain letters, numbers, and spaces.") ?>"
                },
                description: {
                    required: "<?= __("Please enter a description") ?>"
                },
                product_tags: {
                    maxlength: "<?= __("Product tags cannot exceed 500 characters") ?>"
                },
                // promotion_start_date: {
                // required: "<?= __("Please enter a start date") ?>",
                // date: "<?= __("Please enter a valid date") ?>"
                // },
                "product_image[]": {
                    requireImageOrAllFiles: "Please upload a product image.",
                    accept: "Only JPG, JPEG, and PNG formats are allowed.",
                    filesize: "File size must be 10 MB or less."
                },
                catalogue: {
                    accept: "Only PDF format is allowed for catalogue.",
                    filesize: "Catalogue file size must be 10 MB or less."
                },
                reference_name: {
                    required: "<?= __("Please enter a supplier reference title") ?>",
                    maxlength: "<?= __("Supplier reference ID cannot exceed 50 characters") ?>",
                    // alphanumeric: "<?= __("Supplier reference ID can only contain letters and numbers") ?>"
                },
                // supplier_id: {
                // required: "<?= __("Please enter a supplier name") ?>"
                // },
                sku: {
                    required: "<?= __("Please enter a SKU ID") ?>",
                    maxlength: "<?= __("SKU ID cannot exceed 50 characters") ?>",
                    // pattern: "<?= __("SKU ID can only contain letters and numbers.") ?>"
                },
                purchase_price: {
                    number: "<?= __("Please enter a valid purchase price") ?>",
                    min: "<?= __("Purchase price cannot be negative") ?>"
                },
                product_price: {
                    required: "<?= __("Please enter a reselling price") ?>",
                    number: "<?= __("Please enter a valid reselling price") ?>",
                    min: "<?= __("Reselling price cannot be negative") ?>"
                },
                sales_price: {
                    required: "<?= __("Please enter a sales price") ?>",
                    number: "<?= __("Please enter a valid sales price") ?>",
                    min: "<?= __("Sales price cannot be negative") ?>"
                },
                promotion_price: {
                    required: "<?= __("Please enter a promotional price") ?>",
                    number: "<?= __("Please enter a valid promotional price") ?>",
                    min: "<?= __("Promotional price cannot be negative") ?>"
                },
                status: "<?= __("Please select the product status") ?>",
                approval_status: "<?= __("Please select the product approval status") ?>"
            },
            submitHandler: function(form) {
                $('button[type="submit"]').attr('disabled', 'disabled');

                let showroomBasedPrice = $("input[name='showroom_based_price']:checked").val();
                let valid = true; // Flag to check if the form is valid

                if (showroomBasedPrice === 'Yes') {
                    $('#price-table tbody tr').each(function() {
                        let showroomField = $(this).find('select[name="showroom_id[]"]');
                        let transportationField = $(this).find('input[name="transportation_cost[]"]');

                        let showroomSelected = showroomField.val() !== '';
                        let transportationFilled = transportationField.val() !== '';

                        $(this).find('.error').remove();

                        if (!showroomSelected) {
                            showroomField.after('<label class="error"><?= __("Please select a showroom.") ?></label>');
                            valid = false;
                        }

                        if (!transportationFilled) {
                            transportationField.after('<label class="error"><?= __("Please enter transportation cost.") ?></label>');
                            valid = false;
                        }
                    });

                    if (!valid) {
                        $('button[type="submit"]').removeAttr('disabled');
                        return false;
                    }
                }

                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].updateElement();
                }

                const imagefiles = allFiles;

                if (imagefiles.length > 0) {
                    const dataTransfer = new DataTransfer();
                    imagefiles.forEach(file => {
                        dataTransfer.items.add(file);
                    });
                    document.getElementById('imageInput').files = dataTransfer.files;
                }

                form.submit();
            },
            errorPlacement: function(error, element) {
                error.appendTo(element.closest(".main-field"));
            },
            highlight: function(element) {
                $(element).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                $(element).closest('.form-group').find('.cke').addClass('is-invalid-ckeditor');
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element) {
                $(element).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                $(element).closest('.form-group').find('.cke').removeClass('is-invalid-ckeditor');
                $(element).removeClass('is-invalid');
            }
        });

        $.validator.addMethod("pattern", function(value, element, param) {
            return this.optional(element) || new RegExp(param).test(value);
        }, "<?= __("Invalid format.") ?>");

        $.validator.addMethod('accept', function(value, element, param) {
            var allowedExtensions = param.split(',');
            var files = element.files;

            if (files.length === 0) return true;

            for (var i = 0; i < files.length; i++) {
                var fileExtension = files[i].name.split('.').pop().toLowerCase();
                if ($.inArray(fileExtension, allowedExtensions) === -1) {
                    return false;
                }
            }

            return true;
        }, '<?= __("Please select a valid file type.") ?>');

        $.validator.addMethod('filesize', function(value, element, param) {
            var files = element.files;

            if (files.length === 0) return true;

            for (var i = 0; i < files.length; i++) {
                if (files[i].size > param) {
                    return false;
                }
            }

            return true;
        }, '<?= __("File size must be less than {0} bytes") ?>');

        $.validator.addMethod('alphanumeric', function(value, element) {
            return this.optional(element) || /^[a-z0-9]+$/i.test(value);
        }, '<?= __("Only letters and numbers are allowed") ?>');

        $.validator.addMethod('minDate', function(value, element) {
            var today = new Date();
            today.setHours(0, 0, 0, 0);
            var inputDate = new Date(value);
            return this.optional(element) || inputDate >= today;
        }, '<?= __("Start date cannot be in the past") ?>');

        $.validator.addMethod('slug', function(value, element) {
            return this.optional(element) || /^[a-z0-9]+(?:-[a-z0-9]+)*$/i.test(value);
        }, '<?= __("URL key can only contain lowercase letters, numbers, hyphens, and underscores, and must start and end with a letter or number") ?>');
    });

    $(document).ready(function() {

        // Check country selection before form submission
        <?php $currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId'); ?>
        <?php if (!$currentCountryFilter): ?>
        // If no country is selected, disable form submission
        $('#product-form').on('submit', function(e) {
            e.preventDefault();
            swal({
                title: '<?= __("Country Required") ?>',
                text: '<?= __("Please select a country from the header dropdown before adding a product. Products must be assigned to a specific country.") ?>',
                icon: 'warning',
                button: '<?= __("OK") ?>'
            });
            return false;
        });

        // Also disable the submit button
        $('button[type="submit"], input[type="submit"]').prop('disabled', true).addClass('btn-secondary').removeClass('btn-primary');
        <?php endif; ?>

        function fetchMappedBrands() {
            const categoryId = $('#category-id').val();
            const subCategoryId = $('#sub-category-id').val();

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Brands', 'action' => 'fetchMappedBrands']) ?>",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                data: {
                    category_id: categoryId,
                    sub_category_id: subCategoryId
                },
                dataType: 'json',
                success: function(response) {
                    var brandSelect = $('#brand-id');
                    brandSelect.empty();

                    brandSelect.append('<option value=""><?= __('Select Brand') ?></option>');

                    if (response.success && response.data.length > 0) {
                        $.each(response.data, function(index, value) {
                            brandSelect.append('<option value="' + value.id + '">' + value.name + '</option>');
                        });
                    } else {
                        brandSelect.append('<option value=""><?= __('No Brands Available') ?></option>');
                    }

                    brandSelect.trigger('optionsLoaded');
                },
                error: function() {
                    swal('Error!', 'Failed to load brands. Please try again later.', 'error');
                }
            });


        }
        $('#category-id, #sub-category-id').change(fetchMappedBrands);
    });
</script>
<?php $this->end(); ?>