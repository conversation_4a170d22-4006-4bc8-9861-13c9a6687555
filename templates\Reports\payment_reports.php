<?php
/**
 * Payment Reports Dashboard
 */
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= __('Payment Reports Dashboard') ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <?= $this->Html->link(__('Dashboard'), ['controller' => 'Dashboards', 'action' => 'adminDashboard']) ?>
                    </li>
                    <li class="breadcrumb-item active"><?= __('Payment Reports') ?></li>
                </ol>
            </nav>
        </div>
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-download me-2"></i><?= __('Export Reports') ?>
            </button>
            <div class="dropdown-menu">
                <?= $this->Html->link(
                    '<i class="fas fa-file-csv me-2"></i>' . __('Export Payment CSV'),
                    ['action' => 'exportPayments'] + $filters,
                    ['class' => 'dropdown-item', 'escape' => false, 'target' => '_blank']
                ) ?>
                <?= $this->Html->link(
                    '<i class="fas fa-list me-2"></i>' . __('Payment Details'),
                    ['action' => 'paymentDetails'],
                    ['class' => 'dropdown-item', 'escape' => false]
                ) ?>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i><?= __('Filter Payment Data') ?>
            </h6>
        </div>
        <div class="card-body">
            <?= $this->Form->create(null, [
                'type' => 'get',
                'class' => 'row g-3',
                'id' => 'paymentFiltersForm'
            ]) ?>
                <div class="col-md-3">
                    <label class="form-label"><?= __('Date From') ?></label>
                    <?= $this->Form->control('date_from', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_from'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <label class="form-label"><?= __('Date To') ?></label>
                    <?= $this->Form->control('date_to', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_to'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <label class="form-label"><?= __('Country') ?></label>
                    <?= $this->Form->control('country_id', [
                        'type' => 'select',
                        'options' => ['' => __('All Countries')] + array_column($countries, 'name', 'id'),
                        'class' => 'form-select',
                        'value' => $filters['country_id'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <label class="form-label"><?= __('Payment Status') ?></label>
                    <?= $this->Form->control('status', [
                        'type' => 'select',
                        'options' => [
                            '' => __('All Statuses'),
                            'Pending' => __('Pending'),
                            'Completed' => __('Completed'),
                            'Failed' => __('Failed'),
                            'Refunded' => __('Refunded')
                        ],
                        'class' => 'form-select',
                        'value' => $filters['status'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i><?= __('Apply Filters') ?>
                    </button>
                    <?= $this->Html->link(
                        '<i class="fas fa-undo me-2"></i>' . __('Reset'),
                        ['action' => 'paymentReports'],
                        ['class' => 'btn btn-secondary', 'escape' => false]
                    ) ?>
                </div>
            <?= $this->Form->end() ?>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?= __('Total Payments') ?> (<?= $summaryStats['report_period'] ?>)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                    <?= $currencyInfo['currency_symbol'] ?> <?= number_format($summaryStats['total_payments'], 2) ?>
                                <?php else: ?>
                                    <?= number_format($summaryStats['total_payments'], 2) ?>
                                    <small class="text-muted">(<?= $currencyInfo['currency_name'] ?>)</small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?= __('Total Transactions') ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($summaryStats['total_transactions']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-receipt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?= __('Average Payment') ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                    <?= $currencyInfo['currency_symbol'] ?> <?= number_format($summaryStats['avg_payment_amount'], 2) ?>
                                <?php else: ?>
                                    <?= number_format($summaryStats['avg_payment_amount'], 2) ?>
                                    <small class="text-muted">(<?= $currencyInfo['currency_name'] ?>)</small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?= __('Report Period') ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $summaryStats['report_period'] ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Analysis Tables -->
    <div class="row">
        <!-- Payments by Status -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i><?= __('Payments by Status') ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th><?= __('Status') ?></th>
                                    <th><?= __('Count') ?></th>
                                    <th><?= __('Amount') ?></th>
                                    <!-- <th><?= __('%') ?></th> -->
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($summaryStats['payments_by_status'] as $status): ?>
                                    <?php $percentage = $summaryStats['total_transactions'] > 0 ? ($status->count / $summaryStats['total_transactions']) * 100 : 0; ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-<?= $this->element('payment_status_color', ['status' => $status->status]) ?>">
                                            <?= h($status->status) ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($status->count) ?></td>
                                    <td>
                                        <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                            <?= $currencyInfo['currency_symbol'] ?> <?= number_format($status->amount, 2) ?>
                                        <?php else: ?>
                                            <?= number_format($status->amount, 2) ?>
                                        <?php endif; ?>
                                    </td>
                                    <!-- <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-<?= $this->element('payment_status_color', ['status' => $status->status]) ?>" 
                                                 style="width: <?= $percentage ?>%">
                                                <?= number_format($percentage, 1) ?>%
                                            </div>
                                        </div>
                                    </td> -->
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payments by Method -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-credit-card me-2"></i><?= __('Payments by Method') ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th><?= __('Method') ?></th>
                                    <th><?= __('Count') ?></th>
                                    <th><?= __('Amount') ?></th>
                                    <!-- <th><?= __('%') ?></th> -->
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($summaryStats['payments_by_method'] as $method): ?>
                                    <?php $percentage = $summaryStats['total_transactions'] > 0 ? ($method->count / $summaryStats['total_transactions']) * 100 : 0; ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-<?= $this->element('payment_method_color', ['method' => $method->method]) ?>">
                                            <?= h($method->method) ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($method->count) ?></td>
                                    <td>
                                        <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                            <?= $currencyInfo['currency_symbol'] ?> <?= number_format($method->amount, 2) ?>
                                        <?php else: ?>
                                            <?= number_format($method->amount, 2) ?>
                                        <?php endif; ?>
                                    </td>
                                    <!-- <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-<?= $this->element('payment_method_color', ['method' => $method->method]) ?>" 
                                                 style="width: <?= $percentage ?>%">
                                                <?= number_format($percentage, 1) ?>%
                                            </div>
                                        </div>
                                    </td> -->
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Countries by Payment Amount -->
    <div class="row">
        <div class="col-xl-8 col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-globe me-2"></i><?= __('Top Countries by Payment Amount') ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th><?= __('Country') ?></th>
                                    <th><?= __('Transactions') ?></th>
                                    <th><?= __('Total Amount') ?></th>
                                    <!-- <th><?= __('Share') ?></th> -->
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($summaryStats['top_countries'] as $country): ?>
                                    <?php $percentage = $summaryStats['total_payments'] > 0 ? ($country->total_amount / $summaryStats['total_payments']) * 100 : 0; ?>
                                <tr>
                                    <td><?= h($country->country_name) ?></td>
                                    <td><?= number_format($country->payment_count) ?></td>
                                    <td>
                                        <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                            <?= $currencyInfo['currency_symbol'] ?> <?= number_format($country->total_amount, 2) ?>
                                        <?php else: ?>
                                            <?= number_format($country->total_amount, 2) ?>
                                        <?php endif; ?>
                                    </td>
                                    <!-- <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" style="width: <?= $percentage ?>%">
                                                <?= number_format($percentage, 1) ?>%
                                            </div>
                                        </div>
                                    </td> -->
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="col-xl-4 col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock me-2"></i><?= __('Recent Payments') ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recentReports)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3 d-block"></i>
                            <?= __('No recent payments found') ?>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recentReports as $payment): ?>
                        <div class="d-flex align-items-center border-bottom py-3">
                            <div class="flex-shrink-0">
                                <div class="avatar avatar-sm">
                                    <span class="badge badge-<?= $this->element('payment_status_color', ['status' => $payment->payment_status]) ?>">
                                        <?= substr($payment->payment_status, 0, 1) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="small">
                                    <strong><?= h($payment->customer_name) ?></strong><br>
                                    <span class="text-muted"><?= h($payment->order_number) ?></span>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <div class="small text-end">
                                    <strong>
                                        <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                            <?= $currencyInfo['currency_symbol'] ?> <?= number_format($payment->amount, 2) ?>
                                        <?php else: ?>
                                            <?= number_format($payment->amount, 2) ?>
                                        <?php endif; ?>
                                    </strong><br>
                                    <small class="text-muted"><?= h($payment->payment_method) ?></small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <div class="text-center mt-3">
                            <?= $this->Html->link(
                                __('View All Payments') . ' <i class="fas fa-arrow-right ms-1"></i>',
                                ['action' => 'paymentDetails'],
                                ['class' => 'btn btn-primary btn-sm', 'escape' => false]
                            ) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df; }
.border-left-success { border-left: 0.25rem solid #1cc88a; }
.border-left-info { border-left: 0.25rem solid #36b9cc; }
.border-left-warning { border-left: 0.25rem solid #f6c23e; }

.badge-primary { background-color: #4e73df; }
.badge-success { background-color: #1cc88a; }
.badge-danger { background-color: #e74a3b; }
.badge-warning { background-color: #f6c23e; color: #000; }
.badge-info { background-color: #36b9cc; }
.badge-secondary { background-color: #858796; }

.progress-bar.bg-primary { background-color: #4e73df !important; }
.progress-bar.bg-success { background-color: #1cc88a !important; }
.progress-bar.bg-danger { background-color: #e74a3b !important; }
.progress-bar.bg-warning { background-color: #f6c23e !important; }
.progress-bar.bg-info { background-color: #36b9cc !important; }
.progress-bar.bg-secondary { background-color: #858796 !important; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const filterForm = document.getElementById('paymentFiltersForm');
    const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
    
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            filterForm.submit();
        });
    });
});
</script>
