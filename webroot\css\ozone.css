﻿@font-face {
    font-family: 'Sansation';
    src:
        /* url('../font/sansation/Sansation_Bold.ttf') format('woff2'),
        url('../font/sansation/Sansation_Light.ttf') format('woff'), */
        url('../font/sansation/Sansation_Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap');

body,
button {
  /*  font-family: 'Sansation', sans-serif; */
}

a,
a:hover {
    color: inherit;
}

p {
    color: #231F20;
    font-size: 18px;
    font-weight: 400;
}

.social-icons a i {
    color: #243F13;
}

.navbar-head-main {
    border-top: 20px solid #355C3F;
}

.navbar-brand {
    font-weight: bold;
    font-size: 24px;
    color: #76c083;
    font-family: 'Sansation', sans-serif;
}

.nav-link {
    color: #000;
    font-weight: 500;
    margin: 0 10px;
}

.nav-link:hover {
    color: #206331;
}

.navbar-light .navbar-nav .show>.nav-link,
.navbar-light .navbar-nav .nav-link.active {
    color: #206331;
    font-weight: 700;
}

.login-btn {
    background-color: #264e36;
    color: white;
    border-radius: 20px;
    padding: 8px 20px;
}

.icon {
    font-size: 20px;
    margin-left: 15px;
}

.banner {
    margin: 10px;
    text-align: center;
    position: relative;
    min-height: 890px;
}

.banner-section {
    position: absolute;
    right: 0%;
    left: 0%;
}

.better-air {
    color: #6FC284;
    font-size: 45px;
    font-weight: 700;
    margin-top: 105px;
}

.highlight {
    position: relative;
    display: inline-block;
}

.highlight::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -11px;
    width: 100%;
    height: 23px;
    background: url(../img/ozone/Brush.png) no-repeat bottom left;
    border-radius: 50px;
}

.testimonial-banner .highlight::after {
    content: "";
    position: absolute;
    left: 84px;
    bottom: -1px;
    width: 100%;
    height: 23px;
    background: url(../img/ozone/Brush.png) no-repeat bottom left;
    border-radius: 50px;
}

.description {
    color: #2E7D32;
    max-width: 600px;
    font-size: 18px;
    font-weight: 400;
}


.explore-btn {
    background-color: #1B1B1B;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    position: absolute;
    bottom: 0px;
    text-decoration: none;
}

.featured-content p {
    color: #30583A;
}


.explore-btn:hover {
    background-color: #1B1B1B;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    position: absolute;
    bottom: 0px;
    text-decoration: none;
}

.explore-btn i {
    margin-left: 10px;
    color: #459311;
}

.image-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.image-card img {
    width: 100%;
    border-radius: 15px;
}

.new-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    color: black;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: bold;
}

.tab-btn {
    border-radius: 25px;
    padding: 8px 20px;
    background: #2e2e2e;
    color: white;
    margin-right: 10px;
    border: none;
}

.tab-btn.active {
    background-color: #000;
}

.product-card {
    border-radius: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    background-color: #DBF5E2;
    position: relative;
    box-shadow: rgba(0, 0, 0, 0.45) 0px 25px 20px -20px;
    border: 1px solid #ff620000;
}

.product-card:hover {
    border-radius: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    background-color: #DBF5E2;
    position: relative;
    box-shadow: rgba(0, 0, 0, 0.45) 0px 25px 20px -20px;
    border: 1px solid #FF6200;
}

.discount-badge {
    background-color: orange;
    color: white;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 5px;
}

.cart-btn {
    background-color: #2e6e4c;
    color: white;
    border-radius: 15px;
    padding: 8px;
    position: absolute;
    bottom: 20px;
    right: 20px;
}

.load-more-btn {
    border-radius: 20px;
    background-color: #e6e6e6;
    padding: 10px 25px;
    border: none;
    margin-top: 20px;
}

.nav-pills .nav-link.active {
    background-color: #000;
}


















:root {
    --primary-color: #276100;
    --light-green: #e8f5e9;
    --dark-green: #30583A;
    --yellow: #ff6200;
}


.navbar-brand {
    font-weight: bold;
    color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--dark-green);
    border-color: var(--dark-green);
}

.btn-outline-primary {
    color: #231F20;
    background-color: #D9D9D9;
    border: 0px;
    border-radius: 42px;
    font-size: 22px;
    font-weight: 700;
}

.btn-outline-primary:hover {
    color: #231F20;
    background-color: #D9D9D9;
    border: 0px;
    border-radius: 42px;
}

.hero-section {
    background-color: var(--light-green);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: bold;
}

.hero-text h1 span {
    color: var(--primary-color);
}

.hero-image {
    position: relative;
}

/* .product-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    background-color: white;
} */
.product-card .badge {
    padding: 8px 10px;
    background-color: #FF6200;
    color: white;
    font-weight: normal;
    margin-left: 5%;
}
/* 
.product-card .substract {
    background-color: #fff;
    padding: 20px;
    min-height: 370px;
    border-radius: 20px;
    text-align: center;
} */
.product-card .substract {
    background-color: #fff;
    padding: 5px;
    min-height: 370px;
    border-radius: 20px;
    text-align: center;
}
.product-card .substract .product-img {
    position: relative;
    top: 17px;
    left: 0px;
}

/* .product-card .substract img {
    position: absolute;
    top: 100px;
    left: 0px;
} */
/* .product-card .substract img {
    position: absolute;
    top: 0;
    left: 0px;
    height: 100%;
    object-fit: contain;
} */
.product-card .substract img {
    position: relative;
    top: 0px;
    left: 0px;
    height: 100%;
    object-fit: contain;
}

.product-card .price .text-muted {
    font-size: 12px;
}

.product-card .price {
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--primary-color);
    display: flex
;
    justify-content: space-between;
    align-items: flex-end;
}

.product-card .rating {
    color: var(--yellow);
}

.filter-btn {
    border-radius: 20px;
    margin: 0 5px;
    padding: 5px 15px;
    background-color: #f5f5f5;
    color: #333;
    border: none;
}

.filter-btn.active {
    background-color: #333;
    color: white;
}

.section-title {
    position: relative;
    margin-bottom: 30px;
    font-size: 56px;
    font-weight: 700;
}

.section-title::after {
    /* content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color); */
}

.latest-offers {
    background: url(../img/ozone/latest-offers.png) no-repeat center;
    padding: 50px 0;

}

.products p {
    color: #30583A;
    font-size: 18px;
    font-weight: 400;
}

.products .btn-success {
    background: #231F20;
    border-radius: 33px;
    font-size: 18px;
    font-weight: 400;
    color: #FFFFFF;
}

.products .btn-secondary {
    background: #231F2026;
    color: #231F20;
    border-radius: 33px;
    font-size: 18px;
    font-weight: 400;
    border: 0;
}

.air-quality {
    background: url(../img/ozone/air-quality.png) no-repeat center bottom -36px;
}

/* .latest-offers .owl-carousel .product-card {
    background: #FFF8DE !important;
} */
.product-card:hover {
    background: #FFF8DE !important;
}

.product-card:hover .add-cart .bor-bot-left {
    border-bottom: 8px solid #FFF8DE;
    position: absolute;
    left: -13px;
    bottom: -8px;
    padding: 6px;
    border-bottom-right-radius: 119px;
    border-right: 8px solid #FFF8DE;
}

.product-card:hover .add-cart .bor-top-rig {
    border-bottom: 8px solid #FFF8DE;
    position: absolute;
    right: -7px;
    top: -12px;
    padding: 6px;
    border-bottom-right-radius: 119px;
    border-right: 8px solid #FFF8DE;
}

/* .product-card:hover .add-cart {
    background-color: #FFF8DE;
    position: relative;
    right: 0px;
    padding: 10px;
    border-top-left-radius: 20px;
} */

.brand-banner {
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/1_Landing%20Page.jpg-BnFcd53HfNq3sKr3DZxIcXysZNdtwB.jpeg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 80px 0;
    text-align: center;
}

.stats-card {
    text-align: left;
    margin-bottom: 20px;
}

.stats-card h2 {
    font-size: 2.5rem;
    font-weight: bold;
    color: Gray;
}

.partner-logo {
    /* height: 40px; */
    margin: 10px 20px;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.partner-logo:hover {
    opacity: 1;
}

.review-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.review-card img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown-toggle::after {
    background: url(../img/ozone/arrow-down-green.png) no-repeat center bottom;
    border: 0px !important;
    width: 20px;
    height: 10px;
    margin-bottom: -7px;
}

footer {
    background-color: #333;
    color: white;
    padding: 120px 0px 0px;
}

footer h5 {
    color: var(--primary-color);
    margin-bottom: 20px;
}

footer ul {
    list-style: none;
    padding-left: 0;
}

body.rtl footer ul {
    list-style: none;
    padding-right: 0;
}
body.rtl .noUi-handle:before {
   
    height: 70% !important;
    width: 60% !important;
    left: 11px !important;
    top: 1px !important;
}

footer ul li {
    margin-bottom: 20px;
}

footer ul li,
footer ul li a {
    color: #6D737A;
    text-decoration: none;
}

footer ul li a:hover {
    color: #30583A;
}

.social-icons a {
    color: white;
    margin-right: 15px;
    font-size: 1.2rem;
}

.product-card .add-cart {
    background-color: #DBF5E2;
    position: relative;
    right: 0px;
    padding: 10px;
    border-top-left-radius: 30px;
}

.product-card:hover .add-cart {
    background-color: #FFF8DE;
    position: relative;
    right: 0px;
    padding: 10px;
    border-top-left-radius: 30px;
}

.add-cart .bor-top-rig {
    border-bottom: 8px solid #DBF5E2;
    position: absolute;
    right: -7px;
    top: -12px;
    padding: 6px;
    border-bottom-right-radius: 119px;
    border-right: 8px solid #DBF5E2;
}

.add-cart .bor-bot-left {
    border-bottom: 8px solid #DBF5E2;
    position: absolute;
    left: -13px;
    bottom: -8px;
    padding: 6px;
    border-bottom-right-radius: 119px;
    border-right: 8px solid #DBF5E2;
}

.substract .position-absolute {

    bottom: 0px;
    left: 0px;
    width: 100%;
}

.product-card .price span {
    color: #6C6C6C;
    font-size: 14px;
    font-weight: 400;
}

.energy-rating {
    background-color: var(--dark-green);
    color: white;
    /* width: 40px;
    height: 40px; */
    display: flex;
    padding: 30px;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
    /* position: absolute;
    right: 10px;
    bottom: 10px; */
}

.product-card:hover .energy-rating {
    background-color: #FF6200;
    color: white;
    /* width: 40px;
    height: 40px; */
    display: flex;
    padding: 30px;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
    /* position: absolute;
    right: 10px;
    bottom: 10px; */
}


.energy-rating svg {}

.lead-banner {
    background: url(../img/ozone/lead-banner.png) no-repeat center left;
    height: 100%;
    max-width: 1690px;
    margin: auto
}

.lead-md-banner h2 {
    margin: 40px 0px;
    font-size: 56px;
}

.md-language {
    background: #355C3F;
}

.navbar-head-main .right-bord {
    border-right: 1px solid #D9D9D9;
    height: 37px;
}

.navbar-icons .dropdown .btn,
.navbar-icons .dropdown .btn:hover {
    color: #355C3F;
    font-weight: 600;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link,
.nav-tabs .nav-link.active {
    border-radius: 33px;
    font-size: 24px;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link {
    color: #231F20;
    background-color: #231F2026;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    color: #FFFFFF;
    background-color: #231F20;
}

.nav-tabs {
    border-bottom: 0px solid #dee2e6;
}

.owl-theme .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
}

#our-strength .owl-dots .owl-dot span {
    width: 10px;
    height: 10px;
    background: #53882F33;
}

#our-strength .owl-dots .owl-dot.active span {
    width: 10px;
    height: 10px;
    background: #53882F;
}

#our-strength .owl-dots {
    margin-top: 12px;
    /* space below carousel */
}

#our-strength .owl-dots .owl-dot {
    display: inline-block !important;
}

.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev {
    background: #231F20 !important;
    padding: 20px !important;
    border-radius: 25px;
    position: absolute;
}

.owl-carousel .owl-nav button.owl-next span,
.owl-carousel .owl-nav button.owl-prev span,
.owl-carousel button.owl-dot {
    display: none !important;
}

.owl-carousel .owl-nav button.owl-next {
    position: absolute;
    right: -90px
}

.noUi-tooltip {
    display: none !important;
}

.owl-carousel .owl-stage {
    margin-bottom: 30px;
}

.owl-carousel .owl-nav button.owl-prev {
    position: absolute;
    left: -90px
}

.owl-carousel .owl-nav button.owl-prev::before {
    content: '←';
    font-size: 22px;
    color: #B6E596;
    line-height: 12px;
    font-weight: 800;
}

.owl-carousel .owl-nav button.owl-next::before {
    content: '→';
    font-size: 24px;
    color: #B6E596;
    line-height: 12px;
}

.carousel-control-prev-icon {
    width: 50px;
    background-image: none;
    font-size: 30px;
    color: #000 !important;
    font-weight: 700;
    background: #fff;
    border-radius: 50px;
    height: 50px;
}

.carousel-control-next-icon {
    width: 50px;
    background-image: none;
    font-size: 30px;
    color: #000 !important;
    font-weight: 700;
    background: #fff;
    border-radius: 50px;
    height: 50px;
}

.customer-stories-content p,
.testimonial-banner .title {
    color: #30583A;
    font-weight: 600;
}

.owl-carousel .owl-nav button.owl-next:hover {
    color: #ffffff !important;
}

.product-general-banner {
    background: url(../img/ozone/product-page-banner.png) no-repeat center;
    position: relative;
    /* min-height: 640px; */
    max-width: 1692px;
    margin: auto;
}

/* .product-general-banner .blur-bg {
    background: #ffffff57;
     position: absolute;
    left: 0;
    top: 25%;
    backdrop-filter: blur(10px);
    padding: 60px;
    border-top-right-radius: 65px;
    border-bottom-right-radius: 65px;
    width: 50%;
} */
.show-results {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.show-results p {
    margin: 0px;
    color: #231F20;
    font-size: 14px;
    font-weight: 400;
}

.show-results p span {
    margin: 0px;
    color: #231F20;
    font-size: 14px;
    font-weight: 700;
}

.popularity-filter .btn-primary {
    color: #30583A;
    border-radius: 20px;
    background: #ffffff00;
    border: 1px solid #30583A;
    font-weight: 700;
    font-size: 14px;
}

.product-general-banner .blur-bg {
    background: #ffffff57;
    backdrop-filter: blur(10px);
    padding: 60px 60px 60px 100px;
    border-top-right-radius: 65px;
    border-bottom-right-radius: 65px;
    margin: auto 0px;
    width: 50%;
}

.product-general-banner .blur-bg p {
    color: #231F20;
    font-size: 22px;
    font-weight: 400;

}

.product-general-banner .btn-outline-dark {
    background: #231F20;
    color: #FFFFFF;
    border-radius: 25px;
}

.product-general-banner .btn-outline-dark span {
    /* content: '→'; */
    font-size: 24px;
    color: #B6E596;
    line-height: 12px;
}

.product-general-banner .carousel-control-prev {
    left: 60%;
}

.product-general-banner .arrow {
    position: relative;
}






.o-general-banner {
    background: url(../img/ozone/shop-banner.png) no-repeat center;
    position: relative;
    min-height: 640px;
    max-width: 1690px;
    margin: auto;
}

.o-general-banner .blur-bg {
    background: #ffffff57;
    position: absolute;
    left: 0;
    top: 25%;
    backdrop-filter: blur(10px);
    padding: 60px;
    border-top-right-radius: 65px;
    border-bottom-right-radius: 65px;
    width: 50%;
    max-width: 650px;
}

.o-general-banner .blur-bg p {
    color: #231F20;
    font-size: 22px;
    font-weight: 400;

}

.o-general-banner .blur-bg .carousel-inner .carousel-item {
    text-align: left;
}

.o-general-banner .btn-outline-dark {
    background: #231F20;
    color: #FFFFFF;
    border-radius: 25px;
}

.o-general-banner .btn-outline-dark span {
    /* content: '→'; */
    font-size: 24px;
    color: #B6E596;
    line-height: 12px;
}

.o-general-banner .carousel-control-prev {
    left: 75%;
}

.o-general-banner .arrow {
    position: relative;
}

.success-banner {
    background: url(../img/ozone/success-banner.png) no-repeat center;
}

.partner-carousel {
    background: #f3f3f3;
}

.client-logo .owl-nav {
    display: none;
}

.client-logo {
    background: url(../img/ozone/client-logo.png) no-repeat center;
}

.footer-banner {
    background: url(../img/ozone/Footer.png) no-repeat left;
    min-height: 541px;
}

.footer-banner .copy-rights p {
    color: #6F6C90;
}

.footer-content h5 {
    color: #243F13;
    font-size: 20px;
    font-weight: 700;
}

.footer-content .week {
    color: #243F13;
    font-weight: 600;
}

.footer-content .icons .fas {
    padding-bottom: 15px;
}

.testimonial-banner {
    /* background: url(../img/ozone/testimonial-banner.png) no-repeat center top -34px; */
}



.testimonials {
    background: url('../img/ozone/testimonial-1.png') no-repeat center;
    background-size: cover;
    min-height: 590px;
    display: flex;
    align-items: end;
    justify-content: center;
    padding: 2rem;
    border-radius: 18px;
}

.testimonials-two {
    background: url('../img/ozone/testimonial-2.png') no-repeat center;
    background-size: cover;
    min-height: 590px;
    display: flex;
    align-items: end;
    justify-content: center;
    padding: 2rem;
    border-radius: 18px;
}

.testimonials-three {
    background: url('../img/ozone/testimonial-3.png') no-repeat center;
    background-size: cover;
    min-height: 590px;
    display: flex;
    align-items: end;
    justify-content: center;
    padding: 2rem;
    border-radius: 18px;
}

.testimonial-card {
    background-color: #fff;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    padding: 2rem 1.5rem 1.5rem;
    max-width: 400px;
    width: 100%;
    position: relative;
    margin-bottom: 0px;
}

.profile-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #fff;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #ffc107;
}

.profile-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.stars {
    color: #50b25f;
}

.testimonial-banner .owl-carousel .owl-nav button.owl-next {
    background: url(../img/ozone/nxt.png) no-repeat center !important;
    content: '' !important;
    right: -36px;
    top: -170px;
    height: 80px;
    width: 94px;
}

.testimonial-banner .owl-carousel .owl-nav button.owl-next::before {
    content: '' !important;
}

.testimonial-banner .owl-carousel .owl-nav button.owl-prev {
    background: url(../img/ozone/pre.png) no-repeat center !important;
    content: '' !important;
    left: -36px;
    top: -170px;
    height: 80px;
    width: 94px;
}

.testimonial-banner .owl-carousel .owl-nav button.owl-prev::before {
    content: '' !important;
}

/* login */
.login {
    margin: 0;
    font-family: 'Inter', sans-serif;
    background-color: #D9D9D91A;
    backdrop-filter: blur(5px);
    color: white;
    position: relative;
}

.login .login-container {
    max-width: 838px;
    padding: 20px 0px 20px 20px;
    position: absolute;
    right: 0;
    background: #FFFFFFCC;
    border-top-left-radius: 70px;
    width: 100%;
    height: 100%;
    overflow: auto;
}

.login .login-container .substract {
    position: absolute;

}

.login .login-container .btn-close {
    background: none;
    color: #000;
    opacity: 3.5;
    position: fixed;
    top: 27px;

}

/* .login .login-container .substract {
    background: url(../img/ozone/Subtract-form-bg.png) no-repeat;
    width: 600px;
    height: 600px;
    position: absolute;
    right: 0px;
    top: 70%;
    max-height: 1200px;
} */
.login .login-container .substract {
    background: url(../img/ozone/Subtract-form-bg.png) no-repeat left;
    width: 100%;
    height: 100%;
    position: absolute;
    right: 0px;
    top: 50%;
    max-height: 1200px;
}

.verify-btn {
    color: #FFFFFF;
    background: #231F20;
    border-radius: 18px;
    font-size: 14px;
    font-weight: 700;
    padding: 10px;
    width: 170px;
    height: 57px;
    margin: 0px 10px;
    border: 0px;
}

.form-login label {
    color: #231F20;
    font-size: 16px;
    font-weight: 700;
}

.social-icons span {
    display: none;
    transition: opacity 0.3s ease;
    font-weight: 700;
    font-size: 16px;
}

.btn-green-border {
    border: 2px solid #00ff73;
    border-radius: 1rem;
    color: white;
    background: black;
}

.btn-green-border:hover {
    background-color: #111;
}

.login .social-icons button:hover {
    background-color: #D9ECD3;
}

.social-icons button:hover span {
    display: inline;

}

.login .login-container .form-login {
    max-width: 450px;
    margin: auto;
    position: relative;
    z-index: 9999;
}

.login .logo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32px;
    font-weight: 700;
    color: #231F20;
}

.login .logo h1 {
    font-size: 32px;
    font-weight: bold;
}

.login .logo .green {
    color: #38FF93;
}

.login .social-login {
    background-color: #DDF0D6;
    border-radius: 10px;
    padding: 12px;
    text-align: center;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.login .social-icons {
    display: flex;
    justify-content: space-around;
    gap: 15px;
    margin: 15px 0;
}

.login .social-icons button {
    background: #F6F6F6;
    border: none;
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    color: #162C08;
}

.login .divider {
    text-align: center;
    margin: 20px 0;
    color: gray;
    position: relative;
}

.login .divider::before,
.login .divider::after {
    content: '';
    height: 1px;
    background: #44444433;
    position: absolute;
    top: 50%;
    width: 40%;
}

.login .divider::before {
    left: 0;
}

.login .divider::after {
    right: 0;
}

.login .input-group {
    /* margin-bottom: 20px; */
}

.login .input-group label {
    display: block;
    font-size: 16px;
    margin-bottom: 6px;
    text-align: left;
    color: #231F20;
    font-weight: 700;
}

.login .input-group input {
    width: 100%;
    height: 57px;
    padding: 10px 12px;
    border-radius: 10px;
    border: 2px solid #ADADAD;
    outline: none;
    font-size: 14px;
    border-radius: 18px !important;
    color: black;
}

.login .input-group input:focus {
    border-color: #FF6200;
}

.login .email input {
    border-color: #ADADAD;
    border-radius: 18px;
}

.login .forgot-password {
    text-align: right;
    font-size: 13px;
    margin-top: 10px;
    color: #276100;
    cursor: pointer;
}

.login .btn-glow {
    width: 100%;
    padding: 7px 15px;
    font-size: 22px;
    font-weight: 600;
    border-radius: 30px;
    /* border: 2px solid #38FF93; */
    /* box-shadow: 0 0 10px #38FF93; */
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.login .btn-login {
    background-color: #1C1C1C;
    color: white;
}

.login .btn-register {
    background-color: white;
    color: black;
}

.login .btn-glow:hover {
    /* box-shadow: 0 0 20px #38FF93; */
}

.login .footer-text {
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #53882F;
}

.input-group.email {
    position: relative;
    width: 100%;
}

.input-group.email input {
    border-radius: 1rem;
    padding-right: 4rem;
    /* space for timer */
    padding-left: 1rem;
    width: 100%;
}

.timer-inside {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #96e88e;
    font-weight: 600;
    pointer-events: none;
}

.verify-btn {
    /* margin-left: 10px;
    border: 0px solid #00ff73;
    background: black;
    color: white;
    border-radius: 1rem;
    padding: 0.5rem 1rem; */
}

.verify-btn:hover {
    background: #111;
}

.error-text {
    font-size: 0.875rem;
    text-align: center;
    color: orange;
    border-bottom: 1px solid #231F20;
    padding-bottom: 35px;
}

.terms {
    color: #276100;
}

.terms-condition {
    color: #3A3A3C !important;
    font-size: 12px;
    font-weight: 600;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 0.5rem;
    /* Fully rounded */
    border: 2px solid #505050;
    background-color: white;
    appearance: none;
    -webkit-appearance: none;
    outline: none;
    cursor: pointer;
    position: relative;
}

.form-check-input:focus {
    border: none;
}

.form-check-input:checked {
    background-color: white;
    /* Keep white background */
    border: 2px solid black;
}

.form-check-input[type=checkbox] {
    border-radius: 5px !important;
}

.form-check-input:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0.4rem;
    height: 0.8rem;
    border: solid rgb(255, 255, 255);
    border-width: 0 2px 2px 0;
    transform: translate(-50%, -60%) rotate(45deg);
    display: none
}

.product-breadcrumb {
    background: linear-gradient(to right, #CEFFDA 0%, #00640000 40%, #00640000 40%)
}

.breadcrumb-item a {
    color: #231F20;
    text-decoration: none;
}

.form-check-label span {
    color: #6FC284;
}

.filter-section {
    border-bottom: 1px solid #B4B4B4;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.filter-title {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}

.star {
    color: #28a745;
    font-size: 18px;
}

.form-check-input:checked {
    background-color: #276100;
    border-color: #276100;
}

.price-inputs input {
    width: 45%;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 4px;
}

.Filters .title h5,
.filter-section .filter-title {
    font-size: 20px;
    font-weight: 700;
    color: #30583A;
    /* padding-bottom: 10px; */
}

.Filters .title a {
    color: #276100 !important;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
}

#brandFilter .form-check,
#tonnageFilter .form-check,
#ratingFilter .form-check,
#productsubtypeFilter .form-check,
#producttypeFilter .form-check {
    padding-bottom: 20px;
}

#discountSwitch {
    border-radius: 12px !important;
    padding: 12px;
    width: 60px;
}

/* Green slider track */
.custom-range-slider::-webkit-slider-thumb {
    background: white;
    border: 3px solid darkgreen;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    margin-top: -7px;
    cursor: pointer;
}

.star-green {
    color: #6FC284;
    margin: 5px;
}

.star-grey {
    color: #B9B9B9 !important;

}

.custom-range-slider::-webkit-slider-runnable-track {
    height: 6px;
    background: linear-gradient(to right, darkgreen 0%, darkgreen 40%, darkgreen 40%);
    border-radius: 10px;
}

.custom-range-slider::-moz-range-thumb {
    background: white;
    border: 3px solid darkgreen;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
}

.custom-range-slider::-moz-range-track {
    height: 6px;
    background: #ccc;
    border-radius: 10px;
}

.range-value .form-control {
    color: #1A064F !important;
    font-size: 13px;
    padding: 10px;
    margin: 3px;
}

.breadcrumb .active {
    font-weight: 700;
    font-size: 18px;
    color: #231F20;
}

.breadcrumb-item+.breadcrumb-item::before {
    float: left;
    padding-right: 0.5rem;
    color: #231F20;
    content: var(--bs-breadcrumb-divider, "→")
}

.wishlist .wishlist-img {

    position: relative !important;
    top: 0px !important;
    left: 0px !important;
    z-index: 99;
}

#productListItems {
    margin-left: 10px;
    margin-right: 10px;
}

.describtion {
    background: #FFFFFF;
    border-radius: 25px;
    padding: 15px 10px;
    margin-top: 10px;
    min-height: 144px;
    max-height: 144px;
    overflow: hidden;
}

/* .describtion p {
    font-size: 12px;
    font-weight: 400;
    color: #231F20;
} */
.describtion p {
    word-wrap: break-word;
    font-size: 12px;
    font-weight: 400;
    color: #231F20;
    margin-top: 0px !important;
    margin-bottom: 0px;
}

.noUi-touch-area {
    height: 100%;
    width: 100%;
    background: #30583a;
    border-radius: 100%;
}

.noUi-handle {
    border: 1px solid #d9d9d900 !important;

    background: #ffffff00 !important;

    box-shadow: none !important;
}

.noUi-connect {
    background: #30583a !important;
    /* height: 6px !important; */
}

.noUi-horizontal {
    height: 7px !important;
}

.noUi-handle:after {
    content: "";
    display: none !important;
    position: absolute;
    height: 100%;
    width: 100%;
    background: #30583a !important;
    left: 0 !important;
    top: 0 !important;
    border: 1px solid #fff;
}

.noUi-handle:before {
    content: "";
    display: block;
    position: absolute;
    height: 67% !important;
    width: 60% !important;
    background: #30583a !important;
    left: 2px !important;
    top: 1px !important;
    border: 3px solid #fff;
    border-radius: 100%;
}

.noUi-touch-area {
    height: 75% !important;
    width: 73% !important;
}

.product-details-banner {
    background: url(../img/ozone/product-details-banner.png) no-repeat center;
    position: relative;
    /* min-height: 640px; */
}

.product-gallery {
    position: relative;
}

.product-gallery .tiles {
    display: flex;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.product-gallery .tile {
    flex: 1;
    position: relative;
    overflow: hidden;
    cursor: zoom-in;
}

.product-gallery .photo {
    width: 100%;
    height: 100%;
    overflow: hidden;
    margin: auto;
}

.product-gallery .photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    transform-origin: center center;
    background: none;
}

.product-gallery .txt {
    position: absolute;
    z-index: 2;
    right: 0;
    bottom: 10%;
    left: 0;
    font-size: 9px;
    line-height: 12px;
    text-align: center;
    pointer-events: none;
}

.product-gallery .x {
    font-size: 32px;
    line-height: 32px;
}

.product-features .featured-content {
    padding: 20% 0px 0px;
}

.highlight-img {
    background: url(../img/ozone/Brush.png) no-repeat left bottom;
    padding-bottom: 10px;
    padding-right: 63px;
}

.highlight-img-2-addition {
    background: url(../img/ozone/addition.png) no-repeat left 115px top 30px;
    padding-bottom: 30px;
    padding-top: 30px;
    padding-right: 90px;

}

.highlight-img-3-brief {
    background: url(../img/ozone/Brush.png) no-repeat right -17px bottom;
    padding-bottom: 0px;
    padding-top: 31px;
    padding-right: 30px
}


.highlight-img-2 {
    background: url(../img/ozone/highlight-img-2.png) no-repeat center bottom;
    padding-bottom: 0px;
    padding-top: 31px;
}

.highlight-img-3 {
    background: url(../img/ozone/highlight-img-3.png) no-repeat right 9px bottom;
    padding-bottom: 0px;
    padding-top: 31px;
    padding-right: 42px;
}

.product-gallery .main-swiper #swiper-carousel .carousel-control-prev span img {
    position: absolute;
    top: 0px;
}

.product-gallery .main-swiper #swiper-carousel .carousel-control-next span img {
    position: absolute;
    top: 0px;
}

.product-gallery .main-swiper #swiper-carousel .carousel-control-prev {
    top: 0px;
    right: 0px;
    left: 70%;
}

.product-details-banner .rating {
    color: #FF6200;
    font-size: 12px;
    line-height: 12px;
}

.product-details-banner .rating-green {
    color: #276100;
    font-size: 12px;
    line-height: 12px;
}

.product-details-banner .text {
    color: #276100;
    font-size: 16px;
    padding: 0px 10px;
}

.rating-value {
    font-size: 16px;
    font-weight: 400;
    padding: 0px 0px 0px 10px;
}

.model-text {
    align-items: baseline
}

.price-range {
    color: #276100;
    /* font-size: 16px; */
    font-weight: 600;
}

.increment-decrement {
    height: 52px;
    max-width: 150px;
    border-radius: 50px;
    border: 1px solid #A2A3B1;
}

.increment-decrement .btn,
.increment-decrement input {
    font-size: 20px;
    color: #231F20;
    font-weight: 600;
    border: 0px;
}

.btn-add-cart {
    background: #231F20;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    border-radius: 25px;
    padding: 15px;
}

.btn-add-cart:hover {
    background: #231F20;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    border-radius: 25px;
    padding: 15px;
}

.btn-wishlist {
    color: #276100;
    font-size: 16px;
    padding: 15px;
    font-weight: 600;
    margin-top: 20px;
}

..btn-wishlist img {
    padding-right: 10px !important;
}

.bottom-line {
    border-bottom: 2px solid #D9D9D9;
}

.bottom-line p {
    color: #6A6D70;
    font-size: 22px;
    font-weight: 400;
    margin: 0px;
}

.bottom-line .fw-bold {
    color: #1E1E1E;
    font-size: 22px;
    font-weight: 600;
}

.Specifications .text-success-title {
    font-size: 32px;
    font-weight: 700;
    color: #30583A;
}

.Specifications .btn-outline-secondary,
.reviews .btn-outline-secondary {
    background: #D9D9D9;
    border: 0px;
    font-weight: 600;
    color: #231F20;
}

.reviews .review-card {
    border-radius: 12px;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}

.reviews .star-rating {
    color: #f97316;
    font-size: 25px;
}

.reviews .review-header {
    /* border-bottom: 1px solid #e5e5e5;
    padding-bottom: 10px; */
}

.reviews .review-footer {
    text-align: right;
    font-size: 18px;
    font-weight: 500;
    color: #525151;
}

.reviews .customer-reviews-title {
    color: #276100;
    font-weight: bold;
}

.reviews .sec-title span {
    color: #276100 !important;
    font-size: 40px;
}

.reviews .progress-d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reviews .progress {
    width: 90%;
    height: 10px;
}

.reviews .ratings {
    display: contents !important;
    font-size: 12px;
}

.reviews h3 {
    font-size: 14px;
    font-weight: 500;
}

.reviews .progress-bar {
    background: #212121;
}

.review-product h3 {
    font-size: 18px;
    font-weight: 500;
}

.review-product p {
    font-size: 12px;
}

.total-rating {
    color: #6A737D;
    font-size: 14px;
    font-weight: 400;
    margin-left: 10px;
}

.newest-first {
    border: 1px solid #6FC284;
    background: none;
    color: #30583A;
    border-radius: 20px;
}

.need-help .help-card {
    background-color: #d1f7d4;
    border-radius: 1rem;
    height: 100%;
    transition: transform 0.2s;
    position: relative;
}

.need-help .help-card:hover {
    transform: translateY(-5px);
}

.need-help .arrow-circle {
    position: absolute;
    bottom: 15px;
    right: 15px;
}

.need-help .text-success {
    font-weight: 600;
    font-size: 35px;
    color: #276100 !important;
}

.sec-title {
    font-size: 55px;
    font-weight: 600;
    margin-top: 50px;
}

.help-card h5 {
    color: #276100;
    font-size: 35px;

}

.help-card p {
    font-size: 18px;
    color: #5B5B5B;
}



.thumb-swiper {
    height: 100px;
    box-sizing: border-box;
    padding: 10px 0;
}

.thumb-swiper .swiper-slide {
    width: 25%;
    height: 100%;
    opacity: 0.5;
    cursor: pointer;
    border: 1px solid #D1D1D8;
    border-radius: 5px;
    width: 104px !important;
    height: 85px !important;
}

.thumb-swiper .swiper-slide-thumb-active {
    opacity: 1;
    border: 2px solid #53882F;
    border-radius: 5px;
    padding: 5px;
    width: 104px !important;
    height: 85px !important;
}

.swiper-wrapper {
    justify-content: center !important;
}

.thumbnail-images {
    position: relative;
    bottom: -6%;
    /* max-width: 625px; */
}

.swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #fff;
    border-radius: 5px;
}

.swiper-counter {
    position: absolute;
    top: 10px;
    right: 15%;
    z-index: 10;
    font-size: 16px;
    font-weight: bold;
    color: #000;
}

.swiper-navigation {
    display: flex;
    align-items: center;
    margin-top: 15px;
    gap: 0px;
    position: relative;
    right: 11%;
    top: 55px;
    justify-content: space-evenly;
}

.purchase-cta {
    margin-top: 45px;
}

.swiper-wrapper-c3b46e40cab2114b {
    bottom: -20px;
}

.swiper-button-prev {
    left: auto !important;
    right: 50px !important;
}

.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
    color: #000;
    font-size: 15px !important;
}

.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
    color: #000;
    font-size: 15px !important;
}

.custom-nav {
    font-size: 15px;
    cursor: pointer;
    color: #000;
    user-select: none;
    padding: 4px 12px;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0);
    transition: background 0.2s;
}

.custom-nav:hover {
    background: rgba(0, 0, 0, 0.1);
}

.swiper-counter .total-slides {
    color: #999;
}

.scroll-spy .nav-tabs-scroll {
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
    padding-top: 1rem;
    padding-bottom: 0.5rem;
}

.scroll-spy .nav-link {
    color: #6A6D70;
    font-size: 25px;
    font-weight: 400;
}

.scroll-spy .nav-link.active {
    color: #0a501c;
    /* Active green */
    font-size: 25px;
    font-weight: 600;
}

.about-general-banner {
    background: url(../img/ozone/aboutus-banner.png) no-repeat center;
    position: relative;
    height: 480px;
    background-size: cover;
    text-align: center;
}

.about-general-bann {
    text-align: center;
}

.aboutus-banner-sec-2 {
    background: url(../img/ozone/aboutus-sec-2.png) no-repeat center top 5px;
    position: relative;
    /* height: 753px; */
}

.aboutus-describtion {
    color: #0a501c;
    font-size: 22px;
    font-weight: 400;
    text-align: justify;
}

.aboutus-banner-sec-2 h2 {
    font-size: 35px;
    font-weight: 700;
    text-align: justify;
}

.aboutus-sec-3-bg-green {
    background-color: #CBFED7;
    padding: 20px;
}

.aboutus-sec-3-bg-green .aboutus-describtion {
    font-size: 20px;
    font-weight: 700;
    color: #0a501c;
    text-align: center;
    font-family: 'Sansation', sans-serif;
}

.aboutus-sec-3-bg-green h2 {
    font-size: 40px;
    font-weight: 700;
}

.aboutus-sec-3-bg-green .card-aboutus {
    padding: 15px;
}

.services .carousel-control-next-icon {
    width: 51px;
    height: 50px;
}

.services .carousel-item .card-box-shadow {
    background: #fff;
    padding: 10px;
    border-radius: 12px;
    box-shadow: 0px 0px 30px 15px #058E6E24;
    height: 75px;
    width: 85%;
    margin: auto;
    display: flex;
    align-items: center;
}

.services .carousel-item .card-box-shadow img {
    margin-right: 15px;
    margin-left: 15px;
}

.services .carousel-control-prev {
    left: -6%;
}

.services .carousel-control-next {
    right: -5%;
}

.section-title-abut-install {
    position: relative;
    font-size: 36px;
    font-weight: 700;
}

.centralized-ac {
    background: url(../img/ozone/centralized-ac.png) no-repeat top center;
    min-height: 520px;
}

.content-brief {
    background: url(../img/ozone/Subtract-corprate-brief.png) no-repeat top center;
}

.content-brief .content p {
    color: #231F20;
    font-size: 22px;
    font-weight: 400;
    line-height: 50px;
    text-align: justify;
    padding-bottom: 50px;
}

.misson-vission {
    /* background: url(../img/ozone/vision-banner.png) no-repeat left center; */
}

.misson-vission .vision {
    border: 1px solid #00000000;
    padding: 30px 40px;
    border-radius: 20px;
}

.misson-vission .vision .title {
    color: #034833;
    font-size: 35px;
    font-weight: 700;
    width: 400px;
}

.misson-vission .vision:hover {
    border: 1px solid #6FC284;
    padding: 30px 40px;
    border-radius: 20px;
}

.misson-vission .vision .title {
    margin: 10px;
}

.misson-vission .vision .innovation {
    color: #FF6200;
    font-weight: 700;
    font-size: 20px;
}

.misson-vission .vision .describtion {
    color: #30583A;
    font-weight: 400;
    font-size: 20px;
}

.misson-vission .vision .describtion br {
    margin-bottom: 10px;
}

.exclude {
    background: url(../img/ozone/Exclude.png) no-repeat top center;
    height: 990px;
    margin: 40px auto;
    position: relative;
    z-index: 9;
    background-size: cover;
    max-width: 1650px;
}

.exclude .header {
    padding-top: 80px;
    padding-bottom: 30px;
    border-bottom: 1px solid transparent;
    /* Set solid border and make it transparent to apply border-image */
    border-image-source: linear-gradient(270deg, rgba(78, 101, 182, 0) -8.63%, #000000 50.54%, rgba(78, 101, 182, 0) 104.98%);
    border-image-slice: 1;
    /* Required to apply the gradient to the border */
}

.exclude .content {
    max-width: 430px;
    padding-left: 20px;
}

.exclude .content h3 {
    color: #fff;
    font-size: 40px;
    font-weight: 700;
    text-align: left;
    margin-bottom: 0px;
}

.exclude .content p {
    color: #000000;
    font-size: 18px;
    font-weight: 400;
    text-align: left;
    background: none !important;
    padding: 0px;

}

.our-strength {
    /* background: url(../img/ozone/our-strength-banner.png) no-repeat top center; */
    height: auto;
    position: relative;
    top: 0px;
    z-index: 3;
}

.our-strength .content {
    padding-top: 10px;
}

#our-strength {
    margin: 80px 0px;
}

.our-strength .owl-nav {
    display: none;
}

#our-strength .item .testimonial-card {
    background: #fff;
    padding: 10px;
    border-radius: 20px;
    text-align: left;
}

#our-strength .item .testimonial-card h2 {
    font-size: 50px;
    color: transparent;
    -webkit-text-stroke: 1px #004225;
    font-weight: 800;
    letter-spacing: 2px;
    font-family: "Plus Jakarta Sans", sans-serif;
}

.center-owl-carousel {
    transform: scale(1.05);
    margin-top: 0px;
    z-index: 2;
    box-shadow: 0 8px 30px rgba(0, 66, 37, 0.15);
}

#our-strength .owl-item {
    transition: transform 0.3s ease, margin 0.3s ease;
    margin-top: 30px;
}

#our-strength .owl-item.center {
    transform: scale(1.05);
    margin-top: 0px;
    /* brings center item lower */
    z-index: 2;
}

.contact-section {
    padding: 50px 0;
}

.contact-info {
    background-color: #f8f9fa;
    color: #000;
    padding: 50px 20px;
    background-image: url('../img/ozone/Contact-Info.png');
    /* Light pattern */
}

.contactus-form small,
.contact-info small {
    color: #276100;
    font-size: 24px;
    font-weight: 400;
}

#contact {
    text-align: left;
}

.contact-info hr {
    width: 25px;
    height: 3px;
    background: #000000;
    opacity: 100%;
}

#contact .btn-custom {
    background-color: #2f5233;
    color: #fff;
    border-radius: 25px;
    padding: 20px 30px;
}

#contact .btn-custom:hover {
    background-color: #244027;
    color: #fff;
}

#contact .form-control {
    border: 1px solid #CACACA;
    border-radius: 20px;
    padding: 15px 15px;
}

.cart-section .cart-card {
    background: #fff;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #6C6C6C
}

.cart-section .summary-card {
    background: none;
    border-radius: 20px;
    padding: 20px;
    border: 1px solid #6C6C6C
}

.cart-section .summary-card .title {
    text-align: center;
    color: #276100 !important;
}

.total-head {
    font-size: 22px;
    font-weight: 700;
    color: #060709;
}

.total-amount {
    font-size: 22px;
    font-weight: 700;
    color: #53882F;
}

.cart-section .summary-card .text-success {
    text-align: right;
    color: #30583A !important;
    font-weight: 700;
    font-size: 14px;
}

.cart-section .btn-remove {
    background-color: #b65959;
    color: #fff;
    border-radius: 10px;
    padding: 8px 20px;
    border: none;
    margin: 0px 10px 0px 0px !important;
}

.cart-section .btn-place-order {
    background-color: #30583A;
    color: #FFFFFF;
    border-radius: 100px;
    width: 100%;
    padding: 12px 30px;
    font-weight: 500;
    font-size: 16px;

}

.cart-section .payment-icons img {
    width: 40px;
    margin-right: 8px;
}

.cart-section .coupon-input {
    border-radius: 8px;
    border: 1px solid #F4F4F4;
    color: #C8C9CB;
}

.cart-section .apply-coupon-btn {
    background: #F3FBF4;
    color: #17AF26;
    border-radius: 30px;
    padding: 5px 10px;
    font-size: 14px;
    font-weight: 400;
    border: none;
    width: 220px;
}

.cart-section .form-check-input {
    border-radius: 5px !important;
    border: 1px solid #A9A9A9;
    margin-right: 11px;
}

.cart-tab-head {
    background: #F4F4F4 !important;
}

#purchase-tab .nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link {
    color: #231F20;
    font-size: 16px;
    font-weight: 600;
    background: #231F2026;
    border: 0px;
    white-space: nowrap;
}

#purchase-tab .nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    color: #ffffff;
    font-size: 16px;
    font-weight: 700;
    background: #231F20;
    border: 0px;
}

.stats-card p {
    color: #6A6D70;
}

#purchase-tab .nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link .cart-img {
    background: #FFFFFF;
    border-radius: 50px;
    padding: 5px 10px 7px;
    margin-right: 5px;
}

#purchase-tab .nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active .cart-img {
    background: #30583A;
    border-radius: 50px;
    padding: 5px 10px 7px;
    margin-right: 5px;
    color: #060709;
    font-size: 16px;
    font-weight: 700;
}

.cart-tab-head .active .cart-img img {
    filter: invert(100%) sepia(4%) saturate(0%) hue-rotate(165deg) brightness(103%) contrast(103%);

}

#purchase-tab {
    align-items: center;
    justify-content: space-evenly;
}

.cart-tab-head ul.nav-tabs {
    display: flex;
    justify-content: space-between;
    position: relative;
    list-style: none;
    padding: 0;
}

.cart-tab-head ul.nav-tabs li {
    position: relative;
    display: flex;
    align-items: center;
}

.cart-tab-head ul.nav-tabs li::after {
    content: "";
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: 2px;
    background-color: #e0e0e0;
    z-index: 0;
}

.cart-tab-head ul.nav-tabs li:last-child::after {
    content: none;
}

.purchase-cart .text-success {
    color: #53882F;
}

.purchase-cart .bg-light {
    background: #F2F2F2;
}

.cart-remove-add-qua .increment-decrement {
    height: 45px;
    max-width: 150px;
    border-radius: 50px;
    border: 1px solid #A2A3B1;
}

.summary-card .describtion {
    color: #9D9EA2;
    font-size: 14px;
    font-weight: 400;
    padding: 5px 0px;
    margin-top: 0px;
    min-height: 25px !important;
}

.summary-card hr {
    color: #F4F4F4;
    height: 1px;
    opacity: 1;
}

.secure-pay-terms {
    font-family: "Lexend", sans-serif;
    font-size: 12px;
    font-weight: 300;
    color: #717378;
    text-transform: uppercase;
}

.cart-section .address-option .form-check-input {
    border-radius: 17px !important;
    border: 1px solid #30583A;
    margin-right: 11px;
}

.cart-section .address-option p {
    font-size: 16px;
    font-weight: 400;
}

.cart-section .address-option h6 {
    font-size: 16px;
    font-weight: 700;
}

.cart-section .address-option .contact-txt {
    color: #1E1E1E;
}

.cart-section .btn-add {
    background: #30583A;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 700;
    border-radius: 53px;
    padding: 12px;
}

.cart-section .address-option .action-links .remove {
    text-decoration: none;
    color: #E14B4B;
    font-size: 16px;
    padding: 0px 10px;
    font-weight: 600;
}

.cart-section .address-option .action-links .edit {
    border-right: 1px solid #D1D1D8;
    color: #17183B;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    padding: 0px 10px;
}

.cart-card .section-title {
    color: #231F20;
    font-size: 20px;
    font-weight: 700;
    text-align: center;
}

.shipping-details .form-label {
    font-size: 12px;
    font-weight: 700;
    color: #231F20;
}

.shipping-details .form-control,
.shipping-details .form-select {
    border-radius: 8px;
}

.language-footer .active {
    color: #30583A;
    font-weight: 700;
    font-size: 16px;
}


/* .payment-info .form-check-input:checked[type=radio] {
    background-image: url(../img/ozone/radio-checkout.png)!important;
   
} */
.payment-info .form-check-input:checked[type=radio] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%233AA39F'/%3e%3c/svg%3e");
}

.payment-info .form-check-input:checked {
    background-color: #ffffff;
    border: 2px solid #3AA39F;
}

.payment-info .form-check-input {
    border-radius: 11px !important;
    border-color: #3AA39F !important;
    margin-right: 11px;
}

.payment-method-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid #ddd;
}

.payment-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

.payment-info img {
    width: 40px;
    height: auto;
}

.payment-meta {
    display: flex;
    /* flex-direction: column; */
}

.payment-meta span {
    padding-right: 30px;
}

.remove-btn {
    color: #E14B4B;
    cursor: pointer;
    text-decoration: none;
}

.remove-btn:hover {
    text-decoration: underline;
}

.purchase-success .estimated {
    color: #17183B;
    font-size: 16px;
    font-weight: 400;
}

.purchase-success .estimated-date {
    font-size: 16px;
    font-weight: 700;
    color: #FF6200;
}

.purchase-success .ord-placed-content p {
    font-weight: 400;
    font-size: 30px;
    color: #000000;
    text-align: center;
}

.purchase-success .ord-placed-content p span {
    color: #53882F;
    font-weight: 700;
    font-size: 45px;
    text-align: center;
}

.purchase-success {
    text-align: center;
}

body.rtl .product-card .substract .position-absolute .d-flex {
    flex-direction: row-reverse !important;
}
body.rtl .product-general-banner .blur-bg{
  
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    border-top-left-radius: 65px;
    border-bottom-left-radius: 65px;
}

body.rtl .stats-card {
    text-align: right;
    margin-bottom: 20px;
}

body.rtl .latest-offers {
    background: none;
}

body.rtl .latest-offers .section-title span {
    background: url(../img/ozone/latest-substract.png) no-repeat center bottom -27px;
    padding: 0px 0px 46px;
}
body.rtl .swiper-navigation {
 
    right: 7% !important;
}
.zoom-btn {
    background: none;
    border: none;
    border-radius: 50%;
    padding: 5px;
    cursor: pointer;
}

.zoomable {
    transition: transform 0.3s ease;
}

.zoomed {
    transform: scale(1.5);
    cursor: zoom-out;
}

.carousel-inner {
    overflow: hidden;
}

.chng-pass {
    background: #004225;
}

.myaccount {
    background: url(../img/ozone/lead-banner.png) no-repeat top left;
    height: 100%;
    max-width: 1690px;
    margin: auto
}

.myaccount .tab-content .tab-pane .account-form,
#orderAccordion,
#wishlist-tab,
#changePassword {
    /* height: 470px; */
    /* overflow-y: scroll; */
    width: 100%;
    /* overflow-x: hidden; */
}

#changePassword form {
    background: #fff;
    padding: 15px;
    border-radius: 10px;
}

.myaccount .list-group {
    border-radius: 10px;
}

.myaccount .list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: #004225;
    border-color: #004225;
}

#orders-tab .status-badge {
    font-size: 0.85rem;
    padding: 0.4em 0.7em;
    border-radius: 0.5rem;
    text-transform: capitalize;
}

#orders-tab .status-processing {
    background-color: #ffe08a;
    color: #664d03;
    font-weight: 600;
}

#orders-tab .status-pending,
.status-partially {
    background-color: #ffc107;
    /* yellow */
    color: #664d03;
    font-weight: 600;
}

#orders-tab .status-shipped,
.status-return {
    background-color: #cfe2ff;
    color: #084298;
    font-weight: 600;
}

#orders-tab .status-confirmed,
.status-approved,
.status-out {
    background-color: #28a745;
    /* green */
    color: white;
    font-weight: 600;
}

#orders-tab .status-rejected,
.status-cancellation {
    background-color: #dc3545;
    /* red */
    color: white;
    font-weight: 600;
}

#orders-tab .status-expired,
.status-deleted {
    background-color: #6c757d;
    /* gray */
    color: white;
    font-weight: 600;
}

#orders-tab .status-delivered {
    background-color: #d1e7dd;
    color: #0f5132;
    font-weight: 600;
}

#orders-tab .status-cancelled,
.status-refunded,
.status-rejected,
.status-returned {
    background-color: #f8d7da;
    color: #842029;
    font-weight: 600;
}

#wishlist-tab .wishlist-card img {
    object-fit: cover;
    height: auto;

}

.myaccount .accordion-button {
    border: solid 2px #355C3F;
    color: #355C3F;
    background-color: #fff;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
}

.myaccount .btn-outline-primary {
    color: #231F20;
    background-color: #D9D9D9;
    border: 0px;
    border-radius: 42px;
    font-size: 15px;
    font-weight: 700;
}

.myaccount .tab-pane .accordion .accordion-item {
    border-radius: 10px;
}

.btn-outline-danger {
    background: #AE5757;
    color: #FFFFFF;
    border-radius: 16px;
}

.myaccount .tab-pane .accordion .accordion-item .btn-primary,
.myaccount .tab-pane .card .btn-primary {
    border-radius: 16px;
}

.testimonial-card h5 {
    font-size: 22px;
    font-weight: 700;
    font-family: "Plus Jakarta Sans", sans-serif;
    color: #034833;
}

/* .products .tab-content .tab-pane .owl-carousel .item {
  display: block !important ;
  padding: 15px;
} */
.products .tab-content .tab-pane .owl-carousel {
    display: block !important;
    overflow: hidden;
}

.prdt-viewmore {
    text-align: right;
}

.prdt-viewmore button {
    background-color: #6FC284;
    color: #231F20;
    font-size: 18px;
    font-weight: 700;
}

.text-truncate-2-line {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 50px;
}


.noUi-horizontal .noUi-handle {
    top: -8px !important;
}

/* Base container for filters + products */
.filtermenu {
    position: relative;
    /* overflow-x: hidden; */
}

/* Filter Panel Styling */
/* .filter-slide-panel {
  position: absolute;
  top: 0;
  left: -300px; 
  width: 300px;
  height: 100%;
  background: #fff;
  z-index: 1000;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  padding: 15px;
} */
.filter-slide-panel {
    position: absolute;
    /* absolute can also work */
    top: 10px;
    left: -350px;
    /* hidden initially */
    width: 300px;
    height: 100vh;
    background: white;
    z-index: 9999;
    transition: left 0.3s ease-in-out;
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.filter-slide-panel.open {
    left: 0;
    /* slide in */
    padding: 20px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
}

/* When panel is visible */
.filter-slide-panel.open {
    left: 0;
}

/* Shift the product list to the right when filter panel is open */
.product-list-container {
    transition: margin-left 0.3s ease-in-out;
}

.product-list-container.shifted {
    margin-left: 100%;
    overflow: hidden;
    display: none;
}

.filter-btn {
    background-color: none;
    border: 1px solid #6FC284;
    border-radius: 28px;
    color: #30583A;
    font-size: 14px;
    font-weight: 700;
}

.flt-clr-btn {
    font-size: 14px;
    font-weight: 700;
    color: #53882F;
    text-decoration: none;
}
