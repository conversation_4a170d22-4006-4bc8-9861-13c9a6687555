<?php
declare(strict_types=1);

namespace App\Command;

use Cake\Command\Command;
use <PERSON>ake\Console\Arguments;
use <PERSON>ake\Console\ConsoleIo;
use <PERSON>ake\Console\ConsoleOptionParser;
use Cake\Datasource\ConnectionManager;

/**
 * SetupRoleCountryPermissions command.
 */
class SetupRoleCountryPermissionsCommand extends Command
{
    /**
     * Hook method for defining this command's option parser.
     *
     * @see https://book.cakephp.org/4/en/console-commands/commands.html#defining-arguments-and-options
     * @param \Cake\Console\ConsoleOptionParser $parser The parser to be defined
     * @return \Cake\Console\ConsoleOptionParser The built parser.
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser = parent::buildOptionParser($parser);
        $parser->setDescription('Setup role country permissions table and default data');

        return $parser;
    }

    /**
     * Implement this method with your command's logic.
     *
     * @param \Cake\Console\Arguments $args The command arguments.
     * @param \Cake\Console\ConsoleIo $io The console io
     * @return null|void|int The exit code or null for success
     */
    public function execute(Arguments $args, ConsoleIo $io)
    {
        $io->out('Setting up Role Country Permissions...');
        
        $connection = ConnectionManager::get('default');
        
        try {
            // Check if roles table exists
            $rolesExists = $this->tableExists($connection, 'roles');
            if (!$rolesExists) {
                $io->error('Roles table does not exist. Please ensure your basic tables are set up first.');
                return static::CODE_ERROR;
            }
            
            // Check if countries table exists
            $countriesExists = $this->tableExists($connection, 'countries');
            if (!$countriesExists) {
                $io->error('Countries table does not exist. Please ensure your basic tables are set up first.');
                return static::CODE_ERROR;
            }
            
            // Add country_access_type to roles table if it doesn't exist
            $io->out('Adding country_access_type column to roles table...');
            $this->addCountryAccessTypeColumn($connection, $io);
            
            // Create role_country_permissions table
            $io->out('Creating role_country_permissions table...');
            $this->createRoleCountryPermissionsTable($connection, $io);
            
            // Set default permissions for existing roles
            $io->out('Setting default permissions for existing roles...');
            $this->setDefaultPermissions($connection, $io);
            
            $io->success('Role Country Permissions setup completed successfully!');
            
        } catch (\Exception $e) {
            $io->error('Error during setup: ' . $e->getMessage());
            return static::CODE_ERROR;
        }
        
        return static::CODE_SUCCESS;
    }
    
    /**
     * Check if a table exists
     */
    private function tableExists($connection, $tableName)
    {
        try {
            $schema = $connection->getSchemaCollection();
            return in_array($tableName, $schema->listTables());
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Check if a column exists in a table
     */
    private function columnExists($connection, $tableName, $columnName)
    {
        try {
            $schema = $connection->getSchemaCollection()->describe($tableName);
            return $schema->hasColumn($columnName);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Add country_access_type column to roles table
     */
    private function addCountryAccessTypeColumn($connection, $io)
    {
        if ($this->columnExists($connection, 'roles', 'country_access_type')) {
            $io->out('  - country_access_type column already exists in roles table');
            return;
        }

        try {
            $sql = "ALTER TABLE `roles`
                    ADD COLUMN `country_access_type` ENUM('all', 'specific', 'user_country') DEFAULT 'all' NOT NULL
                    COMMENT 'all=access all countries, specific=access specific countries only, user_country=access only user assigned country'";

            $connection->execute($sql);
            $io->out('  - Added country_access_type column to roles table');
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                $io->out('  - country_access_type column already exists in roles table');
            } else {
                throw $e;
            }
        }
    }
    
    /**
     * Create role_country_permissions table
     */
    private function createRoleCountryPermissionsTable($connection, $io)
    {
        if ($this->tableExists($connection, 'role_country_permissions')) {
            $io->out('  - role_country_permissions table already exists');
            return;
        }
        
        // First, let's check the actual column types
        $rolesSchema = $connection->getSchemaCollection()->describe('roles');
        $countriesSchema = $connection->getSchemaCollection()->describe('countries');

        $roleIdType = $rolesSchema->getColumn('id')['type'];
        $countryIdType = $countriesSchema->getColumn('id')['type'];

        $io->out("  - Roles.id type: {$roleIdType}");
        $io->out("  - Countries.id type: {$countryIdType}");

        // Create table without foreign keys first
        $sql = "CREATE TABLE `role_country_permissions` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `role_id` INT(11) NOT NULL COMMENT 'Foreign key to roles table',
            `country_id` INT(11) NULL COMMENT 'Foreign key to countries table. NULL means access to all countries',
            `can_access` TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Whether this role can access this country',
            `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            INDEX `idx_role_id` (`role_id`),
            INDEX `idx_country_id` (`country_id`),
            UNIQUE INDEX `unique_role_country` (`role_id`, `country_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
        
        $connection->execute($sql);
        $io->out('  - Created role_country_permissions table');
    }
    
    /**
     * Set default permissions for existing roles
     */
    private function setDefaultPermissions($connection, $io)
    {
        // Check if there are already permissions set
        $result = $connection->execute("SELECT COUNT(*) as count FROM role_country_permissions")->fetch();
        $existingCount = $result ? $result['count'] : 0;
        
        if ($existingCount > 0) {
            $io->out('  - Default permissions already exist');
            return;
        }
        
        // Insert default permissions (all roles get access to all countries)
        $sql = "INSERT INTO `role_country_permissions` (`role_id`, `country_id`, `can_access`)
                SELECT `id`, NULL, 1 FROM `roles` WHERE `status` != 'D'";
        
        $result = $connection->execute($sql);
        $affectedRows = $result->rowCount();
        
        $io->out("  - Set default permissions for {$affectedRows} roles");
    }
}
