a:74:{s:16:"attribute_values";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:16:"attribute_values";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:12:"attribute_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"value";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"value_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:12:"attribute_id";s:7:"integer";s:5:"value";s:6:"string";s:8:"value_ar";s:6:"string";s:6:"status";s:6:"string";}s:11:" * _indexes";a:1:{s:12:"attribute_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:12:"attribute_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:23:"attribute_values_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:12:"attribute_id";}s:10:"references";a:2:{i:0;s:10:"attributes";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:10:"attributes";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:10:"attributes";s:11:" * _columns";a:6:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"key_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:6:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:8:"key_name";s:6:"string";s:6:"status";s:6:"string";}s:11:" * _indexes";a:1:{s:21:"fk_attributes_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:21:"fk_attributes_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:12:"audit_trails";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:12:"audit_trails";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:7:"user_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"table_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"record_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"action";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"column_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"old_value";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"new_value";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"modified_at";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:7:"user_id";s:7:"integer";s:10:"table_name";s:6:"string";s:9:"record_id";s:7:"integer";s:6:"action";s:6:"string";s:11:"column_name";s:6:"string";s:9:"old_value";s:4:"text";s:9:"new_value";s:4:"text";s:11:"modified_at";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:7:"user_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"user_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:19:"audit_trails_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"user_id";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb3_general_ci";}s:13:" * _temporary";b:0;}s:10:"banner_ads";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:10:"banner_ads";s:11:" * _columns";a:20:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:5:"title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"summary";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"ad_location";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"ad_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"brand_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"url_link";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"display_in_web";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"display_in_mobile";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"web_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"mobile_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"ad_position";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"start_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"end_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"display_order";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:20:{s:2:"id";s:7:"integer";s:5:"title";s:6:"string";s:7:"summary";s:6:"string";s:11:"ad_location";s:6:"string";s:7:"ad_type";s:6:"string";s:11:"category_id";s:7:"integer";s:8:"brand_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:8:"url_link";s:6:"string";s:14:"display_in_web";s:7:"boolean";s:17:"display_in_mobile";s:7:"boolean";s:9:"web_image";s:6:"string";s:12:"mobile_image";s:6:"string";s:11:"ad_position";s:6:"string";s:10:"start_date";s:4:"date";s:8:"end_date";s:4:"date";s:13:"display_order";s:11:"tinyinteger";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:3:{s:11:"category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:6:"length";a:0:{}}s:8:"brand_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"brand_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:17:"banner_ads_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"brand_id";}s:10:"references";a:2:{i:0;s:6:"brands";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:17:"banner_ads_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:10:"references";a:2:{i:0;s:10:"categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:17:"banner_ads_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb3_general_ci";}s:13:" * _temporary";b:0;}s:7:"banners";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:7:"banners";s:11:" * _columns";a:20:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"title_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"summary";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"summary_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"banner_location";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"banner_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"url_link";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"target_mob";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"display_in_web";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"display_in_mobile";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"web_banner";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"mobile_banner";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"start_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"end_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"display_order";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:20:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:5:"title";s:6:"string";s:8:"title_ar";s:6:"string";s:7:"summary";s:6:"string";s:10:"summary_ar";s:6:"string";s:15:"banner_location";s:6:"string";s:11:"banner_type";s:6:"string";s:8:"url_link";s:6:"string";s:10:"target_mob";s:6:"string";s:14:"display_in_web";s:7:"boolean";s:17:"display_in_mobile";s:7:"boolean";s:10:"web_banner";s:6:"string";s:13:"mobile_banner";s:6:"string";s:10:"start_date";s:4:"date";s:8:"end_date";s:4:"date";s:13:"display_order";s:11:"tinyinteger";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:14:"banners_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:23:"brand_category_mappings";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:23:"brand_category_mappings";s:11:" * _columns";a:3:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"brand_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}}s:11:" * _typeMap";a:3:{s:2:"id";s:7:"integer";s:8:"brand_id";s:7:"integer";s:11:"category_id";s:7:"integer";}s:11:" * _indexes";a:2:{s:8:"brand_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"brand_id";}s:6:"length";a:0:{}}s:11:"category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:30:"brand_category_mappings_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"brand_id";}s:10:"references";a:2:{i:0;s:6:"brands";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:30:"brand_category_mappings_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:10:"references";a:2:{i:0;s:10:"categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:6:"brands";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:6:"brands";s:11:" * _columns";a:18:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"url_key";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"brand_logo";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"web_banner";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"mobile_banner";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"is_featured";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"warranty";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:10:"meta_title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"meta_keyword";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"meta_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:18:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:7:"url_key";s:6:"string";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:11:"description";s:4:"text";s:14:"description_ar";s:4:"text";s:10:"brand_logo";s:6:"string";s:10:"web_banner";s:6:"string";s:13:"mobile_banner";s:6:"string";s:11:"is_featured";s:7:"boolean";s:8:"warranty";s:7:"decimal";s:10:"meta_title";s:6:"string";s:12:"meta_keyword";s:6:"string";s:16:"meta_description";s:4:"text";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:13:"brands_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:13:"cancellations";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:13:"cancellations";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:31:"cancellation_return_category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"remarks";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:8:"order_id";s:7:"integer";s:31:"cancellation_return_category_id";s:7:"integer";s:7:"remarks";s:4:"text";s:7:"created";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:8:"order_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}s:24:"cancellation_category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:31:"cancellation_return_category_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:20:"cancellations_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:20:"cancellations_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:31:"cancellation_return_category_id";}s:10:"references";a:2:{i:0;s:29:"order_cancellation_categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:20:"cart_item_attributes";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:20:"cart_item_attributes";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:7:"cart_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"cart_item_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:20:"product_attribute_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:7:"integer";s:7:"cart_id";s:7:"integer";s:12:"cart_item_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:20:"product_attribute_id";s:7:"integer";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:4:{s:7:"cart_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"cart_id";}s:6:"length";a:0:{}}s:12:"cart_item_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:12:"cart_item_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:20:"product_attribute_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:20:"product_attribute_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:5:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:27:"cart_item_attributes_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"cart_id";}s:10:"references";a:2:{i:0;s:5:"carts";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:27:"cart_item_attributes_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:12:"cart_item_id";}s:10:"references";a:2:{i:0;s:10:"cart_items";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:27:"cart_item_attributes_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:27:"cart_item_attributes_ibfk_4";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:20:"product_attribute_id";}s:10:"references";a:2:{i:0;s:18:"product_attributes";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:10:"cart_items";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:10:"cart_items";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:7:"cart_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:18:"product_variant_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:7:"cart_id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:18:"product_variant_id";s:7:"integer";s:8:"quantity";s:7:"integer";s:5:"price";s:7:"decimal";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:4:{s:7:"cart_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"cart_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:18:"product_variant_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:18:"product_variant_id";}s:6:"length";a:0:{}}s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:17:"cart_items_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"cart_id";}s:10:"references";a:2:{i:0;s:5:"carts";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:17:"cart_items_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:17:"cart_items_ibfk_4";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:5:"carts";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:5:"carts";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"guest_token";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:11:"guest_token";s:6:"string";s:7:"created";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:12:"carts_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:10:"categories";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:10:"categories";s:11:" * _columns";a:23:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"url_key";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"category_icon";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"web_banner";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"mobile_banner";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"min_product_quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"parent_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"level";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:3:"lft";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:4:"rght";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"is_featured";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"display_order";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"meta_title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"meta_keyword";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"meta_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:23:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:7:"url_key";s:6:"string";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:11:"description";s:4:"text";s:14:"description_ar";s:4:"text";s:13:"category_icon";s:6:"string";s:10:"web_banner";s:6:"string";s:13:"mobile_banner";s:6:"string";s:20:"min_product_quantity";s:7:"integer";s:9:"parent_id";s:7:"integer";s:5:"level";s:7:"integer";s:3:"lft";s:7:"integer";s:4:"rght";s:7:"integer";s:11:"is_featured";s:7:"boolean";s:13:"display_order";s:11:"tinyinteger";s:10:"meta_title";s:6:"string";s:12:"meta_keyword";s:6:"string";s:16:"meta_description";s:4:"text";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:17:"categories_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:19:"category_attributes";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:19:"category_attributes";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:11:"category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"attribute_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"position";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:11:"category_id";s:7:"integer";s:12:"attribute_id";s:7:"integer";s:8:"position";s:7:"integer";s:6:"status";s:6:"string";}s:11:" * _indexes";a:2:{s:12:"attribute_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:12:"attribute_id";}s:6:"length";a:0:{}}s:11:"category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:26:"category_attributes_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:12:"attribute_id";}s:10:"references";a:2:{i:0;s:10:"attributes";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:26:"category_attributes_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:10:"references";a:2:{i:0;s:10:"categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:6:"cities";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:6:"cities";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"state_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"city_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"city_name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:8:"state_id";s:7:"integer";s:9:"city_name";s:6:"string";s:12:"city_name_ar";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:8:"state_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"state_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:13:"cities_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"state_id";}s:10:"references";a:2:{i:0;s:6:"states";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:11:"coming_soon";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:11:"coming_soon";s:11:" * _columns";a:13:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"product_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"product_name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"web_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"mob_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"start_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"end_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:13:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:12:"product_name";s:6:"string";s:15:"product_name_ar";s:6:"string";s:11:"description";s:4:"text";s:14:"description_ar";s:4:"text";s:9:"web_image";s:6:"string";s:9:"mob_image";s:6:"string";s:10:"start_date";s:4:"date";s:8:"end_date";s:4:"date";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:1:{s:22:"fk_coming_soon_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:22:"fk_coming_soon_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:17:"contact_inquiries";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:17:"contact_inquiries";s:11:" * _columns";a:11:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"first_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"last_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"email";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"phone";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"subject";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"message";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"privacy_consent";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:11:{s:2:"id";s:7:"integer";s:10:"first_name";s:6:"string";s:9:"last_name";s:6:"string";s:5:"email";s:6:"string";s:5:"phone";s:6:"string";s:7:"subject";s:6:"string";s:7:"message";s:4:"text";s:15:"privacy_consent";s:7:"boolean";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_0900_ai_ci";}s:13:" * _temporary";b:0;}s:19:"contact_query_types";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:19:"contact_query_types";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:7:"created";s:9:"timestamp";s:7:"updated";s:9:"timestamp";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:8:"contacts";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:8:"contacts";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"email";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"phone";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"inquiry_type";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"message";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_0900_ai_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:5:"email";s:6:"string";s:5:"phone";s:6:"string";s:12:"inquiry_type";s:6:"string";s:7:"message";s:4:"text";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_0900_ai_ci";}s:13:" * _temporary";b:0;}s:14:"content_images";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:14:"content_images";s:11:" * _columns";a:3:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:15:"content_page_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:3:{s:2:"id";s:7:"integer";s:15:"content_page_id";s:7:"integer";s:5:"image";s:6:"string";}s:11:" * _indexes";a:1:{s:15:"content_page_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:15:"content_page_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:21:"content_images_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:15:"content_page_id";}s:10:"references";a:2:{i:0;s:13:"content_pages";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:13:"content_pages";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:13:"content_pages";s:11:" * _columns";a:23:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:16:"content_category";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:27:"content_category_identifier";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"url_key";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"title_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:3:"tag";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:4294967295;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:4294967295;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"image_alt";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"video_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"content";a:8:{s:4:"type";s:4:"text";s:6:"length";i:4294967295;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"published";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:3:"Yes";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"published_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"meta_title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"meta_keyword";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"meta_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:10:"content_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:23:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:16:"content_category";s:6:"string";s:27:"content_category_identifier";s:6:"string";s:7:"url_key";s:6:"string";s:5:"title";s:6:"string";s:8:"title_ar";s:6:"string";s:3:"tag";s:6:"string";s:11:"description";s:4:"text";s:14:"description_ar";s:4:"text";s:5:"image";s:6:"string";s:9:"image_alt";s:6:"string";s:9:"video_url";s:6:"string";s:7:"content";s:4:"text";s:6:"status";s:6:"string";s:9:"published";s:6:"string";s:14:"published_date";s:4:"date";s:10:"meta_title";s:6:"string";s:12:"meta_keyword";s:6:"string";s:16:"meta_description";s:4:"text";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";s:10:"content_ar";s:4:"text";}s:11:" * _indexes";a:1:{s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:20:"content_pages_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:9:"countries";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:9:"countries";s:11:" * _columns";a:10:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"iso_code_2";a:8:{s:4:"type";s:4:"char";s:6:"length";i:2;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"iso_code_3";a:8:{s:4:"type";s:4:"char";s:6:"length";i:3;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"phone_code";a:8:{s:4:"type";s:6:"string";s:6:"length";i:10;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"address_format";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:10:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:10:"iso_code_2";s:4:"char";s:10:"iso_code_3";s:4:"char";s:10:"phone_code";s:6:"string";s:14:"address_format";s:4:"text";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:10:"currencies";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:10:"currencies";s:11:" * _columns";a:11:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:4:"code";a:8:{s:4:"type";s:6:"string";s:6:"length";i:3;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"symbol";a:8:{s:4:"type";s:6:"string";s:6:"length";i:10;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"symbol_position";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"value";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:15;s:9:"precision";i:6;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:8:"1.000000";s:7:"comment";s:0:"";s:8:"baseType";N;}s:14:"decimal_places";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:1:"2";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:11:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:4:"code";s:6:"string";s:4:"name";s:6:"string";s:6:"symbol";s:6:"string";s:15:"symbol_position";s:6:"string";s:5:"value";s:7:"decimal";s:14:"decimal_places";s:7:"integer";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:1:{s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:4:"code";a:3:{s:4:"type";s:6:"unique";s:7:"columns";a:1:{i:0;s:4:"code";}s:6:"length";a:0:{}}s:17:"currencies_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:18:"customer_addresses";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:18:"customer_addresses";s:11:" * _columns";a:22:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:4:"Home";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"address_line1";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"address_line2";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"house_no";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"landmark";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"state_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"city_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"zipcode";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"latitude";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:8;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:9:"longitude";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:11;s:9:"precision";i:8;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:13:"country_code1";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"phone_no1";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"country_code2";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"phone_no2";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:22:{s:2:"id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:5:"title";s:6:"string";s:4:"name";s:6:"string";s:4:"type";s:6:"string";s:13:"address_line1";s:6:"string";s:13:"address_line2";s:6:"string";s:8:"house_no";s:6:"string";s:10:"country_id";s:7:"integer";s:8:"landmark";s:6:"string";s:8:"state_id";s:7:"integer";s:7:"city_id";s:7:"integer";s:7:"zipcode";s:6:"string";s:8:"latitude";s:7:"decimal";s:9:"longitude";s:7:"decimal";s:13:"country_code1";s:6:"string";s:9:"phone_no1";s:6:"string";s:13:"country_code2";s:6:"string";s:9:"phone_no2";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:4:{s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}s:8:"state_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"state_id";}s:6:"length";a:0:{}}s:7:"city_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:6:"length";a:0:{}}s:25:"customer_addresses_ibfk_4";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:5:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:25:"customer_addresses_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:25:"customer_addresses_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"state_id";}s:10:"references";a:2:{i:0;s:6:"states";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:25:"customer_addresses_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:10:"references";a:2:{i:0;s:6:"cities";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:25:"customer_addresses_ibfk_4";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:23:"customer_group_mappings";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:23:"customer_group_mappings";s:11:" * _columns";a:6:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:17:"customer_group_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:6:{s:2:"id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:17:"customer_group_id";s:7:"integer";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:2:{s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}s:17:"customer_group_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:17:"customer_group_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:30:"customer_group_mappings_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:30:"customer_group_mappings_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:17:"customer_group_id";}s:10:"references";a:2:{i:0;s:15:"customer_groups";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:15:"customer_groups";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:15:"customer_groups";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:11:"description";s:4:"text";s:14:"description_ar";s:4:"text";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:9:"customers";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:9:"customers";s:11:" * _columns";a:18:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"user_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"country_code";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"phone_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"profile_photo";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"date_of_birth";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"gender";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"platform";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:0;s:7:"default";s:6:"normal";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"push_notification";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"1";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"fcm_token";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"device_type";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"device_token";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"apple_user_token";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"udid";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"app_version";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"model_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:18:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:7:"user_id";s:7:"integer";s:12:"country_code";s:6:"string";s:12:"phone_number";s:6:"string";s:13:"profile_photo";s:6:"string";s:13:"date_of_birth";s:4:"date";s:6:"gender";s:6:"string";s:8:"platform";s:6:"string";s:17:"push_notification";s:7:"boolean";s:9:"fcm_token";s:6:"string";s:11:"device_type";s:6:"string";s:12:"device_token";s:6:"string";s:16:"apple_user_token";s:6:"string";s:4:"udid";s:6:"string";s:11:"app_version";s:6:"string";s:10:"model_name";s:6:"string";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:7:"user_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"user_id";}s:6:"length";a:0:{}}s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:16:"customers_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"user_id";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:16:"customers_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:16:"delivery_charges";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:16:"delivery_charges";s:11:" * _columns";a:11:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"city_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:13:"delivery_mode";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"product_size";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:57:"('Very Small', 'Small', 'Medium', 'Large', 'Extra Large')";s:8:"baseType";N;s:9:"precision";N;}s:6:"weight";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"charge";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:11:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:7:"city_id";s:7:"integer";s:13:"delivery_mode";s:6:"string";s:4:"type";s:6:"string";s:12:"product_size";s:6:"string";s:6:"weight";s:6:"string";s:6:"charge";s:7:"decimal";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:2:{s:23:"delivery_charges_ibfk_1";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:6:"length";a:0:{}}s:27:"fk_delivery_charges_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:23:"delivery_charges_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:10:"references";a:2:{i:0;s:6:"cities";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:27:"fk_delivery_charges_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:8:"invoices";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:8:"invoices";s:11:" * _columns";a:10:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:14:"invoice_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"pdf_link";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"invoice_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:12:"total_amount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:6:"Unpaid";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"created_at";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:10:"updated_at";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:10:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:8:"order_id";s:7:"integer";s:14:"invoice_number";s:6:"string";s:8:"pdf_link";s:6:"string";s:12:"invoice_date";s:8:"datetime";s:12:"total_amount";s:7:"decimal";s:6:"status";s:6:"string";s:10:"created_at";s:9:"timestamp";s:10:"updated_at";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:8:"order_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}s:19:"fk_invoices_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:19:"fk_invoices_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:15:"invoices_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:7:"modules";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:7:"modules";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:9:"parent_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"display_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:250;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"display_order";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:9:"parent_id";s:7:"integer";s:12:"display_name";s:6:"string";s:4:"name";s:6:"string";s:13:"display_order";s:11:"tinyinteger";s:7:"created";s:9:"timestamp";s:7:"updated";s:9:"timestamp";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:13:"notifications";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:13:"notifications";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:12:"from_user_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"to_user_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"is_read";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"read_at";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:10:"created_at";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:7:"integer";s:12:"from_user_id";s:7:"integer";s:10:"to_user_id";s:7:"integer";s:5:"title";s:6:"string";s:11:"description";s:4:"text";s:7:"is_read";s:7:"boolean";s:7:"read_at";s:9:"timestamp";s:10:"created_at";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:10:"to_user_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"to_user_id";}s:6:"length";a:0:{}}s:12:"from_user_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:12:"from_user_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:20:"notifications_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"to_user_id";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:20:"notifications_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:12:"from_user_id";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:16:"offer_categories";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:16:"offer_categories";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"offer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"level";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:8:"offer_id";s:7:"integer";s:11:"category_id";s:7:"integer";s:5:"level";s:7:"integer";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:11:"category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:6:"length";a:0:{}}s:11:"fk_offer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"offer_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:11:"fk_offer_id";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"offer_id";}s:10:"references";a:2:{i:0;s:6:"offers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:23:"offer_categories_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:10:"references";a:2:{i:0;s:10:"categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:21:"offer_customer_groups";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:21:"offer_customer_groups";s:11:" * _columns";a:6:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"offer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:17:"customer_group_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:6:{s:2:"id";s:7:"integer";s:8:"offer_id";s:7:"integer";s:17:"customer_group_id";s:7:"integer";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:2:{s:8:"fk_offer";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"offer_id";}s:6:"length";a:0:{}}s:17:"fk_customer_group";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:17:"customer_group_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:17:"fk_customer_group";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:17:"customer_group_id";}s:10:"references";a:2:{i:0;s:15:"customer_groups";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:8:"fk_offer";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"offer_id";}s:10:"references";a:2:{i:0;s:6:"offers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:6:"offers";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:6:"offers";s:11:" * _columns";a:28:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"merchant_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"url_key";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"offer_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"offer_name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"offer_group";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"offer_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"redeem_mode";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"offer_code";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"offer_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"offer_description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"web_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"mobile_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"discount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:12:"max_discount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:22:"max_amt_per_disc_value";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:14:"min_cart_value";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:4:"0.00";s:7:"comment";s:0:"";s:8:"baseType";N;}s:13:"free_shipping";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:16:"terms_conditions";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"offer_start_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:14:"offer_end_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:10:"meta_title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"meta_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"meta_keyword";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:28:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:11:"merchant_id";s:7:"integer";s:7:"url_key";s:6:"string";s:10:"offer_name";s:6:"string";s:13:"offer_name_ar";s:6:"string";s:11:"offer_group";s:6:"string";s:10:"offer_type";s:6:"string";s:11:"redeem_mode";s:6:"string";s:10:"offer_code";s:6:"string";s:17:"offer_description";s:4:"text";s:20:"offer_description_ar";s:4:"text";s:9:"web_image";s:6:"string";s:12:"mobile_image";s:6:"string";s:8:"discount";s:7:"decimal";s:12:"max_discount";s:7:"decimal";s:22:"max_amt_per_disc_value";s:7:"decimal";s:14:"min_cart_value";s:7:"decimal";s:13:"free_shipping";s:11:"tinyinteger";s:16:"terms_conditions";s:4:"text";s:16:"offer_start_date";s:8:"datetime";s:14:"offer_end_date";s:8:"datetime";s:10:"meta_title";s:6:"string";s:16:"meta_description";s:4:"text";s:12:"meta_keyword";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:11:"merchant_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"merchant_id";}s:6:"length";a:0:{}}s:17:"fk_offers_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:17:"fk_offers_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:13:"offers_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"merchant_id";}s:10:"references";a:2:{i:0;s:9:"merchants";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:29:"order_cancellation_categories";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:29:"order_cancellation_categories";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:7:"created";s:9:"timestamp";s:7:"updated";s:9:"timestamp";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:19:"order_cancellations";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:19:"order_cancellations";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:13:"order_item_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:30:"order_cancellation_category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"reason";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"canceled_at";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:8:"order_id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:13:"order_item_id";s:7:"integer";s:30:"order_cancellation_category_id";s:7:"integer";s:6:"reason";s:6:"string";s:6:"status";s:6:"string";s:11:"canceled_at";s:8:"datetime";s:7:"updated";s:9:"timestamp";}s:11:" * _indexes";a:4:{s:13:"order_item_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:6:"length";a:0:{}}s:8:"order_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}s:30:"order_cancellation_category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:30:"order_cancellation_category_id";}s:6:"length";a:0:{}}s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:5:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:26:"order_cancellations_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:10:"references";a:2:{i:0;s:11:"order_items";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"setNull";s:6:"length";a:0:{}}s:26:"order_cancellations_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:26:"order_cancellations_ibfk_4";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:30:"order_cancellation_category_id";}s:10:"references";a:2:{i:0;s:29:"order_cancellation_categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:26:"order_cancellations_ibfk_5";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:21:"order_item_attributes";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:21:"order_item_attributes";s:11:" * _columns";a:6:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:13:"order_item_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:20:"product_attribute_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:6:{s:2:"id";s:7:"integer";s:13:"order_item_id";s:7:"integer";s:20:"product_attribute_id";s:7:"integer";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:2:{s:13:"order_item_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:6:"length";a:0:{}}s:20:"product_attribute_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:20:"product_attribute_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:28:"order_item_attributes_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:10:"references";a:2:{i:0;s:11:"order_items";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:28:"order_item_attributes_ibfk_4";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:20:"product_attribute_id";}s:10:"references";a:2:{i:0;s:18:"product_attributes";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:11:"order_items";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:11:"order_items";s:11:" * _columns";a:16:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:18:"product_variant_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:11:"total_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"stock_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"quantity_required";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:14:"restock_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:21:"expected_restock_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"delivery_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:7:"pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"delivery_status_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"comments";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"proof_of_delivery";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:16:{s:2:"id";s:7:"integer";s:8:"order_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:18:"product_variant_id";s:7:"integer";s:8:"quantity";s:7:"integer";s:5:"price";s:7:"decimal";s:11:"total_price";s:7:"decimal";s:6:"status";s:6:"string";s:10:"stock_type";s:6:"string";s:17:"quantity_required";s:7:"integer";s:14:"restock_status";s:6:"string";s:21:"expected_restock_date";s:4:"date";s:15:"delivery_status";s:6:"string";s:20:"delivery_status_date";s:8:"datetime";s:8:"comments";s:4:"text";s:17:"proof_of_delivery";s:6:"string";}s:11:" * _indexes";a:3:{s:8:"order_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:18:"product_variant_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:18:"product_variant_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:18:"order_items_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:18:"order_items_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:18:"order_items_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:18:"product_variant_id";}s:10:"references";a:2:{i:0;s:16:"product_variants";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:23:"order_return_categories";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:23:"order_return_categories";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:7:"created";s:9:"timestamp";s:7:"updated";s:9:"timestamp";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:19:"order_return_images";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:19:"order_return_images";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:15:"order_return_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"image_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:15:"order_return_id";s:7:"integer";s:9:"image_url";s:6:"string";s:7:"created";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:9:"review_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:15:"order_return_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:13:"order_returns";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:13:"order_returns";s:11:" * _columns";a:12:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:13:"order_item_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:24:"order_return_category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"reason";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"requested_at";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:12:"processed_at";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:13:"return_amount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:12:{s:2:"id";s:7:"integer";s:8:"order_id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:13:"order_item_id";s:7:"integer";s:24:"order_return_category_id";s:7:"integer";s:6:"reason";s:6:"string";s:6:"status";s:6:"string";s:12:"requested_at";s:8:"datetime";s:12:"processed_at";s:8:"datetime";s:13:"return_amount";s:7:"decimal";s:7:"created";s:9:"timestamp";s:7:"updated";s:9:"timestamp";}s:11:" * _indexes";a:4:{s:13:"order_item_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:6:"length";a:0:{}}s:24:"order_return_category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:24:"order_return_category_id";}s:6:"length";a:0:{}}s:8:"order_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:5:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:20:"order_returns_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:10:"references";a:2:{i:0;s:11:"order_items";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:20:"order_returns_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:24:"order_return_category_id";}s:10:"references";a:2:{i:0;s:23:"order_return_categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:20:"order_returns_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:20:"order_returns_ibfk_4";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:24:"order_tracking_histories";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:24:"order_tracking_histories";s:11:" * _columns";a:6:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:13:"order_item_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"comment";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:6:{s:2:"id";s:7:"integer";s:8:"order_id";s:7:"integer";s:13:"order_item_id";s:7:"integer";s:6:"status";s:6:"string";s:7:"comment";s:6:"string";s:7:"updated";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:13:"order_item_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:6:"length";a:0:{}}s:8:"order_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:31:"order_tracking_histories_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:10:"references";a:2:{i:0;s:11:"order_items";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:31:"order_tracking_histories_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:6:"orders";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:6:"orders";s:11:" * _columns";a:31:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:19:"customer_address_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"city_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"offer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"order_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"order_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:12:"order status";s:8:"baseType";N;s:9:"precision";N;}s:11:"status_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:20:"current status date ";s:8:"baseType";N;s:8:"onUpdate";N;}s:15:"shipment_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:10:"Unassigned";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"payment_method";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"cheque_photo";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"shipping_method_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"subtotal_amount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:15:"delivery_charge";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:4:"0.00";s:7:"comment";s:0:"";s:8:"baseType";N;}s:15:"discount_amount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:4:"0.00";s:7:"comment";s:0:"";s:8:"baseType";N;}s:12:"offer_amount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:4:"0.00";s:7:"comment";s:0:"";s:8:"baseType";N;}s:12:"total_amount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:13:"delivery_mode";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"delivery_mode_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"order_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:19:"order_online_source";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:22:"order_fulfillment_time";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:13:"delivery_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:22:"Expected delivery date";s:8:"baseType";N;s:8:"onUpdate";N;}s:10:"stock_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"created_by";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:36:"logged in user id, who created order";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"created_by_role";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"last_modified_by";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:31:{s:2:"id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:19:"customer_address_id";s:7:"integer";s:7:"city_id";s:7:"integer";s:10:"country_id";s:7:"integer";s:8:"offer_id";s:7:"integer";s:12:"order_number";s:6:"string";s:10:"order_date";s:8:"datetime";s:6:"status";s:6:"string";s:11:"status_date";s:8:"datetime";s:15:"shipment_status";s:6:"string";s:14:"payment_method";s:6:"string";s:12:"cheque_photo";s:6:"string";s:18:"shipping_method_id";s:7:"integer";s:15:"subtotal_amount";s:7:"decimal";s:15:"delivery_charge";s:7:"decimal";s:15:"discount_amount";s:7:"decimal";s:12:"offer_amount";s:7:"decimal";s:12:"total_amount";s:7:"decimal";s:13:"delivery_mode";s:6:"string";s:18:"delivery_mode_type";s:6:"string";s:10:"order_type";s:6:"string";s:19:"order_online_source";s:6:"string";s:22:"order_fulfillment_time";s:8:"datetime";s:13:"delivery_date";s:8:"datetime";s:10:"stock_type";s:6:"string";s:10:"created_by";s:7:"integer";s:15:"created_by_role";s:6:"string";s:16:"last_modified_by";s:7:"integer";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:8:{s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}s:19:"customer_address_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:19:"customer_address_id";}s:6:"length";a:0:{}}s:8:"offer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"offer_id";}s:6:"length";a:0:{}}s:13:"orders_ibfk_5";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:18:"shipping_method_id";}s:6:"length";a:0:{}}s:10:"created_by";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"created_by";}s:6:"length";a:0:{}}s:16:"last_modified_by";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:16:"last_modified_by";}s:6:"length";a:0:{}}s:7:"city_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:6:"length";a:0:{}}s:13:"orders_ibfk_9";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:9:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:13:"orders_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:14:"orders_ibfk_10";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:10:"references";a:2:{i:0;s:6:"cities";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:13:"orders_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:19:"customer_address_id";}s:10:"references";a:2:{i:0;s:18:"customer_addresses";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:13:"orders_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"offer_id";}s:10:"references";a:2:{i:0;s:6:"offers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:13:"orders_ibfk_5";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:18:"shipping_method_id";}s:10:"references";a:2:{i:0;s:16:"shipping_methods";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:13:"orders_ibfk_7";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"created_by";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:13:"orders_ibfk_8";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:16:"last_modified_by";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:13:"orders_ibfk_9";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:17:"otp_verifications";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:17:"otp_verifications";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:15:"user_identifier";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:3:"otp";a:8:{s:4:"type";s:6:"string";s:6:"length";i:6;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"otp_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"created_at";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:10:"expires_at";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"is_used";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:15:"user_identifier";s:6:"string";s:3:"otp";s:6:"string";s:8:"otp_type";s:6:"string";s:10:"created_at";s:8:"datetime";s:10:"expires_at";s:8:"datetime";s:7:"is_used";s:7:"boolean";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:15:"user_identifier";a:3:{s:4:"type";s:6:"unique";s:7:"columns";a:3:{i:0;s:15:"user_identifier";i:1;s:8:"otp_type";i:2;s:3:"otp";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:23:"payment_method_settings";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:23:"payment_method_settings";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:17:"payment_method_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"attribute";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"value";a:8:{s:4:"type";s:6:"string";s:6:"length";i:500;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:17:"payment_method_id";s:7:"integer";s:9:"attribute";s:6:"string";s:5:"value";s:6:"string";}s:11:" * _indexes";a:1:{s:17:"payment_method_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:17:"payment_method_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:30:"payment_method_settings_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:17:"payment_method_id";}s:10:"references";a:2:{i:0;s:15:"payment_methods";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:15:"payment_methods";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:15:"payment_methods";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"description_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:11:"description";s:6:"string";s:14:"description_ar";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:26:"fk_payment_methods_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:26:"fk_payment_methods_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:11:"permissions";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:11:"permissions";s:11:" * _columns";a:11:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:7:"role_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"module_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"can_view";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"can_create";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"can_edit";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"can_delete";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"can_approve";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"store_based";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:11:{s:2:"id";s:7:"integer";s:7:"role_id";s:7:"integer";s:9:"module_id";s:7:"integer";s:8:"can_view";s:7:"boolean";s:10:"can_create";s:7:"boolean";s:8:"can_edit";s:7:"boolean";s:10:"can_delete";s:7:"boolean";s:11:"can_approve";s:7:"boolean";s:11:"store_based";s:7:"boolean";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:7:"role_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"role_id";}s:6:"length";a:0:{}}s:9:"module_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:9:"module_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:18:"permissions_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"role_id";}s:10:"references";a:2:{i:0;s:5:"roles";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:18:"permissions_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:9:"module_id";}s:10:"references";a:2:{i:0;s:7:"modules";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:18:"product_attributes";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:18:"product_attributes";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"attribute_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:18:"attribute_value_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:21:"attribute_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:24:"attribute_description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:10:"product_id";s:7:"integer";s:12:"attribute_id";s:7:"integer";s:18:"attribute_value_id";s:7:"integer";s:21:"attribute_description";s:4:"text";s:24:"attribute_description_ar";s:4:"text";s:6:"status";s:6:"string";}s:11:" * _indexes";a:3:{s:12:"attribute_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:12:"attribute_id";}s:6:"length";a:0:{}}s:18:"attribute_value_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:18:"attribute_value_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:25:"product_attributes_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:12:"attribute_id";}s:10:"references";a:2:{i:0;s:10:"attributes";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:25:"product_attributes_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:18:"attribute_value_id";}s:10:"references";a:2:{i:0;s:16:"attribute_values";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:25:"product_attributes_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:18:"product_categories";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:18:"product_categories";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"level";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:10:"product_id";s:7:"integer";s:11:"category_id";s:7:"integer";s:5:"level";s:7:"integer";}s:11:" * _indexes";a:2:{s:11:"category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:25:"product_categories_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:10:"references";a:2:{i:0;s:10:"categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:25:"product_categories_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:14:"product_images";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:14:"product_images";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"media_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:5:"Image";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"image_default";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"video";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:10:"product_id";s:7:"integer";s:10:"media_type";s:6:"string";s:5:"image";s:6:"string";s:13:"image_default";s:11:"tinyinteger";s:5:"video";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:21:"product_images_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:24:"product_payment_settings";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:24:"product_payment_settings";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"partner_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:13:"avl_on_credit";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"credit_terms";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:10:"product_id";s:7:"integer";s:10:"partner_id";s:7:"integer";s:8:"quantity";s:7:"integer";s:13:"avl_on_credit";s:7:"boolean";s:12:"credit_terms";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";}s:11:" * _indexes";a:2:{s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:10:"partner_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"partner_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:31:"product_payment_settings_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:31:"product_payment_settings_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"partner_id";}s:10:"references";a:2:{i:0;s:8:"partners";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:14:"product_stocks";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:14:"product_stocks";s:11:" * _columns";a:10:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:18:"product_variant_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:20:"product_attribute_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:15:"total qty/stock";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:14:"reserved_stock";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"defective_stock";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:20:"service_center_stock";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"purchased_stock";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"last_updated";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:10:{s:2:"id";s:7:"integer";s:10:"product_id";s:7:"integer";s:18:"product_variant_id";s:7:"integer";s:20:"product_attribute_id";s:7:"integer";s:8:"quantity";s:7:"integer";s:14:"reserved_stock";s:7:"integer";s:15:"defective_stock";s:7:"integer";s:20:"service_center_stock";s:7:"integer";s:15:"purchased_stock";s:7:"integer";s:12:"last_updated";s:9:"timestamp";}s:11:" * _indexes";a:3:{s:21:"product_stocks_ibfk_1";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:18:"product_variant_id";}s:6:"length";a:0:{}}s:21:"product_stocks_ibfk_2";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:20:"product_attribute_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:21:"product_stocks_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:18:"product_variant_id";}s:10:"references";a:2:{i:0;s:16:"product_variants";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:21:"product_stocks_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:20:"product_attribute_id";}s:10:"references";a:2:{i:0;s:18:"product_attributes";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:21:"product_stocks_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:22:"product_variant_images";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:22:"product_variant_images";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"variant_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"media_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:5:"Image";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"image_default";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"video";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:10:"variant_id";s:7:"integer";s:10:"media_type";s:6:"string";s:5:"image";s:6:"string";s:13:"image_default";s:11:"tinyinteger";s:5:"video";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:10:"variant_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"variant_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:29:"product_variant_images_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"variant_id";}s:10:"references";a:2:{i:0;s:16:"product_variants";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:16:"product_variants";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:16:"product_variants";s:11:" * _columns";a:18:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"variant_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"variant_name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"reference_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:3:"sku";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"variant_size";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"variant_weight";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:11:"supplier_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:14:"purchase_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:11:"sales_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:15:"promotion_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:19:"variant_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:22:"variant_description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:18:{s:2:"id";s:7:"integer";s:10:"product_id";s:7:"integer";s:12:"variant_name";s:6:"string";s:15:"variant_name_ar";s:6:"string";s:14:"reference_name";s:6:"string";s:3:"sku";s:6:"string";s:12:"variant_size";s:6:"string";s:14:"variant_weight";s:7:"decimal";s:11:"supplier_id";s:7:"integer";s:14:"purchase_price";s:7:"decimal";s:11:"sales_price";s:7:"decimal";s:15:"promotion_price";s:7:"decimal";s:8:"quantity";s:7:"integer";s:19:"variant_description";s:4:"text";s:22:"variant_description_ar";s:4:"text";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:31:"fk_product_variants_supplier_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"supplier_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:31:"fk_product_variants_supplier_id";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"supplier_id";}s:10:"references";a:2:{i:0;s:9:"suppliers";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:23:"product_variants_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:8:"products";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:8:"products";s:11:" * _columns";a:46:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:7:"url_key";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"merchant_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"brand_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"product_reference";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"reference_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"description_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"details";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"details_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"features";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"features_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"product_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"product_preference";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"product_size";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"product_weight";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:13:"product_model";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"product_tags";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:3:"sku";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"barcode";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"qrcode";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"scanned_barcode";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:14:"purchase_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:13:"product_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:11:"sales_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:15:"promotion_price";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:20:"promotion_start_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"promotion_end_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"max_buy_limit";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"COD_in_city";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"COD_out_city";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:13:"avl_on_credit";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"return_allow";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"return_time_period";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"meta_title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"meta_keyword";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"meta_description";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:15:"approval_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:9:"catalogue";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:46:{s:2:"id";s:7:"integer";s:7:"url_key";s:6:"string";s:10:"country_id";s:7:"integer";s:11:"merchant_id";s:7:"integer";s:8:"brand_id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:17:"product_reference";s:6:"string";s:14:"reference_name";s:6:"string";s:11:"description";s:4:"text";s:14:"description_ar";s:4:"text";s:7:"details";s:4:"text";s:10:"details_ar";s:4:"text";s:8:"features";s:4:"text";s:11:"features_ar";s:4:"text";s:13:"product_image";s:6:"string";s:18:"product_preference";s:6:"string";s:12:"product_size";s:6:"string";s:14:"product_weight";s:7:"decimal";s:13:"product_model";s:6:"string";s:12:"product_tags";s:6:"string";s:3:"sku";s:6:"string";s:7:"barcode";s:6:"string";s:6:"qrcode";s:6:"string";s:15:"scanned_barcode";s:6:"string";s:8:"quantity";s:7:"integer";s:14:"purchase_price";s:7:"decimal";s:13:"product_price";s:7:"decimal";s:11:"sales_price";s:7:"decimal";s:15:"promotion_price";s:7:"decimal";s:20:"promotion_start_date";s:4:"date";s:18:"promotion_end_date";s:4:"date";s:13:"max_buy_limit";s:7:"integer";s:11:"COD_in_city";s:11:"tinyinteger";s:12:"COD_out_city";s:11:"tinyinteger";s:13:"avl_on_credit";s:11:"tinyinteger";s:12:"return_allow";s:7:"boolean";s:18:"return_time_period";s:7:"integer";s:10:"meta_title";s:6:"string";s:12:"meta_keyword";s:6:"string";s:16:"meta_description";s:4:"text";s:6:"status";s:6:"string";s:15:"approval_status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";s:9:"catalogue";s:6:"string";}s:11:" * _indexes";a:3:{s:8:"brand_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"brand_id";}s:6:"length";a:0:{}}s:11:"merchant_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"merchant_id";}s:6:"length";a:0:{}}s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:15:"products_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"brand_id";}s:10:"references";a:2:{i:0;s:6:"brands";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:15:"products_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"merchant_id";}s:10:"references";a:2:{i:0;s:9:"merchants";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:15:"products_ibfk_4";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:15:"queue_processes";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:15:"queue_processes";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:3:"pid";a:8:{s:4:"type";s:6:"string";s:6:"length";i:40;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:9:"terminate";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"server";a:8:{s:4:"type";s:6:"string";s:6:"length";i:90;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"workerkey";a:8:{s:4:"type";s:6:"string";s:6:"length";i:45;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:3:"pid";s:6:"string";s:7:"created";s:8:"datetime";s:8:"modified";s:8:"datetime";s:9:"terminate";s:7:"boolean";s:6:"server";s:6:"string";s:9:"workerkey";s:6:"string";}s:11:" * _indexes";a:0:{}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:9:"workerkey";a:3:{s:4:"type";s:6:"unique";s:7:"columns";a:1:{i:0;s:9:"workerkey";}s:6:"length";a:0:{}}s:3:"pid";a:3:{s:4:"type";s:6:"unique";s:7:"columns";a:2:{i:0;s:3:"pid";i:1;s:6:"server";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:11:"queued_jobs";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:11:"queued_jobs";s:11:" * _columns";a:15:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"job_task";a:8:{s:4:"type";s:6:"string";s:6:"length";i:90;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"data";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"job_group";a:8:{s:4:"type";s:6:"string";s:6:"length";i:190;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"reference";a:8:{s:4:"type";s:6:"string";s:6:"length";i:190;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:9:"notbefore";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"fetched";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:9:"completed";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"progress";a:8:{s:4:"type";s:5:"float";s:6:"length";N;s:9:"precision";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:8:"attempts";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"failure_message";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"workerkey";a:8:{s:4:"type";s:6:"string";s:6:"length";i:45;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";i:190;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"priority";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";s:1:"5";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}}s:11:" * _typeMap";a:15:{s:2:"id";s:7:"integer";s:8:"job_task";s:6:"string";s:4:"data";s:4:"text";s:9:"job_group";s:6:"string";s:9:"reference";s:6:"string";s:7:"created";s:8:"datetime";s:9:"notbefore";s:8:"datetime";s:7:"fetched";s:8:"datetime";s:9:"completed";s:8:"datetime";s:8:"progress";s:5:"float";s:8:"attempts";s:11:"tinyinteger";s:15:"failure_message";s:4:"text";s:9:"workerkey";s:6:"string";s:6:"status";s:6:"string";s:8:"priority";s:7:"integer";}s:11:" * _indexes";a:2:{s:9:"completed";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:9:"completed";}s:6:"length";a:0:{}}s:8:"job_task";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"job_task";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:1:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:24:"recently_viewed_products";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:24:"recently_viewed_products";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"user_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"viewed_at";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:7:"user_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:9:"viewed_at";s:8:"datetime";}s:11:" * _indexes";a:2:{s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:35:"fk_recently_viewed_products_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:5:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:19:"unique_user_product";a:3:{s:4:"type";s:6:"unique";s:7:"columns";a:2:{i:0;s:7:"user_id";i:1;s:10:"product_id";}s:6:"length";a:0:{}}s:35:"fk_recently_viewed_products_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:31:"recently_viewed_products_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"user_id";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:31:"recently_viewed_products_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:16:"related_products";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:16:"related_products";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"related_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:10:"related_id";s:7:"integer";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:3:{s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:10:"related_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"related_id";}s:6:"length";a:0:{}}s:27:"fk_related_products_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:27:"fk_related_products_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:23:"related_products_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:23:"related_products_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"related_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:13:"review_images";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:13:"review_images";s:11:" * _columns";a:4:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:9:"review_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"image_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:4:{s:2:"id";s:7:"integer";s:9:"review_id";s:7:"integer";s:9:"image_url";s:6:"string";s:7:"created";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:9:"review_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:9:"review_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:20:"review_images_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:9:"review_id";}s:10:"references";a:2:{i:0;s:7:"reviews";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:7:"reviews";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:7:"reviews";s:11:" * _columns";a:9:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"rating";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"comment";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:9:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:6:"rating";s:7:"integer";s:7:"comment";s:4:"text";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:3:{s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}s:18:"fk_reviews_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:18:"fk_reviews_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:14:"reviews_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:14:"reviews_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:5:"roles";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:5:"roles";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:4:"name";s:6:"string";s:11:"description";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:16:"fk_roles_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:16:"fk_roles_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:20:"shipment_order_items";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:20:"shipment_order_items";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:10:"biginteger";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:17:"shipment_order_id";a:9:{s:4:"type";s:10:"biginteger";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:13:"order_item_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"quantity";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:20:"item_delivery_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"delivery_status_date";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:15:"driver_comments";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"proof_of_delivery";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:10:"biginteger";s:17:"shipment_order_id";s:10:"biginteger";s:13:"order_item_id";s:7:"integer";s:8:"quantity";s:7:"integer";s:20:"item_delivery_status";s:6:"string";s:20:"delivery_status_date";s:9:"timestamp";s:15:"driver_comments";s:4:"text";s:17:"proof_of_delivery";s:6:"string";}s:11:" * _indexes";a:2:{s:38:"fk_shipment_order_items_shipment_order";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:17:"shipment_order_id";}s:6:"length";a:0:{}}s:34:"fk_shipment_order_items_order_item";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:34:"fk_shipment_order_items_order_item";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:13:"order_item_id";}s:10:"references";a:2:{i:0;s:11:"order_items";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:38:"fk_shipment_order_items_shipment_order";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:17:"shipment_order_id";}s:10:"references";a:2:{i:0;s:15:"shipment_orders";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:15:"shipment_orders";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:15:"shipment_orders";s:11:" * _columns";a:20:{s:2:"id";a:9:{s:4:"type";s:10:"biginteger";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:11:"shipment_id";a:9:{s:4:"type";s:10:"biginteger";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"driver_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:19:"customer_address_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"city_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"zone_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"municipality_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:22:"expected_delivery_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"actual_delivery_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"delivery_attempts";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"is_expedited";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"shipping_cost";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:4:"0.00";s:7:"comment";s:0:"";s:8:"baseType";N;}s:21:"order_delivery_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:13:"Not_Attempted";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"delivery_status_date";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:14:"cash_collected";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:4:"0.00";s:7:"comment";s:0:"";s:8:"baseType";N;}s:17:"proof_of_delivery";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"driver_comments";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"special_instructions";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:20:{s:2:"id";s:10:"biginteger";s:11:"shipment_id";s:10:"biginteger";s:8:"order_id";s:7:"integer";s:9:"driver_id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:19:"customer_address_id";s:7:"integer";s:7:"city_id";s:7:"integer";s:7:"zone_id";s:7:"integer";s:15:"municipality_id";s:7:"integer";s:22:"expected_delivery_date";s:4:"date";s:20:"actual_delivery_date";s:4:"date";s:17:"delivery_attempts";s:7:"integer";s:12:"is_expedited";s:7:"boolean";s:13:"shipping_cost";s:7:"decimal";s:21:"order_delivery_status";s:6:"string";s:20:"delivery_status_date";s:9:"timestamp";s:14:"cash_collected";s:7:"decimal";s:17:"proof_of_delivery";s:6:"string";s:15:"driver_comments";s:4:"text";s:20:"special_instructions";s:4:"text";}s:11:" * _indexes";a:8:{s:27:"fk_shipment_orders_shipment";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"shipment_id";}s:6:"length";a:0:{}}s:24:"fk_shipment_orders_order";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}s:25:"fk_shipment_orders_driver";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:9:"driver_id";}s:6:"length";a:0:{}}s:27:"fk_shipment_orders_customer";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}s:35:"fk_shipment_orders_customer_address";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:19:"customer_address_id";}s:6:"length";a:0:{}}s:23:"fk_shipment_orders_city";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:6:"length";a:0:{}}s:23:"fk_shipment_orders_zone";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"zone_id";}s:6:"length";a:0:{}}s:31:"fk_shipment_orders_municipality";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:15:"municipality_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:9:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:23:"fk_shipment_orders_city";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"city_id";}s:10:"references";a:2:{i:0;s:6:"cities";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:27:"fk_shipment_orders_customer";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:35:"fk_shipment_orders_customer_address";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:19:"customer_address_id";}s:10:"references";a:2:{i:0;s:18:"customer_addresses";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:25:"fk_shipment_orders_driver";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:9:"driver_id";}s:10:"references";a:2:{i:0;s:7:"drivers";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"setNull";s:6:"length";a:0:{}}s:31:"fk_shipment_orders_municipality";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:15:"municipality_id";}s:10:"references";a:2:{i:0;s:14:"municipalities";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"setNull";s:6:"length";a:0:{}}s:24:"fk_shipment_orders_order";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:27:"fk_shipment_orders_shipment";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"shipment_id";}s:10:"references";a:2:{i:0;s:9:"shipments";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"cascade";s:6:"length";a:0:{}}s:23:"fk_shipment_orders_zone";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"zone_id";}s:10:"references";a:2:{i:0;s:5:"zones";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"setNull";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:9:"shipments";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:9:"shipments";s:11:" * _columns";a:15:{s:2:"id";a:9:{s:4:"type";s:10:"biginteger";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:15:"shipment_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"shipment_date";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:13:"delivery_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"driver_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:19:"delivery_partner_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"tracking_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"delivery_note_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"sender_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"senderID";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:15:"shipment_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:15:"delivery_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:11:"Not_Started";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"special_instructions";a:8:{s:4:"type";s:4:"text";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:15:{s:2:"id";s:10:"biginteger";s:15:"shipment_number";s:6:"string";s:13:"shipment_date";s:9:"timestamp";s:13:"delivery_type";s:6:"string";s:9:"driver_id";s:7:"integer";s:19:"delivery_partner_id";s:7:"integer";s:15:"tracking_number";s:6:"string";s:20:"delivery_note_number";s:6:"string";s:11:"sender_type";s:6:"string";s:8:"senderID";s:7:"integer";s:15:"shipment_status";s:6:"string";s:15:"delivery_status";s:6:"string";s:20:"special_instructions";s:4:"text";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:3:{s:19:"fk_shipments_driver";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:9:"driver_id";}s:6:"length";a:0:{}}s:29:"fk_shipments_delivery_partner";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:19:"delivery_partner_id";}s:6:"length";a:0:{}}s:8:"senderID";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"senderID";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:5:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:15:"shipment_number";a:3:{s:4:"type";s:6:"unique";s:7:"columns";a:1:{i:0;s:15:"shipment_number";}s:6:"length";a:0:{}}s:29:"fk_shipments_delivery_partner";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:19:"delivery_partner_id";}s:10:"references";a:2:{i:0;s:17:"delivery_partners";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"setNull";s:6:"length";a:0:{}}s:19:"fk_shipments_driver";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:9:"driver_id";}s:10:"references";a:2:{i:0;s:7:"drivers";i:1;s:2:"id";}s:6:"update";s:7:"cascade";s:6:"delete";s:7:"setNull";s:6:"length";a:0:{}}s:20:"shipments_NEW_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"senderID";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:16:"shipping_methods";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:16:"shipping_methods";s:11:" * _columns";a:8:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:4:"name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"description";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:8:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:4:"name";s:6:"string";s:7:"name_ar";s:6:"string";s:11:"description";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:27:"fk_shipping_methods_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:27:"fk_shipping_methods_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:13:"site_settings";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:13:"site_settings";s:11:" * _columns";a:30:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"site_title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"address_line1";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"address_line2";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"country";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"state";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:4:"city";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"zipcode";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:19:"customer_support_no";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"contact_no";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"support_email";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"admin_email";a:8:{s:4:"type";s:6:"string";s:6:"length";i:50;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"business_open_time";a:7:{s:4:"type";s:4:"time";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:19:"business_close_time";a:7:{s:4:"type";s:4:"time";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"company_logo";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"fav_icon";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"facebook_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"twitter_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"pinterest_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"youtube_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"instagram_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"linkedin_url";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"pagination_count";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:2:"10";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:22:"product_cancel_in_days";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"2";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:22:"product_return_in_days";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"2";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:34:"stock_request_auto_cancel_duration";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"2";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:34:"express_delivery_order_cutoff_time";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"5";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:30:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:10:"site_title";s:6:"string";s:13:"address_line1";s:6:"string";s:13:"address_line2";s:6:"string";s:7:"country";s:6:"string";s:5:"state";s:6:"string";s:4:"city";s:6:"string";s:7:"zipcode";s:6:"string";s:19:"customer_support_no";s:6:"string";s:10:"contact_no";s:6:"string";s:13:"support_email";s:6:"string";s:11:"admin_email";s:6:"string";s:18:"business_open_time";s:4:"time";s:19:"business_close_time";s:4:"time";s:12:"company_logo";s:6:"string";s:8:"fav_icon";s:6:"string";s:12:"facebook_url";s:6:"string";s:11:"twitter_url";s:6:"string";s:13:"pinterest_url";s:6:"string";s:11:"youtube_url";s:6:"string";s:13:"instagram_url";s:6:"string";s:12:"linkedin_url";s:6:"string";s:16:"pagination_count";s:7:"integer";s:22:"product_cancel_in_days";s:7:"integer";s:22:"product_return_in_days";s:7:"integer";s:34:"stock_request_auto_cancel_duration";s:7:"integer";s:34:"express_delivery_order_cutoff_time";s:7:"integer";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:24:"fk_site_settings_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:24:"fk_site_settings_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:11:"site_themes";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:11:"site_themes";s:11:" * _columns";a:10:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"theme_name";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"menu_color";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"header_color";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"footer_color";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"font_family";a:8:{s:4:"type";s:6:"string";s:6:"length";i:100;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"button_color";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:10:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:10:"theme_name";s:6:"string";s:10:"menu_color";s:6:"string";s:12:"header_color";s:6:"string";s:12:"footer_color";s:6:"string";s:11:"font_family";s:6:"string";s:12:"button_color";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:22:"fk_site_themes_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:22:"fk_site_themes_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:6:"states";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:6:"states";s:11:" * _columns";a:7:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"state_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"state_name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:7:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:10:"state_name";s:6:"string";s:13:"state_name_ar";s:6:"string";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:13:"states_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:12:"testimonials";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:12:"testimonials";s:11:" * _columns";a:13:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:12:"client_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"client_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"client_name_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"comments";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"comments_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:6:"rating";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"start_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"end_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:13:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:12:"client_image";s:6:"string";s:11:"client_name";s:6:"string";s:14:"client_name_ar";s:6:"string";s:8:"comments";s:4:"text";s:11:"comments_ar";s:4:"text";s:6:"rating";s:11:"tinyinteger";s:6:"status";s:6:"string";s:10:"start_date";s:4:"date";s:8:"end_date";s:4:"date";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:23:"fk_testimonials_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:23:"fk_testimonials_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:12:"transactions";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:12:"transactions";s:11:" * _columns";a:18:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:8:"order_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:14:"invoice_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:18:"transaction_number";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"transaction_date";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:6:"amount";a:8:{s:4:"type";s:7:"decimal";s:6:"length";i:10;s:9:"precision";i:2;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;}s:14:"payment_method";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"payment_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:7:"Pending";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"transactionID";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:58:"The ID generated by MoMo (financialTransactionId) and Wave";s:8:"baseType";N;s:9:"precision";N;}s:23:"wave_checkout_sessionID";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:20:"wave_checkout_status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:23:"wave_client_referenceID";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"momo_payerID";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:16:"customer's phone";s:8:"baseType";N;s:9:"precision";N;}s:16:"momo_referenceID";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:176:"The unique ID you generate.

This ID is used, for example, validating the status of the request. ‘Universal Unique ID’ for the transaction generated using UUID version 4.";s:8:"baseType";N;s:9:"precision";N;}s:15:"momo_externalID";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:12:"order number";s:8:"baseType";N;s:9:"precision";N;}s:6:"reason";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:18:{s:2:"id";s:7:"integer";s:8:"order_id";s:7:"integer";s:14:"invoice_number";s:6:"string";s:18:"transaction_number";s:6:"string";s:16:"transaction_date";s:8:"datetime";s:6:"amount";s:7:"decimal";s:14:"payment_method";s:6:"string";s:14:"payment_status";s:6:"string";s:13:"transactionID";s:6:"string";s:23:"wave_checkout_sessionID";s:6:"string";s:20:"wave_checkout_status";s:6:"string";s:23:"wave_client_referenceID";s:6:"string";s:12:"momo_payerID";s:6:"string";s:16:"momo_referenceID";s:6:"string";s:15:"momo_externalID";s:6:"string";s:6:"reason";s:4:"text";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:8:"order_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:19:"transactions_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:8:"order_id";}s:10:"references";a:2:{i:0;s:6:"orders";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:5:"users";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:5:"users";s:11:" * _columns";a:29:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"first_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"last_name";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"email";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"password";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"country_code";a:8:{s:4:"type";s:6:"string";s:6:"length";i:25;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"mobile_no";a:8:{s:4:"type";s:6:"string";s:6:"length";i:20;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"profile_pic";a:8:{s:4:"type";s:6:"string";s:6:"length";i:500;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"department";a:8:{s:4:"type";s:6:"string";s:6:"length";i:150;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"user_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"role_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"last_login";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:20:"password_reset_token";a:8:{s:4:"type";s:6:"string";s:6:"length";i:355;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"token_created_at";a:8:{s:4:"type";s:8:"datetime";s:6:"length";N;s:9:"precision";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:18:"email_verify_token";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"is_email_verified";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"token";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"created_by";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:19:"email_notifications";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"1";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:16:"sms_notification";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"1";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:17:"push_notification";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"1";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:17:"app_notifications";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";s:1:"1";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:9:"fcm_token";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:16:"deletion_comment";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"deleted_at";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:29:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:10:"first_name";s:6:"string";s:9:"last_name";s:6:"string";s:5:"email";s:6:"string";s:8:"password";s:6:"string";s:12:"country_code";s:6:"string";s:9:"mobile_no";s:6:"string";s:11:"profile_pic";s:6:"string";s:10:"department";s:6:"string";s:9:"user_type";s:6:"string";s:7:"role_id";s:7:"integer";s:10:"last_login";s:8:"datetime";s:6:"status";s:6:"string";s:20:"password_reset_token";s:6:"string";s:16:"token_created_at";s:8:"datetime";s:18:"email_verify_token";s:6:"string";s:17:"is_email_verified";s:7:"boolean";s:5:"token";s:6:"string";s:10:"created_by";s:7:"integer";s:19:"email_notifications";s:7:"integer";s:16:"sms_notification";s:11:"tinyinteger";s:17:"push_notification";s:11:"tinyinteger";s:17:"app_notifications";s:7:"integer";s:9:"fcm_token";s:4:"text";s:16:"deletion_comment";s:6:"string";s:10:"deleted_at";s:9:"timestamp";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:3:{s:7:"role_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:7:"role_id";}s:6:"length";a:0:{}}s:10:"created_by";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"created_by";}s:6:"length";a:0:{}}s:10:"country_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:4:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:12:"users_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:7:"role_id";}s:10:"references";a:2:{i:0;s:5:"roles";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:12:"users_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"created_by";}s:10:"references";a:2:{i:0;s:5:"users";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:12:"users_ibfk_3";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:24:"widget_category_mappings";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:24:"widget_category_mappings";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:9:"widget_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:11:"category_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:5:"level";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:9:"widget_id";s:7:"integer";s:11:"category_id";s:7:"integer";s:5:"level";s:7:"integer";s:6:"status";s:6:"string";}s:11:" * _indexes";a:2:{s:11:"category_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:6:"length";a:0:{}}s:9:"widget_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:9:"widget_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:31:"widget_category_mappings_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"category_id";}s:10:"references";a:2:{i:0;s:10:"categories";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:31:"widget_category_mappings_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:9:"widget_id";}s:10:"references";a:2:{i:0;s:7:"widgets";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:7:"widgets";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:7:"widgets";s:11:" * _columns";a:20:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:10:"country_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"url_key";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:5:"title";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"title_ar";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:7:"summary";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"summary_ar";a:8:{s:4:"type";s:4:"text";s:6:"length";i:16777215;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"no_of_product";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:18:"product_preference";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:11:"widget_type";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";N;s:7:"collate";s:18:"utf8mb3_general_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:14:"display_in_web";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:17:"display_in_mobile";a:7:{s:4:"type";s:7:"boolean";s:6:"length";N;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:9:"web_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:12:"mobile_image";a:8:{s:4:"type";s:6:"string";s:6:"length";i:255;s:4:"null";b:1;s:7:"default";N;s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:10:"start_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:8:"end_date";a:7:{s:4:"type";s:4:"date";s:6:"length";N;s:4:"null";b:1;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;}s:13:"display_order";a:9:{s:4:"type";s:11:"tinyinteger";s:6:"length";N;s:8:"unsigned";b:0;s:4:"null";b:1;s:7:"default";s:1:"0";s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:6:"status";a:8:{s:4:"type";s:6:"string";s:6:"length";N;s:4:"null";b:0;s:7:"default";s:1:"A";s:7:"collate";s:18:"utf8mb4_unicode_ci";s:7:"comment";s:35:"A=>Active, I=> Inactive, D=>Deleted";s:8:"baseType";N;s:9:"precision";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:20:{s:2:"id";s:7:"integer";s:10:"country_id";s:7:"integer";s:7:"url_key";s:6:"string";s:5:"title";s:6:"string";s:8:"title_ar";s:6:"string";s:7:"summary";s:4:"text";s:10:"summary_ar";s:4:"text";s:13:"no_of_product";s:11:"tinyinteger";s:18:"product_preference";s:6:"string";s:11:"widget_type";s:6:"string";s:14:"display_in_web";s:7:"boolean";s:17:"display_in_mobile";s:7:"boolean";s:9:"web_image";s:6:"string";s:12:"mobile_image";s:6:"string";s:10:"start_date";s:4:"date";s:8:"end_date";s:4:"date";s:13:"display_order";s:11:"tinyinteger";s:6:"status";s:6:"string";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:1:{s:18:"fk_widgets_country";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:2:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:18:"fk_widgets_country";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"country_id";}s:10:"references";a:2:{i:0;s:9:"countries";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}s:9:"wishlists";O:32:"Cake\Database\Schema\TableSchema":7:{s:9:" * _table";s:9:"wishlists";s:11:" * _columns";a:5:{s:2:"id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:13:"autoIncrement";b:1;s:8:"baseType";N;s:9:"precision";N;}s:11:"customer_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:10:"product_id";a:9:{s:4:"type";s:7:"integer";s:6:"length";N;s:8:"unsigned";b:1;s:4:"null";b:0;s:7:"default";N;s:7:"comment";s:0:"";s:8:"baseType";N;s:9:"precision";N;s:13:"autoIncrement";N;}s:7:"created";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}s:8:"modified";a:8:{s:4:"type";s:9:"timestamp";s:6:"length";N;s:9:"precision";N;s:4:"null";b:0;s:7:"default";s:17:"CURRENT_TIMESTAMP";s:7:"comment";s:0:"";s:8:"baseType";N;s:8:"onUpdate";N;}}s:11:" * _typeMap";a:5:{s:2:"id";s:7:"integer";s:11:"customer_id";s:7:"integer";s:10:"product_id";s:7:"integer";s:7:"created";s:9:"timestamp";s:8:"modified";s:9:"timestamp";}s:11:" * _indexes";a:2:{s:11:"customer_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:6:"length";a:0:{}}s:10:"product_id";a:3:{s:4:"type";s:5:"index";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:6:"length";a:0:{}}}s:15:" * _constraints";a:3:{s:7:"primary";a:3:{s:4:"type";s:7:"primary";s:7:"columns";a:1:{i:0;s:2:"id";}s:6:"length";a:0:{}}s:16:"wishlists_ibfk_1";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:11:"customer_id";}s:10:"references";a:2:{i:0;s:9:"customers";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}s:16:"wishlists_ibfk_2";a:6:{s:4:"type";s:7:"foreign";s:7:"columns";a:1:{i:0;s:10:"product_id";}s:10:"references";a:2:{i:0;s:8:"products";i:1;s:2:"id";}s:6:"update";s:8:"noAction";s:6:"delete";s:8:"noAction";s:6:"length";a:0:{}}}s:11:" * _options";a:2:{s:6:"engine";s:6:"InnoDB";s:9:"collation";s:18:"utf8mb4_unicode_ci";}s:13:" * _temporary";b:0;}}