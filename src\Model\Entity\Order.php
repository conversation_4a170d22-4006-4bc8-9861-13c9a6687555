<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Order Entity
 *
 * @property int $id
 * @property int $customer_id
 * @property int|null $customer_address_id
 * @property int|null $city_id
 * @property int|null $municipality_id
 * @property int|null $offer_id
 * @property string $order_number
 * @property \Cake\I18n\DateTime $order_date
 * @property string $status
 * @property \Cake\I18n\DateTime|null $status_date
 * @property string $payment_method
 * @property int|null $shipping_method_id
 * @property string $subtotal_amount
 * @property string $delivery_charge
 * @property string $discount_amount
 * @property string $offer_amount
 * @property string $total_amount
 * @property string $delivery_mode
 * @property string $order_type
 * @property string|null $order_online_source
 * @property int|null $showroom_id
 * @property int|null $sales_person_id
 * @property int|null $driver_id
 * @property \Cake\I18n\DateTime|null $order_fulfillment_time
 * @property \Cake\I18n\DateTime|null $delivery_date
 * @property string|null $stock_type
 * @property int|null $created_by
 * @property int|null $last_modified_by
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Customer $customer
 * @property \App\Model\Entity\CustomerAddress $customer_address
 * @property \App\Model\Entity\Offer $offer
 * @property \App\Model\Entity\Showroom $showroom
 * @property \App\Model\Entity\OrderItem[] $order_items
 * @property \App\Model\Entity\Return[] $returns
 * @property \App\Model\Entity\Shipment[] $shipments
 * @property \App\Model\Entity\Transaction[] $transactions
 * @property \App\Model\Entity\User $user
 * @property \App\Model\Entity\OrderTrackingHistory[] $order_tracking_histories
 */
class Order extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'customer_id' => true,
        'customer_address_id' => true,
        'country_id' => true,
        'city_id' => true,
        'municipality_id' => true,
        'offer_id' => true,
        'order_number' => true,
        'order_date' => true,
        'status' => true,
        'status_date' => true,
        'payment_method' => true,
        'cheque_photo' => true,
        'shipping_method_id' => true,
        'subtotal_amount' => true,
        'delivery_charge' => true,
        'discount_amount' => true,
        'installation_amount' => true,
        'tax_amount'=> true,
        'offer_amount' => true,
        'total_amount' => true,
        'delivery_mode' => true,
        'delivery_mode_type' => true,
        'order_type' => true,
        'order_online_source' => true,
        'delivery_date' => true,
        'order_notes' => true,
        'stock_type' => true,
        'created_by' => true,
        'last_modified_by' => true,
        'created' => true,
        'modified' => true,
        'customer' => true,
        'customer_address' => true,
        'offer' => true,
        'order_items' => true,
        'returns' => true,
        'shipments' => true,
        'transactions' => true,
        'user' => true,
        'order_tracking_histories' => true,
    ];
}
