 
 <section class="cart-tab-head my-5">
        <div class="container">
            <ul class="nav nav-tabs" id="purchase-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link " id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane"
                        type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                        <div class="d-flex align-items-center ">
                            <div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                            Cart
                        </div>
                    </button>
                    <!-- <hr> -->
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane"
                        type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">
                        <div class="d-flex align-items-center ">
                            <div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                            Checkout
                        </div>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact-tab-pane"
                        type="button" role="tab" aria-controls="contact-tab-pane" aria-selected="false">
                        <div class="d-flex align-items-center ">
                            <div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                            Order Complete
                        </div>
                    </button>
                </li>
            </ul>
        </div>
    </section>
    <section>
        <div class="container">
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab"
                    tabindex="0">
                    <div class="cart-section">
                        <div class="container py-5">
                            <div class="row g-4">

                                <!--  Shipping Details -->
                                <div class="col-lg-8">
                                    <div class="shipping-card">
                                        <h5 class="fw-bold mb-4">Shipping Details</h5>

                                        <?php if ($selectedAddress): ?>
                                            <div class="alert alert-info mb-4">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong>Selected Address:</strong> <?= h($selectedAddress->name) ?> - <?= h($selectedAddress->address_line1) ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Cart', 'action' => 'address']) ?>" class="btn btn-sm btn-outline-primary ms-2">Change Address</a>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Simple Address Card -->
                                        <div class="address-card">
                                            <div class="address-header">
                                                <h6 class="mb-0">Delivery Address</h6>
                                            </div>
                                            <div class="address-content">
                                                <div class="address-line">
                                                    <strong><?= h($customer->first_name ?? '') ?> <?= h($customer->last_name ?? '') ?></strong>
                                                </div>
                                                <div class="address-line">
                                                    <?= h($selectedAddress->address_line1 ?? 'No address selected') ?>
                                                    <?php if (!empty($selectedAddress->address_line2)): ?>
                                                        , <?= h($selectedAddress->address_line2) ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="address-line">
                                                    <?= h($selectedAddress && $selectedAddress->city ? $selectedAddress->city->city_name : '') ?><?php if ($selectedAddress && $selectedAddress->state): ?>, <?= h($selectedAddress->state->state_name) ?><?php endif; ?> <?= h($selectedAddress->zipcode ?? '') ?>
                                                </div>
                                                <div class="address-line"><?= h($selectedAddress && $selectedAddress->country ? $selectedAddress->country->name : '') ?></div>
                                                <?php if (!empty($selectedAddress->phone_no1)): ?>
                                                    <div class="address-line">
                                                        <i class="fas fa-phone me-1"></i><?= h($selectedAddress->phone_no1) ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($customer->email)): ?>
                                                    <div class="address-line">
                                                        <i class="fas fa-envelope me-1"></i><?= h($customer->email) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Order Notes Form -->
                                        <?= $this->Form->create(null, [
                                            'id' => 'checkoutForm',
                                            'class' => 'needs-validation',
                                            'novalidate' => true
                                        ]) ?>

                                        <!-- Hidden fields for order data -->
                                        <?php if ($selectedAddress): ?>
                                            <?= $this->Form->hidden('selected_address_id', ['value' => $selectedAddress->id]) ?>
                                        <?php endif; ?>
                                        <?= $this->Form->hidden('first_name', ['value' => $customer->first_name ?? '']) ?>
                                        <?= $this->Form->hidden('last_name', ['value' => $customer->last_name ?? '']) ?>
                                        <?= $this->Form->hidden('email', ['value' => $customer->email ?? '']) ?>
                                        <?= $this->Form->hidden('phone', ['value' => $selectedAddress->phone_no1 ?? '']) ?>
                                        <?= $this->Form->hidden('address_line1', ['value' => $selectedAddress->address_line1 ?? '']) ?>
                                        <?= $this->Form->hidden('address_line2', ['value' => $selectedAddress->address_line2 ?? '']) ?>
                                        <?= $this->Form->hidden('city', ['value' => $selectedAddress && $selectedAddress->city ? $selectedAddress->city->city_name : '']) ?>
                                        <?= $this->Form->hidden('postcode', ['value' => $selectedAddress->zipcode ?? '']) ?>
                                        <?= $this->Form->hidden('country', ['value' => 'Qatar']) ?>

                                        <div class="row g-3 mt-4">

                                            <!-- Order Notes -->
                                            <div class="col-12">
                                                <label for="orderNotes" class="form-label">ORDER NOTES (OPTIONAL)</label>
                                                <?= $this->Form->control('order_notes', [
                                                    'type' => 'textarea',
                                                    'label' => false,
                                                    'class' => 'form-control',
                                                    'id' => 'orderNotes',
                                                    'rows' => 4,
                                                    'placeholder' => 'Notes about your order, e.g. special notes for delivery.'
                                                ]) ?>
                                            </div>
                                        </div>

                                        <?= $this->Form->end() ?>
                                    </div>
                                </div>

                                <!-- Order Summary -->
                                <div class="col-lg-4">
                                    <div class="order-summary-card">
                                        <h5 class="fw-bold mb-4">Order Summary</h5>

                                        <?php if ($orderSummary['has_items']): ?>
                                            <div class="summary-item">
                                                <span class="summary-label">Subtotal </span>
                                                <span class="summary-value"><?= $this->Price->setPriceFormat($orderSummary['subtotal'])?></span>
                                            </div>

                                            <?php if ($orderSummary['discount_amount'] > 0): ?>
                                                <div class="summary-item">
                                                    <span class="summary-label">Discount</span>
                                                    <span class="summary-value text-success"> <?= $this->Price->setPriceFormat($orderSummary['discount_amount'])?></span>
                                                </div>
                                            <?php endif; ?>

                                            <!-- <div class="summary-item">
                                                <span class="summary-label">Shipping Costs</span>
                                                <span class="summary-value"><?= $this->Price->setPriceFormat($orderSummary['shipping_cost'])?></span>
                                            </div> -->
                                             <?php if (!empty($orderSummary['delivery_charge']) && $orderSummary['delivery_charge'] > 0): ?>
                                            <div class="summary-item">
                                                <span class="summary-label">Delivery Charge</span>
                                                <span class="summary-value"><?= $this->Price->setPriceFormat($orderSummary['delivery_charge'])?></span>
                                            </div>
                                          <?php endif; ?>

                                          <?php if (!empty($orderSummary['installation_total']) && $orderSummary['installation_total'] > 0): ?>
                                            <div class="summary-item">
                                                <span class="summary-label">Installation Charge</span>
                                                <span class="summary-value text-success"><?= $this->Price->setPriceFormat($orderSummary['installation_total'])?></span>
                                            </div>
                                          <?php endif; ?>

                                          <?php if (!empty($orderSummary['tax_amount']) && $orderSummary['tax_amount'] > 0): ?>
                                            <div class="summary-item">
                                                <span class="summary-label">
                                                    VAT (<?= number_format($orderSummary['tax_rate'] * 100, 0) ?>%)
                                                    <small class="text-muted d-block">Saudi Arabia</small>
                                                </span>
                                                <span class="summary-value text-info"><?= $this->Price->setPriceFormat($orderSummary['tax_amount'])?></span>
                                            </div>
                                          <?php endif; ?>

                                            <div class="delivery-info">
                                                <span class="delivery-label">Estimated Delivery by</span>
                                                <span class="delivery-date"><?= $orderSummary['estimated_delivery_date'] ?></span>
                                            </div>

                                            <!-- <div class="coupon-section">
                                                <div class="input-group">
                                                    <input type="text" class="form-control coupon-input" placeholder="Coupon code" id="couponCode" value="<?= h($orderSummary['coupon_code'] ?? '') ?>" <?= $orderSummary['coupon_code'] ? 'readonly' : '' ?>>
                                                    <?php if ($orderSummary['coupon_code']): ?>
                                                        <button class="btn btn-remove-coupon" type="button" onclick="removeCoupon()">Remove Coupon</button>
                                                    <?php else: ?>
                                                        <button class="btn btn-apply-coupon" type="button" onclick="applyCoupon()">Apply Coupon</button>
                                                    <?php endif; ?>
                                                </div>
                                                <div id="couponMessage" class="mt-2" style="display: none;"></div>
                                                <?php if ($orderSummary['coupon_code']): ?>
                                                    <div class="coupon-applied-info mt-2">
                                                        <small class="text-success">
                                                            <i class="fas fa-check-circle me-1"></i>
                                                            Coupon "<?= h($orderSummary['coupon_code']) ?>" applied successfully
                                                        </small>
                                                    </div>
                                                <?php endif; ?>
                                            </div> -->
                                             <?= $this->element('coupon_section', [
                                                'orderSummary' => $orderSummary ?? [],
                                                'availableCoupons' => $availableCoupons ?? [],
                                                'appliedCoupon' => $appliedCoupon ?? null,
                                                'showAvailableCoupons' => true
                                            ]) ?>

                                            <hr>

                                            <div class="terms-section">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="acceptTerms">
                                                    <label class="form-check-label" for="acceptTerms">
                                                       Accept <a href="/terms-condtions" target="_blank">Terms and Conditions</a>
                                                    </label>
                                                </div>
                                            </div>

                                            <button class="btn btn-cod-order w-100" onclick="placeCodOrder()">
                                                <i class="fas fa-money-bill-wave me-2"></i>
                                                Place COD Order | <?= $this->Price->setPriceFormat($orderSummary['final_total'] )?> 
                                            </button>

                                            <div id="codOrderMessage" class="mt-3" style="display: none;"></div>

                                        <?php else: ?>
                                            <div class="empty-cart-message text-center py-4">
                                                <p class="text-muted">Your cart is empty</p>
                                                <a href="<?= $this->Url->build(['controller' => 'home', 'action' => 'home']) ?>" class="btn btn-primary">Continue Shopping</a>
                                            </div>
                                        <?php endif; ?>

                                        <div class="secure-payments">
                                            <p class="secure-text">SECURE PAYMENTS PROVIDED BY</p>
                                            <div class="payment-methods">
                                                <img src="../../img/ozone/mastercard-1.png" alt="Mastercard" class="payment-icon">
                                                <img src="../../img/ozone/mastercard-2.png" alt="Visa" class="payment-icon">
                                                <img src="../../img/ozone/mastercard-3.png" alt="Bitcoin" class="payment-icon">
                                                <img src="../../img/ozone/mastercard-4.png" alt="PayPal" class="payment-icon">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="contact-tab-pane" role="tabpanel" aria-labelledby="contact-tab"
                    tabindex="0">
                    ...
                </div>
                <div class="tab-pane fade" id="disabled-tab-pane" role="tabpanel" aria-labelledby="disabled-tab"
                    tabindex="0">
                    ...</div>
            </div>
        </div>
    </section>

    <style>
    /* Shipping Details Card */
    .shipping-card {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .shipping-card h5 {
        color: #333;
        font-weight: 600;
        margin-bottom: 1.5rem;
    }

    .shipping-card .alert-info {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        color: #0c5460;
        border-radius: 8px;
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .shipping-card .alert-info .btn-outline-primary {
        border-color: #355C3F;
        color: #355C3F;
        font-size: 0.8rem;
        padding: 0.25rem 0.75rem;
    }

    .shipping-card .alert-info .btn-outline-primary:hover {
        background-color: #355C3F;
        border-color: #355C3F;
        color: white;
    }

    .shipping-card .form-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .shipping-card .form-control,
    .shipping-card .form-select {
        border: 1px solid #d1d5db;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: border-color 0.2s ease;
    }

    .shipping-card .form-control:focus,
    .shipping-card .form-select:focus {
        border-color: #355C3F;
        box-shadow: 0 0 0 3px rgba(53, 92, 63, 0.1);
    }

    /* Simple Address Card Styles */
    .address-card {
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .address-header {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .address-header h6 {
        color: #495057;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .address-content {
        padding: 1rem;
    }

    .address-line {
        color: #333;
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 0.25rem;
    }

    .address-line:last-child {
        margin-bottom: 0;
    }

    .address-line i {
        color: #6c757d;
        font-size: 0.8rem;
    }



    /* Order Summary Card */
    .order-summary-card {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        height: fit-content;
    }

    .order-summary-card h5 {
        color: #333;
        font-weight: 600;
        margin-bottom: 1.5rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
    }

    .summary-label {
        color: #666;
        font-size: 0.9rem;
    }

    .summary-value {
        color: #333;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .delivery-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 1.5rem 0;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

    .delivery-label {
        color: #666;
        font-size: 0.9rem;
    }

    .delivery-date {
        color: #ff6b35;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .coupon-section {
        margin: 1.5rem 0;
    }

    .coupon-input {
        border: 1px solid #d1d5db;
        border-radius: 8px 0 0 8px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .btn-apply-coupon {
        background-color: #355C3F;
        color: white;
        border: 1px solid #355C3F;
        border-radius: 0 8px 8px 0;
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        white-space: nowrap;
    }

    .btn-apply-coupon:hover {
        background-color: #2a4730;
        border-color: #2a4730;
        color: white;
    }

    .btn-remove-coupon {
        background-color: #dc3545;
        color: white;
        border: 1px solid #dc3545;
        border-radius: 0 8px 8px 0;
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        white-space: nowrap;
    }

    .btn-remove-coupon:hover {
        background-color: #c82333;
        border-color: #c82333;
        color: white;
    }

    .coupon-applied-info {
        font-size: 0.85rem;
    }

    .coupon-applied-info .text-success {
        color: #28a745 !important;
        font-weight: 500;
    }

    .coupon-input[readonly] {
        background-color: #f8f9fa;
        border-color: #28a745;
    }

    .terms-section {
        margin: 1.5rem 0;
    }

    .terms-section .form-check-label {
        color: #666;
        font-size: 0.9rem;
        margin-left: 0.5rem;
    }

    .btn-pay-now {
        background-color: #355C3F;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        margin: 1.5rem 0;
        transition: background-color 0.2s ease;
    }

    .btn-pay-now:hover {
        background-color: #2a4730;
        color: white;
    }

    .btn-cod-order {
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        margin: 1.5rem 0;
        transition: background-color 0.2s ease;
    }

    .btn-cod-order:hover {
        background-color: #218838;
        color: white;
    }

    .btn-cod-order:disabled {
        background-color: #6c757d;
        border-color: #6c757d;
        opacity: 0.65;
    }

    .secure-payments {
        text-align: center;
        margin-top: 1.5rem;
    }

    .secure-text {
        color: #666;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 1rem;
    }

    .payment-methods {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
    }

    .payment-icon {
        height: 24px;
        width: auto;
        opacity: 0.8;
        transition: opacity 0.2s ease;
    }

    .payment-icon:hover {
        opacity: 1;
    }

    /* Coupon Message Styles */
    #couponMessage {
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Empty Cart Message */
    .empty-cart-message {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        margin: 1rem 0;
    }

    .empty-cart-message .btn-primary {
        background-color: #355C3F;
        border-color: #355C3F;
    }

    .empty-cart-message .btn-primary:hover {
        background-color: #2a4730;
        border-color: #2a4730;
    }

    /* Summary Item Discount Styling */
    .summary-item .text-success {
        font-weight: 600;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .shipping-card,
        .order-summary-card {
            padding: 1.5rem;
        }

        .address-content {
            padding: 0.75rem;
        }

        .address-line {
            font-size: 0.85rem;
        }

        .btn-apply-coupon {
            padding: 0.75rem 1rem;
            font-size: 0.8rem;
        }

        .payment-methods {
            flex-wrap: wrap;
        }
    }

    /* Form Validation Styles */
    .was-validated .form-control:valid,
    .was-validated .form-select:valid {
        border-color: #28a745;
    }

    .was-validated .form-control:invalid,
    .was-validated .form-select:invalid {
        border-color: #dc3545;
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #dc3545;
    }
    </style>

    <script>
    // PHP variables for JavaScript
    const CHECKOUT_CONFIG = {
        placeCodOrderUrl: '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'placeCodOrder']) ?>',
        paymentSuccessUrl: '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'paymentSuccess']) ?>',
        // finalTotal: '<?= h($orderSummary['final_total_formatted'] ?? 'QAR 0.00') ?>',
       finalTotal:' <?= $this->Price->setPriceFormat($orderSummary['final_total'] )?>',
        csrfToken: '<?= $this->request->getAttribute('csrfToken') ?>'
    };

    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const checkoutForm = document.getElementById('checkoutForm');

        if (checkoutForm) {
            checkoutForm.addEventListener('submit', function(event) {
                if (!checkoutForm.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                checkoutForm.classList.add('was-validated');
            });
        }


        // Pay Now button functionality
        const payNowBtn = document.querySelector('.btn-pay-now');
        const acceptTerms = document.getElementById('acceptTerms');

        if (payNowBtn && acceptTerms) {
            payNowBtn.addEventListener('click', function() {
                if (!acceptTerms.checked) {
                    alert('Please accept the terms and conditions to proceed');
                    return;
                }

                // Add your payment processing logic here
                console.log('Processing payment...');
                // You can redirect to payment gateway or process the order
            });
        }
    });

    // Apply Coupon Function
    // function applyCoupon() {
    //     const couponCode = document.getElementById('couponCode').value.trim();
    //     const couponMessage = document.getElementById('couponMessage');
    //     const applyCouponBtn = document.querySelector('.btn-apply-coupon');

    //     if (!couponCode) {
    //         showCouponMessage('Please enter a coupon code', 'error');
    //         return;
    //     }

    //     // Show loading state
    //     applyCouponBtn.disabled = true;
    //     applyCouponBtn.textContent = 'Applying...';

    //     // Make AJAX request to apply coupon
    //     fetch('<?= $this->Url->build(['controller' => 'Cart', 'action' => 'applyCoupon']) ?>', {
    //         method: 'POST',
    //         headers: {
    //             'Content-Type': 'application/json',
    //             'X-Requested-With': 'XMLHttpRequest',
    //             'X-CSRF-Token': document.querySelector('meta[name="csrfToken"]').getAttribute('content')
    //         },
    //         body: JSON.stringify({
    //             coupon_code: couponCode
    //         })
    //     })
    //     .then(response => response.json())
    //     .then(data => {
    //         if (data.success) {
    //             showCouponMessage(data.message, 'success');
    //             // Reload page to update order summary
    //             setTimeout(() => {
    //                 window.location.reload();
    //             }, 1500);
    //         } else {
    //             showCouponMessage(data.message, 'error');
    //         }
    //     })
    //     .catch(error => {
    //         console.error('Error:', error);
    //         showCouponMessage('An error occurred while applying the coupon', 'error');
    //     })
    //     .finally(() => {
    //         // Reset button state
    //         applyCouponBtn.disabled = false;
    //         applyCouponBtn.textContent = 'Apply Coupon';
    //     });
    // }

    // Remove Coupon Function
    // function removeCoupon() {
    //     const removeCouponBtn = document.querySelector('.btn-remove-coupon');

    //     // Show loading state
    //     removeCouponBtn.disabled = true;
    //     removeCouponBtn.textContent = 'Removing...';

    //     // Make AJAX request to remove coupon
    //     fetch('<?= $this->Url->build(['controller' => 'Cart', 'action' => 'removeCoupon']) ?>', {
    //         method: 'POST',
    //         headers: {
    //             'Content-Type': 'application/json',
    //             'X-Requested-With': 'XMLHttpRequest',
    //             'X-CSRF-Token': document.querySelector('meta[name="csrfToken"]').getAttribute('content')
    //         }
    //     })
    //     .then(response => response.json())
    //     .then(data => {
    //         if (data.success) {
    //             showCouponMessage(data.message, 'success');
    //             // Reload page to update order summary
    //             setTimeout(() => {
    //                 window.location.reload();
    //             }, 1500);
    //         } else {
    //             showCouponMessage(data.message, 'error');
    //         }
    //     })
    //     .catch(error => {
    //         console.error('Error:', error);
    //         showCouponMessage('An error occurred while removing the coupon', 'error');
    //     })
    //     .finally(() => {
    //         // Reset button state
    //         removeCouponBtn.disabled = false;
    //         removeCouponBtn.textContent = 'Remove Coupon';
    //     });
    // }

    // Show coupon message
    function showCouponMessage(message, type) {
        const couponMessage = document.getElementById('couponMessage');
        couponMessage.textContent = message;
        couponMessage.className = `mt-2 ${type === 'success' ? 'text-success' : 'text-danger'}`;
        couponMessage.style.display = 'block';

        // Hide message after 5 seconds
        setTimeout(() => {
            couponMessage.style.display = 'none';
        }, 5000);
    }

    // Place COD Order Function
    function placeCodOrder() {
        const acceptTerms = document.getElementById('acceptTerms');
        const codOrderBtn = document.querySelector('.btn-cod-order');
        const checkoutForm = document.getElementById('checkoutForm');

        // Check if terms are accepted
        if (!acceptTerms.checked) {
            showCodOrderMessage('Please accept the terms and conditions to proceed', 'error');
            return;
        }

        // Validate form
        if (!checkoutForm.checkValidity()) {
            checkoutForm.classList.add('was-validated');
            showCodOrderMessage('Please fill in all required fields correctly', 'error');
            return;
        }

        // Get form data
        const formData = new FormData(checkoutForm);
        const orderData = {
            selected_address_id: formData.get('selected_address_id'),
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address_line1: formData.get('address_line1'),
            address_line2: formData.get('address_line2'),
            city: formData.get('city'),
            postcode: formData.get('postcode'),
            country: formData.get('country') || 'Qatar',
            order_notes: formData.get('order_notes')
        };

        // Show loading state
        codOrderBtn.disabled = true;
        codOrderBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Placing Order...';

        // Make AJAX request to place COD order
        fetch(CHECKOUT_CONFIG.placeCodOrderUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': CHECKOUT_CONFIG.csrfToken || document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(orderData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showCodOrderMessage(data.message, 'success');
                // Redirect to payment success page after 2 seconds
                setTimeout(() => {
                    window.location.href = CHECKOUT_CONFIG.paymentSuccessUrl + '/' + data.order_id;
                }, 2000);
            } else {
                showCodOrderMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showCodOrderMessage('An error occurred while placing the order. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            codOrderBtn.disabled = false;
            codOrderBtn.innerHTML = '<i class="fas fa-money-bill-wave me-2"></i>Place COD Order | ' + CHECKOUT_CONFIG.finalTotal;
        });
    }

    // Show COD order message
    function showCodOrderMessage(message, type) {
        const codOrderMessage = document.getElementById('codOrderMessage');
        codOrderMessage.textContent = message;
        codOrderMessage.className = `mt-3 alert ${type === 'success' ? 'alert-success' : 'alert-danger'}`;
        codOrderMessage.style.display = 'block';

        // Hide message after 5 seconds (except for success messages)
        if (type !== 'success') {
            setTimeout(() => {
                codOrderMessage.style.display = 'none';
            }, 5000);
        }
    }
    </script>