<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Role Country Example Controller
 * 
 * This controller demonstrates how to use the role-based country access system
 */
class RoleCountryExampleController extends AppController
{
    protected $Products;
    protected $Orders;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        
        $this->Products = $this->fetchTable('Products');
        $this->Orders = $this->fetchTable('Orders');
    }

    /**
     * Example 1: Products filtered by role-based country access
     */
    public function products()
    {
        $query = $this->Products->find()
            ->contain(['Countries', 'Brands'])
            ->order(['Products.name' => 'ASC']);

        // Apply role-based country filtering
        $query = $this->applyRoleBasedCountryFilter($query, 'Products.country_id');

        $products = $this->paginate($query);
        
        // Get user's accessible countries for display
        $accessibleCountries = $this->getUserAccessibleCountries();
        $accessInfo = $this->getAccessInfo();
        
        $this->set(compact('products', 'accessibleCountries', 'accessInfo'));
    }

    /**
     * Example 2: Orders filtered by role-based country access
     */
    public function orders()
    {
        $query = $this->Orders->find()
            ->contain(['Countries', 'Customers'])
            ->order(['Orders.created' => 'DESC']);

        // Apply role-based country filtering
        $query = $this->applyRoleBasedCountryFilter($query, 'Orders.country_id');

        $orders = $this->paginate($query);
        
        $accessInfo = $this->getAccessInfo();
        
        $this->set(compact('orders', 'accessInfo'));
    }

    /**
     * Example 3: Dashboard with role-based statistics
     */
    public function dashboard()
    {
        $user = $this->Authentication->getIdentity();
        $accessInfo = $this->getAccessInfo();
        
        // Get statistics based on role permissions
        $stats = [];
        
        // Products count
        $productsQuery = $this->Products->find();
        $productsQuery = $this->applyRoleBasedCountryFilter($productsQuery, 'Products.country_id');
        $stats['total_products'] = $productsQuery->count();
        
        // Orders count
        $ordersQuery = $this->Orders->find();
        $ordersQuery = $this->applyRoleBasedCountryFilter($ordersQuery, 'Orders.country_id');
        $stats['total_orders'] = $ordersQuery->count();
        
        // Revenue calculation
        $revenueQuery = $this->Orders->find();
        $revenueQuery = $this->applyRoleBasedCountryFilter($revenueQuery, 'Orders.country_id');
        $stats['total_revenue'] = $revenueQuery->select(['total' => 'SUM(total_amount)'])->first()->total ?? 0;
        
        // Country-specific breakdown
        $accessibleCountries = $this->getUserAccessibleCountries();
        $countryStats = [];
        
        if ($accessibleCountries === null) {
            // User can access all countries
            $allCountries = $this->Countries->getCountriesForDropdown();
            foreach ($allCountries as $countryId => $countryName) {
                $countryStats[$countryName] = $this->getCountryStats($countryId);
            }
        } elseif (is_array($accessibleCountries)) {
            // User can access specific countries
            foreach ($accessibleCountries as $countryId) {
                $country = $this->Countries->getCountryById($countryId);
                if ($country) {
                    $countryStats[$country->name] = $this->getCountryStats($countryId);
                }
            }
        } elseif ($accessibleCountries === 'user_country') {
            // User can access only their assigned country
            $userCountryId = $user->country_id ?? null;
            if ($userCountryId) {
                $country = $this->Countries->getCountryById($userCountryId);
                if ($country) {
                    $countryStats[$country->name] = $this->getCountryStats($userCountryId);
                }
            }
        }
        
        $this->set(compact('stats', 'countryStats', 'accessInfo'));
    }

    /**
     * Example 4: Test country access
     */
    public function testAccess()
    {
        $user = $this->Authentication->getIdentity();
        $allCountries = $this->Countries->getCountriesForDropdown();
        
        $accessResults = [];
        foreach ($allCountries as $countryId => $countryName) {
            $accessResults[$countryName] = [
                'id' => $countryId,
                'can_access' => $this->canUserAccessCountry($countryId),
                'name' => $countryName
            ];
        }
        
        $accessInfo = $this->getAccessInfo();
        
        $this->set(compact('accessResults', 'accessInfo'));
    }

    /**
     * AJAX endpoint to check access to specific country
     */
    public function checkCountryAccess()
    {
        $this->request->allowMethod(['post', 'get']);
        
        $countryId = $this->request->getQuery('country_id');
        $canAccess = $this->canUserAccessCountry($countryId);
        
        $country = null;
        if ($countryId) {
            $country = $this->Countries->getCountryById($countryId);
        }
        
        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'can_access' => $canAccess,
                'country_id' => $countryId,
                'country_name' => $country ? $country->name : null,
                'access_info' => $this->getAccessInfo()
            ]));
    }

    /**
     * Get statistics for a specific country
     *
     * @param int $countryId
     * @return array
     */
    private function getCountryStats($countryId)
    {
        $products = $this->Products->find()
            ->where(['Products.country_id' => $countryId])
            ->count();
            
        $orders = $this->Orders->find()
            ->where(['Orders.country_id' => $countryId])
            ->count();
            
        $revenue = $this->Orders->find()
            ->where(['Orders.country_id' => $countryId])
            ->select(['total' => 'SUM(total_amount)'])
            ->first()->total ?? 0;
        
        return [
            'products' => $products,
            'orders' => $orders,
            'revenue' => $revenue
        ];
    }

    /**
     * Get access information for current user
     *
     * @return array
     */
    private function getAccessInfo()
    {
        $user = $this->Authentication->getIdentity();
        $accessibleCountries = $this->getUserAccessibleCountries();
        
        $accessType = 'unknown';
        $accessDescription = '';
        $countryCount = 0;
        
        if ($accessibleCountries === null) {
            $accessType = 'all';
            $accessDescription = 'Access to all countries';
            $countryCount = $this->Countries->find()->count();
        } elseif ($accessibleCountries === 'user_country') {
            $accessType = 'user_country';
            $accessDescription = 'Access to user\'s assigned country only';
            $countryCount = $user->country_id ? 1 : 0;
        } elseif (is_array($accessibleCountries)) {
            $accessType = 'specific';
            $countryCount = count($accessibleCountries);
            $accessDescription = "Access to {$countryCount} specific countries";
        }
        
        return [
            'type' => $accessType,
            'description' => $accessDescription,
            'country_count' => $countryCount,
            'role_id' => $user->role_id ?? null,
            'user_country_id' => $user->country_id ?? null
        ];
    }
}
