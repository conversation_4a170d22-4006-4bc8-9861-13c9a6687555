<style>
th > a {
    color: #ffffff !important;
}
th > a:hover {
    color: #ffffff !important;
}

.pagination {
    margin: 0;
}

.pagination .page-item .page-link {
    padding: 0.5rem 0.75rem;
    color: #6c757d;
    background-color: #fff;
    border: 1px solid #dee2e6;
    margin: 0 2px;
}

.pagination .page-item.active .page-link {
    color: #fff;
    background-color: #6777ef;
    border-color: #6777ef;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}
</style>
<div class="section-header">
    <ul class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item active">
            <?= __('Contact Enquiries') ?>
        </li>
    </ul>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4><?= __('Contact Enquiries') ?></h4>
        <div class="card-header-form d-flex align-items-center">
            <?= $this->Form->create(null, ['type' => 'get', 'url' => ['controller' => 'ContactEnquiries', 'action' => 'index']]) ?>
                <div class="input-group me-2">
                    <?= $this->Form->control('search', [
                        'label' => false,
                        'class' => 'form-control search-control',
                        'placeholder' => __('Search'),
                        'value' => $this->request->getQuery('search')
                    ]) ?>
                    <div class="input-group-btn">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            <?= $this->Form->end() ?>
        </div>
    </div>

    <div class="card-body">
        <div class="filter-container mb-3 px-4">
            <?= $this->Form->create(null, ['type' => 'get', 'url' => ['controller' => 'ContactEnquiries', 'action' => 'index']]) ?>
                <div class="d-flex">
                    <div class="form-group d-flex align-items-center">
                        <?= $this->Form->control('status', [
                            'label' => false,
                            'class' => 'form-control form-select p-10',
                            'empty' => '-- All Status --',
                            'options' => $statusOptions,
                            'value' => $this->request->getQuery('status')
                        ]) ?>
                    </div>
                    <div class="form-group d-flex align-items-center ms-3">
                        <?= $this->Form->control('inquiry_type', [
                            'label' => false,
                            'class' => 'form-control form-select p-10',
                            'empty' => '-- All Inquiry Types --',
                            'options' => $inquiryTypes,
                            'value' => $this->request->getQuery('inquiry_type')
                        ]) ?>
                    </div>
                    <div class="form-group ms-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-filter" aria-hidden="true"></i> <?= __('Filter') ?>
                        </button>
                        <a href="<?= $this->Url->build(['controller' => 'ContactEnquiries', 'action' => 'index']) ?>" class="btn btn-secondary">
                            <i class="fas fa-redo-alt"></i> <?= __('Reset') ?>
                        </a>
                    </div>
                </div>
            <?= $this->Form->end() ?>
        </div>

        <div class="table-responsive">
            <table class="table table-striped no-footer display nowrap table-hover border" id="tblEnquiries">
                <thead>
                    <tr>
                        <th><?= $this->Paginator->sort('id', __('ID')) ?></th>
                        <th><?= $this->Paginator->sort('name', __('Name')) ?></th>
                        <th><?= $this->Paginator->sort('email', __('Email')) ?></th>
                        <th><?= $this->Paginator->sort('phone', __('Phone')) ?></th>
                        <th><?= $this->Paginator->sort('inquiry_type', __('Inquiry Type')) ?></th>
                        <th><?= $this->Paginator->sort('status', __('Status')) ?></th>
                        <th><?= $this->Paginator->sort('created', __('Date')) ?></th>
                        <th><?= __('Actions') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $start = $this->Paginator->params()['start'] - 1;
                    foreach ($contacts as $k => $contact): ?>
                    <tr>
                        <td><?= $start + $k + 1 ?></td>
                        <td><?= h($contact->name) ?></td>
                        <td><?= h($contact->email) ?></td>
                        <td><?= h($contact->phone) ?></td>
                        <td><?= h($contact->inquiry_type) ?></td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm dropdown-toggle <?= ($contact->status == 'Pending' || empty($contact->status)) ? 'btn-warning' : ($contact->status == 'In-Progress' ? 'btn-info' : 'btn-success') ?>" type="button" id="dropdownMenuButton<?= $contact->id ?>" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?= empty($contact->status) ? 'Pending' : h($contact->status) ?>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton<?= $contact->id ?>">
                                    <li>
                                        <?= $this->Form->create(null, ['url' => ['action' => 'updateStatus', $contact->id], 'class' => 'd-inline']) ?>
                                        <?= $this->Form->hidden('status', ['value' => 'Pending']) ?>
                                        <button type="submit" class="dropdown-item" <?= ($contact->status == 'Pending' || empty($contact->status)) ? 'disabled' : '' ?>>Pending</button>
                                        <?= $this->Form->end() ?>
                                    </li>
                                    <li>
                                        <?= $this->Form->create(null, ['url' => ['action' => 'updateStatus', $contact->id], 'class' => 'd-inline']) ?>
                                        <?= $this->Form->hidden('status', ['value' => 'In-Progress']) ?>
                                        <button type="submit" class="dropdown-item" <?= ($contact->status == 'In-Progress') ? 'disabled' : '' ?>>In-Progress</button>
                                        <?= $this->Form->end() ?>
                                    </li>
                                    <li>
                                        <?= $this->Form->create(null, ['url' => ['action' => 'updateStatus', $contact->id], 'class' => 'd-inline']) ?>
                                        <?= $this->Form->hidden('status', ['value' => 'Complete']) ?>
                                        <button type="submit" class="dropdown-item" <?= ($contact->status == 'Complete') ? 'disabled' : '' ?>>Complete</button>
                                        <?= $this->Form->end() ?>
                                    </li>
                                </ul>
                            </div>
                        </td>
                        <td><?= h($contact->created->format('Y-m-d H:i')) ?></td>
                        <td class="actions">
                            <?= $this->Html->link('<i class="fas fa-eye"></i>', ['action' => 'view', $contact->id], ['class' => '', 'escape' => false, 'title' => 'View']) ?>
                            <?= $this->Form->postLink('<i class="fas fa-trash"></i>', ['action' => 'delete', $contact->id], ['confirm' => __('Are you sure you want to delete # {0}?', $contact->id), 'class' => '', 'escape' => false, 'title' => 'Delete']) ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <!-- Pagination Info Text - Left Side -->
                <div class="text-muted">
                    <?= $this->Paginator->counter('Page {{page}} of {{pages}}, showing {{current}} record(s) out of {{count}} total') ?>
                </div>
                
                <!-- Pagination Buttons - Right Side -->
                <nav>
                    <ul class="pagination mb-0">
                        <?php if ($this->Paginator->hasPrev()): ?>
                            <li class="page-item">
                                <?= $this->Paginator->first('<i class="fas fa-angle-double-left"></i>', [
                                    'escape' => false,
                                    'class' => 'page-link'
                                ]) ?>
                            </li>
                            <li class="page-item">
                                <?= $this->Paginator->prev('<i class="fas fa-angle-left"></i>', [
                                    'escape' => false,
                                    'class' => 'page-link'
                                ]) ?>
                            </li>
                        <?php else: ?>
                            <li class="page-item disabled">
                                <a class="page-link"><i class="fas fa-angle-double-left"></i></a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link"><i class="fas fa-angle-left"></i></a>
                            </li>
                        <?php endif; ?>

                        <?php echo $this->Paginator->numbers([
                            'modulus' => 2,
                            'separator' => '',
                            'class' => 'page-link',
                            'currentClass' => 'active',
                            'templates' => [
                                'number' => '<li class="page-item"><a class="page-link" href="{{url}}">{{text}}</a></li>',
                                'current' => '<li class="page-item active"><a class="page-link" href="">{{text}}</a></li>',
                            ]
                        ]); ?>

                        <?php if ($this->Paginator->hasNext()): ?>
                            <li class="page-item">
                                <?= $this->Paginator->next('<i class="fas fa-angle-right"></i>', [
                                    'escape' => false,
                                    'class' => 'page-link'
                                ]) ?>
                            </li>
                            <li class="page-item">
                                <?= $this->Paginator->last('<i class="fas fa-angle-double-right"></i>', [
                                    'escape' => false,
                                    'class' => 'page-link'
                                ]) ?>
                            </li>
                        <?php else: ?>
                            <li class="page-item disabled">
                                <a class="page-link"><i class="fas fa-angle-right"></i></a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link"><i class="fas fa-angle-double-right"></i></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>