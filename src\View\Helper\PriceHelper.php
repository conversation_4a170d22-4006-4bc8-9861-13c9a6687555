<?php
declare(strict_types=1);

namespace App\View\Helper;

use Cake\View\Helper;
use Cake\ORM\TableRegistry;

class PriceHelper extends Helper
{
    /**
     * Get the correct price from product data (passed as an array).
     *
     * @param array $product Product data including deals and promotions.
     * @return float|null The calculated price.
     */

     public function getOrderCurrency($countryId)
    {
        if (empty($countryId)) {
            return null;
        }

        $CurrenciesTable = TableRegistry::getTableLocator()->get('Currencies');

        $currency = $CurrenciesTable->find()
            ->where(['Currencies.country_id' => $countryId, 'Currencies.status' => 'A'])
            ->first();

        return $currency;
    }

     
     public function getOrderCurrencyInfo($countryId)
    {
        $currency = $this->getOrderCurrency($countryId);

        if (!$currency) {
            // Return default currency info if not found
            return [
                'code' => 'QAR',
                'symbol' => 'QR',
                'name' => 'Qatari Riyal',
                'symbol_position' => 'left',
                'decimal_places' => 2
            ];
        }

        return [
            'code' => $currency->code,
            'symbol' => $currency->symbol,
            'name' => $currency->name,
            'symbol_position' => $currency->symbol_position,
            'decimal_places' => $currency->decimal_places
        ];
    }


      public function formatOrderAmount($amount, $countryId)
    {
        $currencyInfo = $this->getOrderCurrencyInfo($countryId);
        if ($currencyInfo['symbol_position'] === 'right') {
            return number_format((float)$amount, 0). ' ' . $currencyInfo['symbol'];
        } else {
            return   number_format((float)$amount, 0). ' ' . $currencyInfo['code'];
        }
    }
    public function getPrice(array $product): ?float
    {
        // Check if a valid deal is active
        if (
            isset($product['product_deal']) &&
            !empty($product['product_deal']['offer_price']) &&
            $product['product_deal']['status'] === 'A' &&
            date('Y-m-d') >= $product['product_deal']['start_date'] &&
            date('Y-m-d') <= $product['product_deal']['end_date']
        ) {
            return (float) $product['product_deal']['offer_price'];
        }

        // If no active deal, check for promotion price
        if (!empty($product['promotion_price'])) {
            return (float) $product['promotion_price'];
        }

        // Default to null if no price is found
        return null;
    }

    /**
     * Fetch product price directly from the database using product ID.
     *
     * @param int $productId Product ID.
     * @return float|null The calculated price.
     */
    public function getPriceById(int $productId): ?float
    {
        $productsTable = TableRegistry::getTableLocator()->get('Products');

        $query = $productsTable->find();

        $priceExpression = $query->newExpr()->add(
            'CASE
                WHEN ProductDeals.status = "A"
                     AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
                THEN ProductDeals.offer_price
                ELSE Products.promotion_price
            END'
        );

        $product = $productsTable->find()
            ->select([
                'price' => $priceExpression,
            ])
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                [
                    'ProductDeals.product_id = Products.id',
                    'ProductDeals.status' => 'A',
                    'CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date'
                ]
            )
            ->where([
                'Products.id' => $productId,
                'Products.status' => 'A'
            ])
            ->first();

        return $product ? (float) $product->price : null;
    }

    public function setPriceFormat($price)
    {
        $country = $this->getView()->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';
        $currency = (strtolower($country) == 'qatar') ? 'QAR' : 'SAR';
        // return number_format((float)$price, 2) . " " . $currency;
           // return (int)$price . " " . $currency;
            return number_format((float)$price, 0) . " " . $currency;
    }

    public function setPriceFormatJS($price)
    {
        // In js it not support number_format any digit conversion
        $country = $this->getView()->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';
        $currency = (strtolower($country) == 'qatar') ? 'QAR' : 'SAR';
        return $price. " " . $currency;
    }

    /**
     * Calculate tax amount based on country
     * @param float $amount The amount to calculate tax on
     * @return float Tax amount
     */
    public function calculateTax($amount)
    {
        $country = $this->getView()->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';

        // Saudi Arabia has 15% VAT
        if ($country === 'Saudi Arabia') {
            return (float) $amount * 0.15;
        }

        // Qatar and other countries - no tax for now
        return 0.00;
    }

    /**
     * Check if tax should be displayed for current country
     * @return bool
     */
    public function shouldShowTax()
    {
        $country = $this->getView()->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';
        return $country === 'Saudi Arabia';
    }

    /**
     * Get tax rate for current country
     * @return float Tax rate as decimal (0.15 for 15%)
     */
    public function getTaxRate()
    {
        $country = $this->getView()->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';

        if ($country === 'Saudi Arabia') {
            return 0.15; // 15% VAT
        }

        return 0.00; // No tax for other countries
    }

    public function productListCard($name, $price)
    {
        
        $price = $this->setPriceFormatJS($price);        
        return 
        '<div class="col-md-6 col-lg-4 mb-4">
                                <div class="product-card">
                                    <div class="position-relative substract">
                                        <div class="wishlist">
                                            <img src="../../img/ozone/heart.png" alt="heart" class="img-fluid">
                                        </div>
                                        <img src="../../img/ozone/ASGH-product.png" alt="Air Conditioner" class="img-fluid w-100 ">

                                        <div class=" position-absolute">
                                            <div class="d-flex justify-content-between align-items-center">

                                                <div class="badge">10% Offer</div>
                                                <div class="add-cart">
                                                    <span class="bor-top-rig"></span>
                                                    <span class="bor-bot-left"></span>
                                                    <div class="energy-rating">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                                                            <path d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995" stroke="white" stroke-width="2" stroke-linecap="round"></path>
                                                            <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5" stroke="white" stroke-width="2" stroke-linecap="round"></path>
                                                            <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999" stroke="white" stroke-width="2" stroke-linecap="round"></path>
                                                            <circle cx="8.19985" cy="33.4" r="3.6" stroke="white" stroke-width="2"></circle>
                                                            <circle cx="28.0001" cy="33.4" r="3.6" stroke="white" stroke-width="2"></circle>
                                                            <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white" stroke-width="2" stroke-linecap="round"></path>
                                                            <path d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8" stroke="white" stroke-width="2" stroke-linecap="round"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="pt-3">
                                        <h5>'.$name.'</h5>
                                        <div class="rating mb-2">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star-half-alt"></i>
                                            <span class="text-muted ms-2">(4.5)</span>
                                        </div>
                                        <div class="price ">'.$price.'
                                        
                                        </div>
                                        <div class="describtion">
                                            <p>2023 Model&nbsp;without Wifi (UAE I23TCP.NUAE1) (OMAN I23TCP.NGCC2)</p>
                                            <p>2023 Model&nbsp;without Wifi (UAE I23TCP.NUAE1) (OMAN I23TCP.NGCC2)</p>
                                            <p>2023 Model&nbsp;without Wifi (UAE I23TCP.NUAE1) (OMAN I23TCP.NGCC2)</p>
                                        </div>

                                    </div>
                                </div>
                            </div>';
    }
}
