<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddStatusToContacts extends AbstractMigration
{
    /**
     * Change Method.
     *
     * @return void
     */
    public function change(): void
    {
        $table = $this->table('contacts');
        
        // Add status field if it doesn't exist
        if (!$table->hasColumn('status')) {
            $table->addColumn('status', 'string', [
                'default' => 'Pending',
                'limit' => 20,
                'null' => false,
            ]);
        }
        
        $table->update();
        
        // Update existing records to have 'Pending' status
        $this->execute("UPDATE contacts SET status = 'Pending' WHERE status IS NULL OR status = ''");
    }
}