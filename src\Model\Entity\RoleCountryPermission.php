<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * RoleCountryPermission Entity
 *
 * @property int $id
 * @property int $role_id
 * @property int|null $country_id
 * @property bool $can_access
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\Role $role
 * @property \App\Model\Entity\Country $country
 */
class RoleCountryPermission extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'role_id' => true,
        'country_id' => true,
        'can_access' => true,
        'role' => true,
        'country' => true,
    ];
}
