<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Country Filter Example Controller
 * 
 * This controller demonstrates how to use the country filtering system
 * in your admin panel controllers.
 */
class CountryFilterExampleController extends AppController
{
    protected $Orders;
    protected $Customers;
    protected $Showrooms;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        
        // Load tables that might have country_id fields
        $this->Orders = $this->fetchTable('Orders');
        $this->Customers = $this->fetchTable('Customers');
        $this->Showrooms = $this->fetchTable('Showrooms');
    }

    /**
     * Example 1: Filter orders by country
     */
    public function orders()
    {
        $query = $this->Orders->find()
            ->contain(['Customers', 'Countries'])
            ->order(['Orders.created' => 'DESC']);

        // Apply country filter - assuming orders table has country_id field
        $query = $this->applyCountryFilter($query, 'Orders.country_id');

        $orders = $this->paginate($query);
        
        $this->set(compact('orders'));
    }

    /**
     * Example 2: Filter customers by country
     */
    public function customers()
    {
        $query = $this->Customers->find()
            ->contain(['Countries', 'Users'])
            ->order(['Customers.created' => 'DESC']);

        // Apply country filter - assuming customers table has country_id field
        $query = $this->applyCountryFilter($query, 'Customers.country_id');

        $customers = $this->paginate($query);
        
        $this->set(compact('customers'));
    }

    /**
     * Example 3: Filter showrooms by country
     */
    public function showrooms()
    {
        $query = $this->Showrooms->find()
            ->contain(['Countries', 'Cities'])
            ->order(['Showrooms.name' => 'ASC']);

        // Apply country filter
        $query = $this->applyCountryFilter($query, 'Showrooms.country_id');

        $showrooms = $this->paginate($query);
        
        $this->set(compact('showrooms'));
    }

    /**
     * Example 4: Custom filtering with additional conditions
     */
    public function customFilter()
    {
        $query = $this->Orders->find()
            ->contain(['Customers', 'Countries'])
            ->where(['Orders.status' => 'completed'])
            ->order(['Orders.created' => 'DESC']);

        // Get current country filter
        $selectedCountryId = $this->getCurrentCountryFilter();
        
        if ($selectedCountryId) {
            // Apply country filter with custom logic
            $query->where(['Orders.country_id' => $selectedCountryId]);
            
            // You can also add additional country-specific logic here
            $selectedCountry = $this->Countries->getCountryById($selectedCountryId);
            if ($selectedCountry) {
                // Example: Apply different logic based on country
                switch ($selectedCountry->iso_code_2) {
                    case 'QA': // Qatar
                        $query->where(['Orders.delivery_type' => 'local']);
                        break;
                    case 'AE': // UAE
                        $query->where(['Orders.delivery_type' => 'international']);
                        break;
                }
            }
        }

        $orders = $this->paginate($query);
        
        $this->set(compact('orders', 'selectedCountryId'));
    }

    /**
     * Example 5: AJAX endpoint that respects country filter
     */
    public function ajaxData()
    {
        $this->request->allowMethod(['post', 'get']);
        
        $query = $this->Orders->find()
            ->select(['id', 'order_number', 'total_amount', 'status'])
            ->contain(['Countries' => ['fields' => ['id', 'name']]])
            ->order(['Orders.created' => 'DESC']);

        // Apply country filter
        $query = $this->applyCountryFilter($query, 'Orders.country_id');

        $orders = $query->limit(10)->toArray();
        
        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'data' => $orders,
                'country_filter' => $this->getCurrentCountryFilter()
            ]));
    }

    /**
     * Example 6: Dashboard with country-specific statistics
     */
    public function dashboard()
    {
        $selectedCountryId = $this->getCurrentCountryFilter();
        
        // Base queries
        $ordersQuery = $this->Orders->find();
        $customersQuery = $this->Customers->find();
        
        // Apply country filters
        $ordersQuery = $this->applyCountryFilter($ordersQuery, 'Orders.country_id');
        $customersQuery = $this->applyCountryFilter($customersQuery, 'Customers.country_id');
        
        // Get statistics
        $stats = [
            'total_orders' => $ordersQuery->count(),
            'total_customers' => $customersQuery->count(),
            'total_revenue' => $ordersQuery->select(['total' => 'SUM(total_amount)'])->first()->total ?? 0,
            'country_name' => $selectedCountryId ? 
                $this->Countries->getCountryById($selectedCountryId)->name : 
                'All Countries'
        ];
        
        $this->set(compact('stats', 'selectedCountryId'));
    }
}
