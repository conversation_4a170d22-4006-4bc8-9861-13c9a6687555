<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Offer Entity
 *
 * @property int $id
 * @property string $offer_name
 * @property string|null $offer_group
 * @property string $offer_type
 * @property string $redeem_mode
 * @property string $offer_code
 * @property string|null $offer_description
 * @property string|null $web_image
 * @property string|null $mobile_image
 * @property string $discount
 * @property string|null $max_discount
 * @property string|null $max_amt_per_disc_value
 * @property string|null $min_cart_value
 * @property int $free_shipping
 * @property string|null $terms_conditions
 * @property \Cake\I18n\DateTime $offer_start_date
 * @property \Cake\I18n\DateTime $offer_end_date
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\OfferShowroom[] $offer_showrooms
 * @property \App\Model\Entity\OfferCategory[] $offer_categories
 * @property \App\Model\Entity\OfferProduct[] $offer_products
 * @property \App\Model\Entity\Order[] $orders
 * @property \App\Model\Entity\Showroom[] $showrooms
 * @property \App\Model\Entity\Category[] $categories
 * @property \App\Model\Entity\Product[] $products
 */
class Offer extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'country_id' => true,
        'offer_name' => true,
        'offer_group' => true,
        'offer_type' => true,
        'redeem_mode' => true,
        'offer_code' => true,
        'offer_description' => true,
        'web_image' => true,
        'mobile_image' => true,
        'discount' => true,
        'max_discount' => true,
        'max_amt_per_disc_value' => true,
        'min_cart_value' => true,
        'free_shipping' => true,
        'terms_conditions' => true,
        'offer_start_date' => true,
        'offer_end_date' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'offer_showrooms' => true,
        'offer_categories' => true,
        'offer_products' => true,
        'orders' => true,
        'showrooms' => true,
        'categories' => true,
        'products' => true,

    ];


   
}
