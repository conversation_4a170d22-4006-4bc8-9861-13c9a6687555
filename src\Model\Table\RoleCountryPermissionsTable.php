<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * RoleCountryPermissions Model
 *
 * @property \App\Model\Table\RolesTable&\Cake\ORM\Association\BelongsTo $Roles
 * @property \App\Model\Table\CountriesTable&\Cake\ORM\Association\BelongsTo $Countries
 *
 * @method \App\Model\Entity\RoleCountryPermission newEmptyEntity()
 * @method \App\Model\Entity\RoleCountryPermission newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\RoleCountryPermission> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\RoleCountryPermission get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\RoleCountryPermission findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\RoleCountryPermission patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\RoleCountryPermission> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\RoleCountryPermission|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\RoleCountryPermission saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\RoleCountryPermission>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\RoleCountryPermission>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\RoleCountryPermission>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\RoleCountryPermission> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\RoleCountryPermission>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\RoleCountryPermission>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\RoleCountryPermission>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\RoleCountryPermission> deleteManyOrFail(iterable $entities, array $options = [])
 */
class RoleCountryPermissionsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('role_country_permissions');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Roles', [
            'foreignKey' => 'role_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT', // LEFT because country_id can be NULL for "all countries"
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('role_id')
            ->requirePresence('role_id', 'create')
            ->notEmptyString('role_id');

        $validator
            ->integer('country_id')
            ->allowEmptyString('country_id'); // Allow NULL for "all countries"

        $validator
            ->boolean('can_access')
            ->notEmptyString('can_access');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['role_id'], 'Roles'), ['errorField' => 'role_id']);
        $rules->add($rules->existsIn(['country_id'], 'Countries'), ['errorField' => 'country_id']);
        $rules->add($rules->isUnique(['role_id', 'country_id']), ['errorField' => 'country_id']);

        return $rules;
    }

    /**
     * Get countries accessible by a role
     *
     * @param int $roleId
     * @return array
     */
    public function getAccessibleCountries($roleId)
    {
        return $this->find()
            ->contain(['Countries'])
            ->where([
                'role_id' => $roleId,
                'can_access' => true
            ])
            ->toArray();
    }

    /**
     * Check if a role can access a specific country
     *
     * @param int $roleId
     * @param int|null $countryId
     * @return bool
     */
    public function canAccessCountry($roleId, $countryId = null)
    {
        // Check if role has "all countries" permission (country_id = NULL)
        $allCountriesAccess = $this->find()
            ->where([
                'role_id' => $roleId,
                'country_id IS' => null,
                'can_access' => true
            ])
            ->count();

        if ($allCountriesAccess > 0) {
            return true;
        }

        // If no country specified, return false (need specific country)
        if ($countryId === null) {
            return false;
        }

        // Check specific country access
        $specificAccess = $this->find()
            ->where([
                'role_id' => $roleId,
                'country_id' => $countryId,
                'can_access' => true
            ])
            ->count();

        return $specificAccess > 0;
    }

    /**
     * Set country permissions for a role
     *
     * @param int $roleId
     * @param array $countryIds Array of country IDs, or empty array for all countries
     * @param string $accessType 'all', 'specific', or 'user_country'
     * @return bool
     */
    public function setRoleCountryPermissions($roleId, $countryIds = [], $accessType = 'all')
    {
        // Delete existing permissions for this role
        $this->deleteAll(['role_id' => $roleId]);

        if ($accessType === 'all') {
            // Grant access to all countries
            $permission = $this->newEntity([
                'role_id' => $roleId,
                'country_id' => null, // NULL means all countries
                'can_access' => true
            ]);
            return $this->save($permission) !== false;
        } elseif ($accessType === 'specific' && !empty($countryIds)) {
            // Grant access to specific countries
            $permissions = [];
            foreach ($countryIds as $countryId) {
                $permissions[] = $this->newEntity([
                    'role_id' => $roleId,
                    'country_id' => $countryId,
                    'can_access' => true
                ]);
            }
            return $this->saveMany($permissions) !== false;
        }

        // For 'user_country' type, no permissions are set here
        // Access will be determined by user's country_id
        return true;
    }

    /**
     * Get country IDs that a role can access
     *
     * @param int $roleId
     * @return array|null Array of country IDs, or null if all countries
     */
    public function getRoleCountryIds($roleId)
    {
        // Check for "all countries" permission
        $allCountriesAccess = $this->find()
            ->where([
                'role_id' => $roleId,
                'country_id IS' => null,
                'can_access' => true
            ])
            ->count();

        if ($allCountriesAccess > 0) {
            return null; // null means all countries
        }

        // Get specific country IDs
        return $this->find()
            ->select(['country_id'])
            ->where([
                'role_id' => $roleId,
                'can_access' => true,
                'country_id IS NOT' => null
            ])
            ->extract('country_id')
            ->toArray();
    }
}
