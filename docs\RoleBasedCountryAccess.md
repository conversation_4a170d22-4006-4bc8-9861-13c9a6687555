# Role-Based Country Access System

## Overview

This system allows you to control which countries each role can access in the admin panel. Users will only see data from countries their role has permission to access.

## Features

### 🎯 **Three Access Types**

1. **All Countries** - Role can access data from all countries
2. **Specific Countries** - Role can access only selected countries
3. **User's Assigned Country Only** - Role can access only the country assigned to the user

### 🔧 **Database Structure**

#### New Tables Created:
- `role_country_permissions` - Stores country permissions for each role

#### Modified Tables:
- `roles` - Added `country_access_type` field

## Implementation Details

### 1. Database Schema

```sql
-- Role Country Permissions Table
CREATE TABLE role_country_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    country_id INT NULL, -- NULL means all countries
    can_access BOOLEAN DEFAULT TRUE,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_country (role_id, country_id)
);

-- Roles Table Addition
ALTER TABLE roles ADD COLUMN country_access_type ENUM('all', 'specific', 'user_country') DEFAULT 'all';
```

### 2. Access Types Explained

#### All Countries (`all`)
- **Database**: `country_access_type = 'all'`
- **Permissions**: One record with `country_id = NULL`
- **Behavior**: User sees all data regardless of country
- **Use Case**: Super admin, global managers

#### Specific Countries (`specific`)
- **Database**: `country_access_type = 'specific'`
- **Permissions**: Multiple records with specific `country_id` values
- **Behavior**: User sees only data from selected countries
- **Use Case**: Regional managers, country-specific roles

#### User's Assigned Country (`user_country`)
- **Database**: `country_access_type = 'user_country'`
- **Permissions**: No specific records needed
- **Behavior**: User sees only data from their assigned country (from `users.country_id`)
- **Use Case**: Local staff, country-specific employees

## Usage Examples

### 1. Creating a Qatar-Only Role

```php
// In RolesController or programmatically
$roleId = 5; // Qatar Manager role
$this->Roles->setCountryAccess($roleId, 'specific', [1]); // 1 = Qatar country ID
```

### 2. Creating a Multi-Country Regional Role

```php
// Gulf Region Manager - Qatar, UAE, Kuwait
$roleId = 6;
$gulfCountries = [1, 2, 3]; // Qatar, UAE, Kuwait IDs
$this->Roles->setCountryAccess($roleId, 'specific', $gulfCountries);
```

### 3. Creating a User-Specific Role

```php
// Local Staff - access only their assigned country
$roleId = 7;
$this->Roles->setCountryAccess($roleId, 'user_country', []);
```

## Controller Integration

### 1. Basic Country Filtering

```php
// In any controller
public function index()
{
    $query = $this->YourModel->find();
    
    // Apply role-based country filtering
    $query = $this->applyRoleBasedCountryFilter($query, 'YourModel.country_id');
    
    $data = $this->paginate($query);
    $this->set(compact('data'));
}
```

### 2. Check User Access

```php
// Check if current user can access a specific country
if ($this->canUserAccessCountry($countryId)) {
    // User has access
    $this->processCountryData($countryId);
} else {
    // Access denied
    $this->Flash->error(__('You do not have access to this country.'));
}
```

### 3. Get Accessible Countries

```php
// Get countries user can access
$accessibleCountries = $this->getUserAccessibleCountries();

if ($accessibleCountries === null) {
    // User can access all countries
} elseif ($accessibleCountries === 'user_country') {
    // User can access only their assigned country
} elseif (is_array($accessibleCountries)) {
    // User can access specific countries
    foreach ($accessibleCountries as $countryId) {
        // Process each accessible country
    }
}
```

## Frontend Integration

### 1. Country Dropdown Filtering

The header country dropdown automatically shows only countries the user's role can access:

```php
// In templates/element/header.php
// Dropdown automatically filtered based on role permissions
<?php foreach ($countries as $countryId => $countryName): ?>
    <!-- Only shows accessible countries -->
<?php endforeach; ?>
```

### 2. Role Management Interface

The role add/edit forms now include country permissions:

- **Access Type Selection**: Radio buttons for all/specific/user_country
- **Country Selection**: Checkboxes for specific countries (shown only for 'specific' type)
- **Validation**: Ensures at least one country is selected for 'specific' type

## API Methods

### AppController Methods

```php
// Get countries accessible by current user's role
protected function getUserAccessibleCountries()

// Check if current user can access a specific country
protected function canUserAccessCountry($countryId = null)

// Get filtered countries dropdown
protected function getFilteredCountriesDropdown()

// Apply role-based country filter to query
protected function applyRoleBasedCountryFilter($query, $countryField = 'country_id')
```

### RolesTable Methods

```php
// Get countries accessible by a role
public function getAccessibleCountries($roleId)

// Check if a role can access a specific country
public function canAccessCountry($roleId, $countryId = null, $userCountryId = null)

// Set country access for a role
public function setCountryAccess($roleId, $accessType, $countryIds = [])
```

### RoleCountryPermissionsTable Methods

```php
// Get countries accessible by a role
public function getAccessibleCountries($roleId)

// Check if a role can access a specific country
public function canAccessCountry($roleId, $countryId = null)

// Set country permissions for a role
public function setRoleCountryPermissions($roleId, $countryIds = [], $accessType = 'all')

// Get country IDs that a role can access
public function getRoleCountryIds($roleId)
```

## Security Features

### 1. Automatic Validation
- Invalid country selections are automatically cleared from session
- Users cannot access countries not permitted by their role
- Form submissions validate country permissions

### 2. Query-Level Filtering
- All database queries automatically respect role permissions
- No data leakage between countries
- Consistent filtering across all controllers

### 3. Session Protection
- Country filter session is validated against role permissions
- Automatic cleanup of invalid selections
- Secure session management

## Migration and Setup

### 1. Run Migration
```bash
bin/cake migrations migrate
```

### 2. Set Default Permissions
```php
// Set all existing roles to "all countries" access
$roles = $this->Roles->find()->toArray();
foreach ($roles as $role) {
    $this->Roles->setCountryAccess($role->id, 'all', []);
}
```

### 3. Configure Specific Roles
Use the admin interface to configure country permissions for each role.

## Testing

### 1. Test Role Creation
1. Create a new role with specific country access
2. Assign user to the role
3. Login as that user
4. Verify only permitted countries appear in dropdown

### 2. Test Data Filtering
1. Create products in different countries
2. Login as user with limited country access
3. Verify only permitted country data is visible

### 3. Test Permission Changes
1. Change role's country permissions
2. Verify user's access is immediately updated
3. Test session validation and cleanup

## Troubleshooting

### Country Dropdown Empty
- Check if role has any country permissions set
- Verify user has a valid role assigned
- Check if countries exist in database

### Data Not Filtering
- Ensure `applyRoleBasedCountryFilter()` is called in controller
- Verify table has `country_id` field
- Check role permissions are properly set

### Permission Changes Not Applied
- Clear user sessions after role changes
- Verify migration ran successfully
- Check foreign key constraints

This system provides comprehensive role-based country access control while maintaining security and ease of use.
